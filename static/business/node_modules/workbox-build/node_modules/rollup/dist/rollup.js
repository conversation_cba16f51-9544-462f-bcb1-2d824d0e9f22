/*
  @license
	Rollup.js v2.79.2
	Thu, 26 Sep 2024 18:44:14 GMT - commit 48aef33cf2f2a6dfb175afb3bcd6a977c81f1d5c

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
'use strict';

Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: 'Module' } });

const rollup = require('./shared/rollup.js');
require('path');
require('process');
require('perf_hooks');
require('crypto');
require('fs');
require('events');



exports.VERSION = rollup.version;
exports.defineConfig = rollup.defineConfig;
exports.rollup = rollup.rollup;
exports.watch = rollup.watch;
//# sourceMappingURL=rollup.js.map
