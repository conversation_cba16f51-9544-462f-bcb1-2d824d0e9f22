/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/

/*! Conditions:: INITIAL */

/*! Production::    css_value : ANGLE */

/*! Production::    css_value : CHS */

/*! Production::    css_value : EMS */

/*! Production::    css_value : EXS */

/*! Production::    css_value : FREQ */

/*! Production::    css_value : LENGTH */

/*! Production::    css_value : PERCENTAGE */

/*! Production::    css_value : REMS */

/*! Production::    css_value : RES */

/*! Production::    css_value : SUB css_value */

/*! Production::    css_value : TIME */

/*! Production::    css_value : VHS */

/*! Production::    css_value : VMAXS */

/*! Production::    css_value : VMINS */

/*! Production::    css_value : VWS */

/*! Production::    css_variable : CSS_VAR LPAREN CSS_CPROP COMMA math_expression RPAREN */

/*! Production::    css_variable : CSS_VAR LPAREN CSS_CPROP RPAREN */

/*! Production::    expression : math_expression EOF */

/*! Production::    math_expression : LPAREN math_expression RPAREN */

/*! Production::    math_expression : NESTED_CALC LPAREN math_expression RPAREN */

/*! Production::    math_expression : SUB PREFIX SUB NESTED_CALC LPAREN math_expression RPAREN */

/*! Production::    math_expression : css_value */

/*! Production::    math_expression : css_variable */

/*! Production::    math_expression : math_expression ADD math_expression */

/*! Production::    math_expression : math_expression DIV math_expression */

/*! Production::    math_expression : math_expression MUL math_expression */

/*! Production::    math_expression : math_expression SUB math_expression */

/*! Production::    math_expression : value */

/*! Production::    value : NUMBER */

/*! Production::    value : SUB NUMBER */

/*! Rule::       $ */

/*! Rule::       (--[0-9a-z-A-Z-]*) */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)% */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)Hz\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)ch\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)cm\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)deg\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)dpcm\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)dpi\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)dppx\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)em\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)ex\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)grad\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)in\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)kHz\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)mm\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)ms\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)pc\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)pt\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)px\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)rad\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)rem\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)s\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)turn\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)vh\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)vmax\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)vmin\b */

/*! Rule::       ([0-9]+(\.[0-9]*)?|\.[0-9]+)vw\b */

/*! Rule::       ([a-z]+) */

/*! Rule::       (calc) */

/*! Rule::       (var) */

/*! Rule::       , */

/*! Rule::       - */

/*! Rule::       \( */

/*! Rule::       \) */

/*! Rule::       \* */

/*! Rule::       \+ */

/*! Rule::       \/ */

/*! decimal.js-light v2.5.1 https://github.com/MikeMcl/decimal.js-light/LICENCE */

/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
