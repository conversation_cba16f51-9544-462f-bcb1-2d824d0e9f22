/*! For license information please see Recharts.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("react"),require("prop-types"),require("react-dom")):"function"==typeof define&&define.amd?define(["react","prop-types","react-dom"],e):"object"==typeof exports?exports.Recharts=e(require("react"),require("prop-types"),require("react-dom")):t.Recharts=e(t.<PERSON>act,t.PropTypes,t.ReactDOM)}(this,((t,e,r)=>(()=>{var n={4184:(t,e)=>{var r;!function(){"use strict";var n={}.hasOwnProperty;function i(){for(var t=[],e=0;e<arguments.length;e++){var r=arguments[e];if(r){var o=typeof r;if("string"===o||"number"===o)t.push(r);else if(Array.isArray(r)){if(r.length){var a=i.apply(null,r);a&&t.push(a)}}else if("object"===o){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){t.push(r.toString());continue}for(var c in r)n.call(r,c)&&r[c]&&t.push(c)}}}return t.join(" ")}t.exports?(i.default=i,t.exports=i):void 0===(r=function(){return i}.apply(e,[]))||(t.exports=r)}()},3258:t=>{var e={px:{px:1,cm:96/2.54,mm:96/25.4,in:96,pt:96/72,pc:16},cm:{px:2.54/96,cm:1,mm:.1,in:2.54,pt:2.54/72,pc:2.54/6},mm:{px:25.4/96,cm:10,mm:1,in:25.4,pt:25.4/72,pc:25.4/6},in:{px:1/96,cm:1/2.54,mm:1/25.4,in:1,pt:1/72,pc:1/6},pt:{px:.75,cm:72/2.54,mm:72/25.4,in:72,pt:1,pc:12},pc:{px:6/96,cm:6/2.54,mm:6/25.4,in:6,pt:6/72,pc:1},deg:{deg:1,grad:.9,rad:180/Math.PI,turn:360},grad:{deg:400/360,grad:1,rad:200/Math.PI,turn:400},rad:{deg:Math.PI/180,grad:Math.PI/200,rad:1,turn:2*Math.PI},turn:{deg:1/360,grad:1/400,rad:.5/Math.PI,turn:1},s:{s:1,ms:.001},ms:{s:1e3,ms:1},Hz:{Hz:1,kHz:1e3},kHz:{Hz:.001,kHz:1},dpi:{dpi:1,dpcm:1/2.54,dppx:1/96},dpcm:{dpi:2.54,dpcm:1,dppx:2.54/96},dppx:{dpi:96,dpcm:96/2.54,dppx:1}};t.exports=function(t,r,n,i){if(!e.hasOwnProperty(n))throw new Error("Cannot convert to "+n);if(!e[n].hasOwnProperty(r))throw new Error("Cannot convert from "+r+" to "+n);var o=e[n][r]*t;return!1!==i?(i=Math.pow(10,parseInt(i)||5),Math.round(o*i)/i):o}},9887:function(t,e,r){var n;!function(i){"use strict";var o,a=1e9,c={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,s="[DecimalError] ",l=s+"Invalid argument: ",f=s+"Exponent out of range: ",p=Math.floor,h=Math.pow,d=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,y=1e7,v=9007199254740991,m=p(1286742750677284.5),g={};function b(t,e){var r,n,i,o,a,c,s,l,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),u?_(e,p):e;if(s=t.d,l=e.d,a=t.e,i=e.e,s=s.slice(),o=a-i){for(o<0?(n=s,o=-o,c=l.length):(n=l,i=a,c=s.length),o>(c=(a=Math.ceil(p/7))>c?a+1:c+1)&&(o=c,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((c=s.length)-(o=l.length)<0&&(o=c,n=l,l=s,s=n),r=0;o;)r=(s[--o]=s[o]+l[o]+r)/y|0,s[o]%=y;for(r&&(s.unshift(r),++i),c=s.length;0==s[--c];)s.pop();return e.d=s,e.e=i,u?_(e,p):e}function x(t,e,r){if(t!==~~t||t<e||t>r)throw Error(l+t)}function w(t){var e,r,n,i=t.length-1,o="",a=t[0];if(i>0){for(o+=a,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(o+=E(r)),o+=n;(r=7-(n=(a=t[e])+"").length)&&(o+=E(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}g.absoluteValue=g.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},g.comparedTo=g.cmp=function(t){var e,r,n,i,o=this;if(t=new o.constructor(t),o.s!==t.s)return o.s||-t.s;if(o.e!==t.e)return o.e>t.e^o.s<0?1:-1;for(e=0,r=(n=o.d.length)<(i=t.d.length)?n:i;e<r;++e)if(o.d[e]!==t.d[e])return o.d[e]>t.d[e]^o.s<0?1:-1;return n===i?0:n>i^o.s<0?1:-1},g.decimalPlaces=g.dp=function(){var t=this,e=t.d.length-1,r=7*(e-t.e);if(e=t.d[e])for(;e%10==0;e/=10)r--;return r<0?0:r},g.dividedBy=g.div=function(t){return O(this,new this.constructor(t))},g.dividedToIntegerBy=g.idiv=function(t){var e=this.constructor;return _(O(this,new e(t),0,1),e.precision)},g.equals=g.eq=function(t){return!this.cmp(t)},g.exponent=function(){return A(this)},g.greaterThan=g.gt=function(t){return this.cmp(t)>0},g.greaterThanOrEqualTo=g.gte=function(t){return this.cmp(t)>=0},g.isInteger=g.isint=function(){return this.e>this.d.length-2},g.isNegative=g.isneg=function(){return this.s<0},g.isPositive=g.ispos=function(){return this.s>0},g.isZero=function(){return 0===this.s},g.lessThan=g.lt=function(t){return this.cmp(t)<0},g.lessThanOrEqualTo=g.lte=function(t){return this.cmp(t)<1},g.logarithm=g.log=function(t){var e,r=this,n=r.constructor,i=n.precision,a=i+5;if(void 0===t)t=new n(10);else if((t=new n(t)).s<1||t.eq(o))throw Error(s+"NaN");if(r.s<1)throw Error(s+(r.s?"NaN":"-Infinity"));return r.eq(o)?new n(0):(u=!1,e=O(k(r,a),k(t,a),a),u=!0,_(e,i))},g.minus=g.sub=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?M(e,t):b(e,(t.s=-t.s,t))},g.modulo=g.mod=function(t){var e,r=this,n=r.constructor,i=n.precision;if(!(t=new n(t)).s)throw Error(s+"NaN");return r.s?(u=!1,e=O(r,t,0,1).times(t),u=!0,r.minus(e)):_(new n(r),i)},g.naturalExponential=g.exp=function(){return S(this)},g.naturalLogarithm=g.ln=function(){return k(this)},g.negated=g.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},g.plus=g.add=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?b(e,t):M(e,(t.s=-t.s,t))},g.precision=g.sd=function(t){var e,r,n,i=this;if(void 0!==t&&t!==!!t&&1!==t&&0!==t)throw Error(l+t);if(e=A(i)+1,r=7*(n=i.d.length-1)+1,n=i.d[n]){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},g.squareRoot=g.sqrt=function(){var t,e,r,n,i,o,a,c=this,l=c.constructor;if(c.s<1){if(!c.s)return new l(0);throw Error(s+"NaN")}for(t=A(c),u=!1,0==(i=Math.sqrt(+c))||i==1/0?(((e=w(c.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=p((t+1)/2)-(t<0||t%2),n=new l(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new l(i.toString()),i=a=(r=l.precision)+3;;)if(n=(o=n).plus(O(c,o,a+2)).times(.5),w(o.d).slice(0,a)===(e=w(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),i==a&&"4999"==e){if(_(o,r+1,0),o.times(o).eq(c)){n=o;break}}else if("9999"!=e)break;a+=4}return u=!0,_(n,r)},g.times=g.mul=function(t){var e,r,n,i,o,a,c,s,l,f=this,p=f.constructor,h=f.d,d=(t=new p(t)).d;if(!f.s||!t.s)return new p(0);for(t.s*=f.s,r=f.e+t.e,(s=h.length)<(l=d.length)&&(o=h,h=d,d=o,a=s,s=l,l=a),o=[],n=a=s+l;n--;)o.push(0);for(n=l;--n>=0;){for(e=0,i=s+n;i>n;)c=o[i]+d[n]*h[i-n-1]+e,o[i--]=c%y|0,e=c/y|0;o[i]=(o[i]+e)%y|0}for(;!o[--a];)o.pop();return e?++r:o.shift(),t.d=o,t.e=r,u?_(t,p.precision):t},g.toDecimalPlaces=g.todp=function(t,e){var r=this,n=r.constructor;return r=new n(r),void 0===t?r:(x(t,0,a),void 0===e?e=n.rounding:x(e,0,8),_(r,t+A(r)+1,e))},g.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=T(n,!0):(x(t,0,a),void 0===e?e=i.rounding:x(e,0,8),r=T(n=_(new i(n),t+1,e),!0,t+1)),r},g.toFixed=function(t,e){var r,n,i=this,o=i.constructor;return void 0===t?T(i):(x(t,0,a),void 0===e?e=o.rounding:x(e,0,8),r=T((n=_(new o(i),t+A(i)+1,e)).abs(),!1,t+A(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)},g.toInteger=g.toint=function(){var t=this,e=t.constructor;return _(new e(t),A(t)+1,e.rounding)},g.toNumber=function(){return+this},g.toPower=g.pow=function(t){var e,r,n,i,a,c,l=this,f=l.constructor,h=+(t=new f(t));if(!t.s)return new f(o);if(!(l=new f(l)).s){if(t.s<1)throw Error(s+"Infinity");return l}if(l.eq(o))return l;if(n=f.precision,t.eq(o))return _(l,n);if(c=(e=t.e)>=(r=t.d.length-1),a=l.s,c){if((r=h<0?-h:h)<=v){for(i=new f(o),e=Math.ceil(n/7+4),u=!1;r%2&&C((i=i.times(l)).d,e),0!==(r=p(r/2));)C((l=l.times(l)).d,e);return u=!0,t.s<0?new f(o).div(i):_(i,n)}}else if(a<0)throw Error(s+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,l.s=1,u=!1,i=t.times(k(l,n+12)),u=!0,(i=S(i)).s=a,i},g.toPrecision=function(t,e){var r,n,i=this,o=i.constructor;return void 0===t?n=T(i,(r=A(i))<=o.toExpNeg||r>=o.toExpPos):(x(t,1,a),void 0===e?e=o.rounding:x(e,0,8),n=T(i=_(new o(i),t,e),t<=(r=A(i))||r<=o.toExpNeg,t)),n},g.toSignificantDigits=g.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(x(t,1,a),void 0===e?e=r.rounding:x(e,0,8)),_(new r(this),t,e)},g.toString=g.valueOf=g.val=g.toJSON=function(){var t=this,e=A(t),r=t.constructor;return T(t,e<=r.toExpNeg||e>=r.toExpPos)};var O=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%y|0,n=r/y|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=n*y+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,o,a){var c,u,l,f,p,h,d,v,m,g,b,x,w,O,S,j,E,k,P=n.constructor,M=n.s==i.s?1:-1,T=n.d,C=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(s+"Division by zero");for(u=n.e-i.e,E=C.length,S=T.length,v=(d=new P(M)).d=[],l=0;C[l]==(T[l]||0);)++l;if(C[l]>(T[l]||0)&&--u,(x=null==o?o=P.precision:a?o+(A(n)-A(i))+1:o)<0)return new P(0);if(x=x/7+2|0,l=0,1==E)for(f=0,C=C[0],x++;(l<S||f)&&x--;l++)w=f*y+(T[l]||0),v[l]=w/C|0,f=w%C|0;else{for((f=y/(C[0]+1)|0)>1&&(C=t(C,f),T=t(T,f),E=C.length,S=T.length),O=E,g=(m=T.slice(0,E)).length;g<E;)m[g++]=0;(k=C.slice()).unshift(0),j=C[0],C[1]>=y/2&&++j;do{f=0,(c=e(C,m,E,g))<0?(b=m[0],E!=g&&(b=b*y+(m[1]||0)),(f=b/j|0)>1?(f>=y&&(f=y-1),1==(c=e(p=t(C,f),m,h=p.length,g=m.length))&&(f--,r(p,E<h?k:C,h))):(0==f&&(c=f=1),p=C.slice()),(h=p.length)<g&&p.unshift(0),r(m,p,g),-1==c&&(c=e(C,m,E,g=m.length))<1&&(f++,r(m,E<g?k:C,g)),g=m.length):0===c&&(f++,m=[0]),v[l++]=f,c&&m[0]?m[g++]=T[O]||0:(m=[T[O]],g=1)}while((O++<S||void 0!==m[0])&&x--)}return v[0]||v.shift(),d.e=u,_(d,a?o+A(d)+1:o)}}();function S(t,e){var r,n,i,a,c,s=0,l=0,p=t.constructor,d=p.precision;if(A(t)>16)throw Error(f+A(t));if(!t.s)return new p(o);for(null==e?(u=!1,c=d):c=e,a=new p(.03125);t.abs().gte(.1);)t=t.times(a),l+=5;for(c+=Math.log(h(2,l))/Math.LN10*2+5|0,r=n=i=new p(o),p.precision=c;;){if(n=_(n.times(t),c),r=r.times(++s),w((a=i.plus(O(n,r,c))).d).slice(0,c)===w(i.d).slice(0,c)){for(;l--;)i=_(i.times(i),c);return p.precision=d,null==e?(u=!0,_(i,d)):i}i=a}}function A(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function j(t,e,r){if(e>t.LN10.sd())throw u=!0,r&&(t.precision=r),Error(s+"LN10 precision limit exceeded");return _(new t(t.LN10),e)}function E(t){for(var e="";t--;)e+="0";return e}function k(t,e){var r,n,i,a,c,l,f,p,h,d=1,y=t,v=y.d,m=y.constructor,g=m.precision;if(y.s<1)throw Error(s+(y.s?"NaN":"-Infinity"));if(y.eq(o))return new m(0);if(null==e?(u=!1,p=g):p=e,y.eq(10))return null==e&&(u=!0),j(m,p);if(p+=10,m.precision=p,n=(r=w(v)).charAt(0),a=A(y),!(Math.abs(a)<15e14))return f=j(m,p+2,g).times(a+""),y=k(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=g,null==e?(u=!0,_(y,g)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=w((y=y.times(t)).d)).charAt(0),d++;for(a=A(y),n>1?(y=new m("0."+r),a++):y=new m(n+"."+r.slice(1)),l=c=y=O(y.minus(o),y.plus(o),p),h=_(y.times(y),p),i=3;;){if(c=_(c.times(h),p),w((f=l.plus(O(c,new m(i),p))).d).slice(0,p)===w(l.d).slice(0,p))return l=l.times(2),0!==a&&(l=l.plus(j(m,p+2,g).times(a+""))),l=O(l,new m(d),p),m.precision=g,null==e?(u=!0,_(l,g)):l;l=f,i+=2}}function P(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,r=r-n-1,t.e=p(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),u&&(t.e>m||t.e<-m))throw Error(f+r)}else t.s=0,t.e=0,t.d=[0];return t}function _(t,e,r){var n,i,o,a,c,s,l,d,v=t.d;for(a=1,o=v[0];o>=10;o/=10)a++;if((n=e-a)<0)n+=7,i=e,l=v[d=0];else{if((d=Math.ceil((n+1)/7))>=(o=v.length))return t;for(l=o=v[d],a=1;o>=10;o/=10)a++;i=(n%=7)-7+a}if(void 0!==r&&(c=l/(o=h(10,a-i-1))%10|0,s=e<0||void 0!==v[d+1]||l%o,s=r<4?(c||s)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||s||6==r&&(n>0?i>0?l/h(10,a-i):0:v[d-1])%10&1||r==(t.s<0?8:7))),e<1||!v[0])return s?(o=A(t),v.length=1,e=e-o-1,v[0]=h(10,(7-e%7)%7),t.e=p(-e/7)||0):(v.length=1,v[0]=t.e=t.s=0),t;if(0==n?(v.length=d,o=1,d--):(v.length=d+1,o=h(10,7-n),v[d]=i>0?(l/h(10,a-i)%h(10,i)|0)*o:0),s)for(;;){if(0==d){(v[0]+=o)==y&&(v[0]=1,++t.e);break}if(v[d]+=o,v[d]!=y)break;v[d--]=0,o=1}for(n=v.length;0===v[--n];)v.pop();if(u&&(t.e>m||t.e<-m))throw Error(f+A(t));return t}function M(t,e){var r,n,i,o,a,c,s,l,f,p,h=t.constructor,d=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),u?_(e,d):e;if(s=t.d,p=e.d,n=e.e,l=t.e,s=s.slice(),a=l-n){for((f=a<0)?(r=s,a=-a,c=p.length):(r=p,n=l,c=s.length),a>(i=Math.max(Math.ceil(d/7),c)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((f=(i=s.length)<(c=p.length))&&(c=i),i=0;i<c;i++)if(s[i]!=p[i]){f=s[i]<p[i];break}a=0}for(f&&(r=s,s=p,p=r,e.s=-e.s),c=s.length,i=p.length-c;i>0;--i)s[c++]=0;for(i=p.length;i>a;){if(s[--i]<p[i]){for(o=i;o&&0===s[--o];)s[o]=y-1;--s[o],s[i]+=y}s[i]-=p[i]}for(;0===s[--c];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(e.d=s,e.e=n,u?_(e,d):e):new h(0)}function T(t,e,r){var n,i=A(t),o=w(t.d),a=o.length;return e?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+E(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+E(-i-1)+o,r&&(n=r-a)>0&&(o+=E(n))):i>=a?(o+=E(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+E(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=E(n))),t.s<0?"-"+o:o}function C(t,e){if(t.length>e)return t.length=e,!0}function I(t){if(!t||"object"!=typeof t)throw Error(s+"Object expected");var e,r,n,i=["precision",1,a,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]])){if(!(p(n)===n&&n>=i[e+1]&&n<=i[e+2]))throw Error(l+r+": "+n);this[r]=n}if(void 0!==(n=t[r="LN10"])){if(n!=Math.LN10)throw Error(l+r+": "+n);this[r]=new this(n)}return this}c=function t(e){var r,n,i;function o(t){var e=this;if(!(e instanceof o))return new o(t);if(e.constructor=o,t instanceof o)return e.s=t.s,e.e=t.e,void(e.d=(t=t.d)?t.slice():t);if("number"==typeof t){if(0*t!=0)throw Error(l+t);if(t>0)e.s=1;else{if(!(t<0))return e.s=0,e.e=0,void(e.d=[0]);t=-t,e.s=-1}return t===~~t&&t<1e7?(e.e=0,void(e.d=[t])):P(e,t.toString())}if("string"!=typeof t)throw Error(l+t);if(45===t.charCodeAt(0)?(t=t.slice(1),e.s=-1):e.s=1,!d.test(t))throw Error(l+t);P(e,t)}if(o.prototype=g,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=t,o.config=o.set=I,void 0===e&&(e={}),e)for(i=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return o.config(e),o}(c),c.default=c.Decimal=c,o=new c(1),void 0===(n=function(){return c}.call(e,r,e,t))||(t.exports=n)}()},8141:(t,e,r)=>{"use strict";var n=r(4836);e.__esModule=!0,e.default=function(t,e){t.classList?t.classList.add(e):(0,i.default)(t,e)||("string"==typeof t.className?t.className=t.className+" "+e:t.setAttribute("class",(t.className&&t.className.baseVal||"")+" "+e))};var i=n(r(404));t.exports=e.default},404:(t,e)=>{"use strict";e.__esModule=!0,e.default=function(t,e){return t.classList?!!e&&t.classList.contains(e):-1!==(" "+(t.className.baseVal||t.className)+" ").indexOf(" "+e+" ")},t.exports=e.default},602:t=>{"use strict";function e(t,e){return t.replace(new RegExp("(^|\\s)"+e+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}t.exports=function(t,r){t.classList?t.classList.remove(r):"string"==typeof t.className?t.className=e(t.className,r):t.setAttribute("class",e(t.className&&t.className.baseVal||"",r))}},6729:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,n,o,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var c=new i(n,o||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=new Array(o);i<o;i++)a[i]=n[i].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,i,o,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,s,l=this._events[c],f=arguments.length;if(l.fn){switch(l.once&&this.removeListener(t,l.fn,void 0,!0),f){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,e),!0;case 3:return l.fn.call(l.context,e,n),!0;case 4:return l.fn.call(l.context,e,n,i),!0;case 5:return l.fn.call(l.context,e,n,i,o),!0;case 6:return l.fn.call(l.context,e,n,i,o,a),!0}for(s=1,u=new Array(f-1);s<f;s++)u[s-1]=arguments[s];l.fn.apply(l.context,u)}else{var p,h=l.length;for(s=0;s<h;s++)switch(l[s].once&&this.removeListener(t,l[s].fn,void 0,!0),f){case 1:l[s].fn.call(l[s].context);break;case 2:l[s].fn.call(l[s].context,e);break;case 3:l[s].fn.call(l[s].context,e,n);break;case 4:l[s].fn.call(l[s].context,e,n,i);break;default:if(!u)for(p=1,u=new Array(f-1);p<f;p++)u[p-1]=arguments[p];l[s].fn.apply(l[s].context,u)}}return!0},c.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,i){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var c=this._events[o];if(c.fn)c.fn!==e||i&&!c.once||n&&c.context!==n||a(this,o);else{for(var u=0,s=[],l=c.length;u<l;u++)(c[u].fn!==e||i&&!c[u].once||n&&c[u].context!==n)&&s.push(c[u]);s.length?this._events[o]=1===s.length?s[0]:s:a(this,o)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},8367:function(t,e){!function(t){"use strict";function e(t){return function(e,r,n,i,o,a,c){return t(e,r,c)}}function r(t){return function(e,r,n,i){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n,i);var o=i.get(e),a=i.get(r);if(o&&a)return o===r&&a===e;i.set(e,r),i.set(r,e);var c=t(e,r,n,i);return i.delete(e),i.delete(r),c}}function n(t,e){var r={};for(var n in t)r[n]=t[n];for(var n in e)r[n]=e[n];return r}function i(t){return t.constructor===Object||null==t.constructor}function o(t){return"function"==typeof t.then}function a(t,e){return t===e||t!=t&&e!=e}var c="[object Arguments]",u="[object Boolean]",s="[object Date]",l="[object RegExp]",f="[object Map]",p="[object Number]",h="[object Object]",d="[object Set]",y="[object String]",v=Object.prototype.toString;function m(t){var e=t.areArraysEqual,r=t.areDatesEqual,n=t.areMapsEqual,m=t.areObjectsEqual,g=t.areRegExpsEqual,b=t.areSetsEqual,x=(0,t.createIsNestedEqual)(w);function w(t,w,O){if(t===w)return!0;if(!t||!w||"object"!=typeof t||"object"!=typeof w)return t!=t&&w!=w;if(i(t)&&i(w))return m(t,w,x,O);var S=Array.isArray(t),A=Array.isArray(w);if(S||A)return S===A&&e(t,w,x,O);var j=v.call(t);return j===v.call(w)&&(j===s?r(t,w,x,O):j===l?g(t,w,x,O):j===f?n(t,w,x,O):j===d?b(t,w,x,O):j===h||j===c?!o(t)&&!o(w)&&m(t,w,x,O):(j===u||j===p||j===y)&&a(t.valueOf(),w.valueOf()))}return w}function g(t,e,r,n){var i=t.length;if(e.length!==i)return!1;for(;i-- >0;)if(!r(t[i],e[i],i,i,t,e,n))return!1;return!0}var b=r(g);function x(t,e){return a(t.valueOf(),e.valueOf())}function w(t,e,r,n){var i=t.size===e.size;if(!i)return!1;if(!t.size)return!0;var o={},a=0;return t.forEach((function(c,u){if(i){var s=!1,l=0;e.forEach((function(i,f){s||o[l]||!(s=r(u,f,a,l,t,e,n)&&r(c,i,u,f,t,e,n))||(o[l]=!0),l++})),a++,i=s}})),i}var O=r(w),S="_owner",A=Object.prototype.hasOwnProperty;function j(t,e,r,n){var i,o=Object.keys(t),a=o.length;if(Object.keys(e).length!==a)return!1;for(;a-- >0;){if((i=o[a])===S){var c=!!t.$$typeof,u=!!e.$$typeof;if((c||u)&&c!==u)return!1}if(!A.call(e,i)||!r(t[i],e[i],i,i,t,e,n))return!1}return!0}var E=r(j);function k(t,e){return t.source===e.source&&t.flags===e.flags}function P(t,e,r,n){var i=t.size===e.size;if(!i)return!1;if(!t.size)return!0;var o={};return t.forEach((function(a,c){if(i){var u=!1,s=0;e.forEach((function(i,l){u||o[s]||!(u=r(a,i,c,l,t,e,n))||(o[s]=!0),s++})),i=u}})),i}var _=r(P),M=Object.freeze({areArraysEqual:g,areDatesEqual:x,areMapsEqual:w,areObjectsEqual:j,areRegExpsEqual:k,areSetsEqual:P,createIsNestedEqual:e}),T=Object.freeze({areArraysEqual:b,areDatesEqual:x,areMapsEqual:O,areObjectsEqual:E,areRegExpsEqual:k,areSetsEqual:_,createIsNestedEqual:e}),C=m(M);function I(t,e){return C(t,e,void 0)}var N=m(n(M,{createIsNestedEqual:function(){return a}}));function D(t,e){return N(t,e,void 0)}var R=m(T);function L(t,e){return R(t,e,new WeakMap)}var B=m(n(T,{createIsNestedEqual:function(){return a}}));function z(t,e){return B(t,e,new WeakMap)}function F(t){return m(n(M,t(M)))}function U(t){var e=m(n(T,t(T)));return function(t,r,n){return void 0===n&&(n=new WeakMap),e(t,r,n)}}t.circularDeepEqual=L,t.circularShallowEqual=z,t.createCustomCircularEqual=U,t.createCustomEqual=F,t.deepEqual=I,t.sameValueZeroEqual=a,t.shallowEqual=D,Object.defineProperty(t,"__esModule",{value:!0})}(e)},8552:(t,e,r)=>{var n=r(852)(r(5639),"DataView");t.exports=n},1989:(t,e,r)=>{var n=r(1789),i=r(401),o=r(7667),a=r(1327),c=r(1866);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},8407:(t,e,r)=>{var n=r(7040),i=r(4125),o=r(2117),a=r(7518),c=r(4705);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},7071:(t,e,r)=>{var n=r(852)(r(5639),"Map");t.exports=n},3369:(t,e,r)=>{var n=r(4785),i=r(1285),o=r(6e3),a=r(9916),c=r(5265);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},3818:(t,e,r)=>{var n=r(852)(r(5639),"Promise");t.exports=n},8525:(t,e,r)=>{var n=r(852)(r(5639),"Set");t.exports=n},8668:(t,e,r)=>{var n=r(3369),i=r(619),o=r(2385);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=i,a.prototype.has=o,t.exports=a},6384:(t,e,r)=>{var n=r(8407),i=r(7465),o=r(3779),a=r(7599),c=r(4758),u=r(4309);function s(t){var e=this.__data__=new n(t);this.size=e.size}s.prototype.clear=i,s.prototype.delete=o,s.prototype.get=a,s.prototype.has=c,s.prototype.set=u,t.exports=s},2705:(t,e,r)=>{var n=r(5639).Symbol;t.exports=n},1149:(t,e,r)=>{var n=r(5639).Uint8Array;t.exports=n},577:(t,e,r)=>{var n=r(852)(r(5639),"WeakMap");t.exports=n},6874:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},7412:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n&&!1!==e(t[r],r,t););return t}},6193:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},4963:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=0,o=[];++r<n;){var a=t[r];e(a,r,t)&&(o[i++]=a)}return o}},7443:(t,e,r)=>{var n=r(2118);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},1196:t=>{t.exports=function(t,e,r){for(var n=-1,i=null==t?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}},4636:(t,e,r)=>{var n=r(2545),i=r(5694),o=r(1469),a=r(4144),c=r(5776),u=r(6719),s=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=o(t),l=!r&&i(t),f=!r&&!l&&a(t),p=!r&&!l&&!f&&u(t),h=r||l||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)!e&&!s.call(t,v)||h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y))||d.push(v);return d}},9932:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}},2488:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t}},2908:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},4286:t=>{t.exports=function(t){return t.split("")}},4865:(t,e,r)=>{var n=r(9465),i=r(7813),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r){var a=t[e];o.call(t,e)&&i(a,r)&&(void 0!==r||e in t)||n(t,e,r)}},8470:(t,e,r)=>{var n=r(7813);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return-1}},4037:(t,e,r)=>{var n=r(8363),i=r(3674);t.exports=function(t,e){return t&&n(e,i(e),t)}},3886:(t,e,r)=>{var n=r(8363),i=r(1704);t.exports=function(t,e){return t&&n(e,i(e),t)}},9465:(t,e,r)=>{var n=r(8777);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},5990:(t,e,r)=>{var n=r(6384),i=r(7412),o=r(4865),a=r(4037),c=r(3886),u=r(4626),s=r(278),l=r(5341),f=r(1911),p=r(8234),h=r(6904),d=r(4160),y=r(3824),v=r(9148),m=r(8517),g=r(1469),b=r(4144),x=r(6688),w=r(3218),O=r(2928),S=r(3674),A=r(1704),j="[object Arguments]",E="[object Function]",k="[object Object]",P={};P[j]=P["[object Array]"]=P["[object ArrayBuffer]"]=P["[object DataView]"]=P["[object Boolean]"]=P["[object Date]"]=P["[object Float32Array]"]=P["[object Float64Array]"]=P["[object Int8Array]"]=P["[object Int16Array]"]=P["[object Int32Array]"]=P["[object Map]"]=P["[object Number]"]=P[k]=P["[object RegExp]"]=P["[object Set]"]=P["[object String]"]=P["[object Symbol]"]=P["[object Uint8Array]"]=P["[object Uint8ClampedArray]"]=P["[object Uint16Array]"]=P["[object Uint32Array]"]=!0,P["[object Error]"]=P[E]=P["[object WeakMap]"]=!1,t.exports=function t(e,r,_,M,T,C){var I,N=1&r,D=2&r,R=4&r;if(_&&(I=T?_(e,M,T,C):_(e)),void 0!==I)return I;if(!w(e))return e;var L=g(e);if(L){if(I=y(e),!N)return s(e,I)}else{var B=d(e),z=B==E||"[object GeneratorFunction]"==B;if(b(e))return u(e,N);if(B==k||B==j||z&&!T){if(I=D||z?{}:m(e),!N)return D?f(e,c(I,e)):l(e,a(I,e))}else{if(!P[B])return T?e:{};I=v(e,B,N)}}C||(C=new n);var F=C.get(e);if(F)return F;C.set(e,I),O(e)?e.forEach((function(n){I.add(t(n,r,_,n,e,C))})):x(e)&&e.forEach((function(n,i){I.set(i,t(n,r,_,i,e,C))}));var U=L?void 0:(R?D?h:p:D?A:S)(e);return i(U||e,(function(n,i){U&&(n=e[i=n]),o(I,i,t(n,r,_,i,e,C))})),I}},3118:(t,e,r)=>{var n=r(3218),i=Object.create,o=function(){function t(){}return function(e){if(!n(e))return{};if(i)return i(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}();t.exports=o},9881:(t,e,r)=>{var n=r(7816),i=r(9291)(n);t.exports=i},3239:(t,e,r)=>{var n=r(9881);t.exports=function(t,e){var r=!0;return n(t,(function(t,n,i){return r=!!e(t,n,i)})),r}},6029:(t,e,r)=>{var n=r(3448);t.exports=function(t,e,r){for(var i=-1,o=t.length;++i<o;){var a=t[i],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,s=a}return s}},1848:t=>{t.exports=function(t,e,r,n){for(var i=t.length,o=r+(n?1:-1);n?o--:++o<i;)if(e(t[o],o,t))return o;return-1}},1078:(t,e,r)=>{var n=r(2488),i=r(7285);t.exports=function t(e,r,o,a,c){var u=-1,s=e.length;for(o||(o=i),c||(c=[]);++u<s;){var l=e[u];r>0&&o(l)?r>1?t(l,r-1,o,a,c):n(c,l):a||(c[c.length]=l)}return c}},8483:(t,e,r)=>{var n=r(5063)();t.exports=n},7816:(t,e,r)=>{var n=r(8483),i=r(3674);t.exports=function(t,e){return t&&n(t,e,i)}},7786:(t,e,r)=>{var n=r(1811),i=r(327);t.exports=function(t,e){for(var r=0,o=(e=n(e,t)).length;null!=t&&r<o;)t=t[i(e[r++])];return r&&r==o?t:void 0}},8866:(t,e,r)=>{var n=r(2488),i=r(1469);t.exports=function(t,e,r){var o=e(t);return i(t)?o:n(o,r(t))}},4239:(t,e,r)=>{var n=r(2705),i=r(9607),o=r(2333),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?i(t):o(t)}},3325:t=>{t.exports=function(t,e){return t>e}},13:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},2118:(t,e,r)=>{var n=r(1848),i=r(2722),o=r(2351);t.exports=function(t,e,r){return e==e?o(t,e,r):n(t,i,r)}},9454:(t,e,r)=>{var n=r(4239),i=r(7005);t.exports=function(t){return i(t)&&"[object Arguments]"==n(t)}},939:(t,e,r)=>{var n=r(2492),i=r(7005);t.exports=function t(e,r,o,a,c){return e===r||(null==e||null==r||!i(e)&&!i(r)?e!=e&&r!=r:n(e,r,o,a,t,c))}},2492:(t,e,r)=>{var n=r(6384),i=r(7114),o=r(8351),a=r(6096),c=r(4160),u=r(1469),s=r(4144),l=r(6719),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var g=u(t),b=u(e),x=g?p:c(t),w=b?p:c(e),O=(x=x==f?h:x)==h,S=(w=w==f?h:w)==h,A=x==w;if(A&&s(t)){if(!s(e))return!1;g=!0,O=!1}if(A&&!O)return m||(m=new n),g||l(t)?i(t,e,r,y,v,m):o(t,e,x,r,y,v,m);if(!(1&r)){var j=O&&d.call(t,"__wrapped__"),E=S&&d.call(e,"__wrapped__");if(j||E){var k=j?t.value():t,P=E?e.value():e;return m||(m=new n),v(k,P,r,y,m)}}return!!A&&(m||(m=new n),a(t,e,r,y,v,m))}},5588:(t,e,r)=>{var n=r(4160),i=r(7005);t.exports=function(t){return i(t)&&"[object Map]"==n(t)}},2958:(t,e,r)=>{var n=r(6384),i=r(939);t.exports=function(t,e,r,o){var a=r.length,c=a,u=!o;if(null==t)return!c;for(t=Object(t);a--;){var s=r[a];if(u&&s[2]?s[1]!==t[s[0]]:!(s[0]in t))return!1}for(;++a<c;){var l=(s=r[a])[0],f=t[l],p=s[1];if(u&&s[2]){if(void 0===f&&!(l in t))return!1}else{var h=new n;if(o)var d=o(f,p,l,t,e,h);if(!(void 0===d?i(p,f,3,o,h):d))return!1}}return!0}},2722:t=>{t.exports=function(t){return t!=t}},8458:(t,e,r)=>{var n=r(3560),i=r(5346),o=r(3218),a=r(346),c=/^\[object .+?Constructor\]$/,u=Function.prototype,s=Object.prototype,l=u.toString,f=s.hasOwnProperty,p=RegExp("^"+l.call(f).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!o(t)||i(t))&&(n(t)?p:c).test(a(t))}},9221:(t,e,r)=>{var n=r(4160),i=r(7005);t.exports=function(t){return i(t)&&"[object Set]"==n(t)}},8749:(t,e,r)=>{var n=r(4239),i=r(1780),o=r(7005),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return o(t)&&i(t.length)&&!!a[n(t)]}},7206:(t,e,r)=>{var n=r(1573),i=r(6432),o=r(6557),a=r(1469),c=r(9601);t.exports=function(t){return"function"==typeof t?t:null==t?o:"object"==typeof t?a(t)?i(t[0],t[1]):n(t):c(t)}},280:(t,e,r)=>{var n=r(5726),i=r(6916),o=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return i(t);var e=[];for(var r in Object(t))o.call(t,r)&&"constructor"!=r&&e.push(r);return e}},313:(t,e,r)=>{var n=r(3218),i=r(5726),o=r(3498),a=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=i(t),r=[];for(var c in t)("constructor"!=c||!e&&a.call(t,c))&&r.push(c);return r}},433:t=>{t.exports=function(t,e){return t<e}},9199:(t,e,r)=>{var n=r(9881),i=r(8612);t.exports=function(t,e){var r=-1,o=i(t)?Array(t.length):[];return n(t,(function(t,n,i){o[++r]=e(t,n,i)})),o}},1573:(t,e,r)=>{var n=r(2958),i=r(1499),o=r(2634);t.exports=function(t){var e=i(t);return 1==e.length&&e[0][2]?o(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},6432:(t,e,r)=>{var n=r(939),i=r(7361),o=r(9095),a=r(5403),c=r(9162),u=r(2634),s=r(327);t.exports=function(t,e){return a(t)&&c(e)?u(s(t),e):function(r){var a=i(r,t);return void 0===a&&a===e?o(r,t):n(e,a,3)}}},9556:(t,e,r)=>{var n=r(9932),i=r(7786),o=r(7206),a=r(9199),c=r(1131),u=r(1717),s=r(5022),l=r(6557),f=r(1469);t.exports=function(t,e,r){e=e.length?n(e,(function(t){return f(t)?function(e){return i(e,1===t.length?t[0]:t)}:t})):[l];var p=-1;e=n(e,u(o));var h=a(t,(function(t,r,i){return{criteria:n(e,(function(e){return e(t)})),index:++p,value:t}}));return c(h,(function(t,e){return s(t,e,r)}))}},371:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},9152:(t,e,r)=>{var n=r(7786);t.exports=function(t){return function(e){return n(e,t)}}},98:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,i,o){for(var a=-1,c=r(e((n-t)/(i||1)),0),u=Array(c);c--;)u[o?c:++a]=t,t+=i;return u}},5976:(t,e,r)=>{var n=r(6557),i=r(5357),o=r(61);t.exports=function(t,e){return o(i(t,e,n),t+"")}},6560:(t,e,r)=>{var n=r(5703),i=r(8777),o=r(6557),a=i?function(t,e){return i(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:o;t.exports=a},4259:t=>{t.exports=function(t,e,r){var n=-1,i=t.length;e<0&&(e=-e>i?0:i+e),(r=r>i?i:r)<0&&(r+=i),i=e>r?0:r-e>>>0,e>>>=0;for(var o=Array(i);++n<i;)o[n]=t[n+e];return o}},5076:(t,e,r)=>{var n=r(9881);t.exports=function(t,e){var r;return n(t,(function(t,n,i){return!(r=e(t,n,i))})),!!r}},1131:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},7762:t=>{t.exports=function(t,e){for(var r,n=-1,i=t.length;++n<i;){var o=e(t[n]);void 0!==o&&(r=void 0===r?o:r+o)}return r}},2545:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},531:(t,e,r)=>{var n=r(2705),i=r(9932),o=r(1469),a=r(3448),c=n?n.prototype:void 0,u=c?c.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(o(e))return i(e,t)+"";if(a(e))return u?u.call(e):"";var r=e+"";return"0"==r&&1/e==-Infinity?"-0":r}},7561:(t,e,r)=>{var n=r(7990),i=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(i,""):t}},1717:t=>{t.exports=function(t){return function(e){return t(e)}}},5652:(t,e,r)=>{var n=r(8668),i=r(7443),o=r(1196),a=r(4757),c=r(3593),u=r(1814);t.exports=function(t,e,r){var s=-1,l=i,f=t.length,p=!0,h=[],d=h;if(r)p=!1,l=o;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,l=a,d=new n}else d=e?[]:h;t:for(;++s<f;){var v=t[s],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var g=d.length;g--;)if(d[g]===m)continue t;e&&d.push(m),h.push(v)}else l(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},7406:(t,e,r)=>{var n=r(1811),i=r(928),o=r(292),a=r(327);t.exports=function(t,e){return e=n(e,t),null==(t=o(t,e))||delete t[a(i(e))]}},4757:t=>{t.exports=function(t,e){return t.has(e)}},1811:(t,e,r)=>{var n=r(1469),i=r(5403),o=r(5514),a=r(9833);t.exports=function(t,e){return n(t)?t:i(t,e)?[t]:o(a(t))}},180:(t,e,r)=>{var n=r(4259);t.exports=function(t,e,r){var i=t.length;return r=void 0===r?i:r,!e&&r>=i?t:n(t,e,r)}},4318:(t,e,r)=>{var n=r(1149);t.exports=function(t){var e=new t.constructor(t.byteLength);return new n(e).set(new n(t)),e}},4626:(t,e,r)=>{t=r.nmd(t);var n=r(5639),i=e&&!e.nodeType&&e,o=i&&t&&!t.nodeType&&t,a=o&&o.exports===i?n.Buffer:void 0,c=a?a.allocUnsafe:void 0;t.exports=function(t,e){if(e)return t.slice();var r=t.length,n=c?c(r):new t.constructor(r);return t.copy(n),n}},7157:(t,e,r)=>{var n=r(4318);t.exports=function(t,e){var r=e?n(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)}},3147:t=>{var e=/\w*$/;t.exports=function(t){var r=new t.constructor(t.source,e.exec(t));return r.lastIndex=t.lastIndex,r}},419:(t,e,r)=>{var n=r(2705),i=n?n.prototype:void 0,o=i?i.valueOf:void 0;t.exports=function(t){return o?Object(o.call(t)):{}}},7133:(t,e,r)=>{var n=r(4318);t.exports=function(t,e){var r=e?n(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}},6393:(t,e,r)=>{var n=r(3448);t.exports=function(t,e){if(t!==e){var r=void 0!==t,i=null===t,o=t==t,a=n(t),c=void 0!==e,u=null===e,s=e==e,l=n(e);if(!u&&!l&&!a&&t>e||a&&c&&s&&!u&&!l||i&&c&&s||!r&&s||!o)return 1;if(!i&&!a&&!l&&t<e||l&&r&&o&&!i&&!a||u&&r&&o||!c&&o||!s)return-1}return 0}},5022:(t,e,r)=>{var n=r(6393);t.exports=function(t,e,r){for(var i=-1,o=t.criteria,a=e.criteria,c=o.length,u=r.length;++i<c;){var s=n(o[i],a[i]);if(s)return i>=u?s:s*("desc"==r[i]?-1:1)}return t.index-e.index}},278:t=>{t.exports=function(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e}},8363:(t,e,r)=>{var n=r(4865),i=r(9465);t.exports=function(t,e,r,o){var a=!r;r||(r={});for(var c=-1,u=e.length;++c<u;){var s=e[c],l=o?o(r[s],t[s],s,r,t):void 0;void 0===l&&(l=t[s]),a?i(r,s,l):n(r,s,l)}return r}},5341:(t,e,r)=>{var n=r(8363),i=r(9551);t.exports=function(t,e){return n(t,i(t),e)}},1911:(t,e,r)=>{var n=r(8363),i=r(1442);t.exports=function(t,e){return n(t,i(t),e)}},4429:(t,e,r)=>{var n=r(5639)["__core-js_shared__"];t.exports=n},9291:(t,e,r)=>{var n=r(8612);t.exports=function(t,e){return function(r,i){if(null==r)return r;if(!n(r))return t(r,i);for(var o=r.length,a=e?o:-1,c=Object(r);(e?a--:++a<o)&&!1!==i(c[a],a,c););return r}}},5063:t=>{t.exports=function(t){return function(e,r,n){for(var i=-1,o=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++i];if(!1===r(o[u],u,o))break}return e}}},8805:(t,e,r)=>{var n=r(180),i=r(2689),o=r(3140),a=r(9833);t.exports=function(t){return function(e){e=a(e);var r=i(e)?o(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},7740:(t,e,r)=>{var n=r(7206),i=r(8612),o=r(3674);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!i(e)){var u=n(r,3);e=o(e),r=function(t){return u(c[t],t,c)}}var s=t(e,r,a);return s>-1?c[u?e[s]:s]:void 0}}},7445:(t,e,r)=>{var n=r(98),i=r(6612),o=r(8601);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&i(e,r,a)&&(r=a=void 0),e=o(e),void 0===r?(r=e,e=0):r=o(r),a=void 0===a?e<r?1:-1:o(a),n(e,r,a,t)}}},3593:(t,e,r)=>{var n=r(8525),i=r(308),o=r(1814),a=n&&1/o(new n([,-0]))[1]==1/0?function(t){return new n(t)}:i;t.exports=a},696:(t,e,r)=>{var n=r(8630);t.exports=function(t){return n(t)?void 0:t}},8777:(t,e,r)=>{var n=r(852),i=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=i},7114:(t,e,r)=>{var n=r(8668),i=r(2908),o=r(4757);t.exports=function(t,e,r,a,c,u){var s=1&r,l=t.length,f=e.length;if(l!=f&&!(s&&f>l))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<l;){var m=t[d],g=e[d];if(a)var b=s?a(g,m,d,e,t,u):a(m,g,d,t,e,u);if(void 0!==b){if(b)continue;y=!1;break}if(v){if(!i(e,(function(t,e){if(!o(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)}))){y=!1;break}}else if(m!==g&&!c(m,g,r,a,u)){y=!1;break}}return u.delete(t),u.delete(e),y}},8351:(t,e,r)=>{var n=r(2705),i=r(1149),o=r(7813),a=r(7114),c=r(8776),u=r(1814),s=n?n.prototype:void 0,l=s?s.valueOf:void 0;t.exports=function(t,e,r,n,s,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!f(new i(t),new i(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return o(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)return!1;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,s,f,p);return p.delete(t),v;case"[object Symbol]":if(l)return l.call(t)==l.call(e)}return!1}},6096:(t,e,r)=>{var n=r(8234),i=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,o,a,c){var u=1&r,s=n(t),l=s.length;if(l!=n(e).length&&!u)return!1;for(var f=l;f--;){var p=s[f];if(!(u?p in e:i.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<l;){var m=t[p=s[f]],g=e[p];if(o)var b=u?o(g,m,p,e,t,c):o(m,g,p,t,e,c);if(!(void 0===b?m===g||a(m,g,r,o,c):b)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x==w||!("constructor"in t)||!("constructor"in e)||"function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w||(y=!1)}return c.delete(t),c.delete(e),y}},9021:(t,e,r)=>{var n=r(5564),i=r(5357),o=r(61);t.exports=function(t){return o(i(t,void 0,n),t+"")}},1957:(t,e,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;t.exports=n},8234:(t,e,r)=>{var n=r(8866),i=r(9551),o=r(3674);t.exports=function(t){return n(t,o,i)}},6904:(t,e,r)=>{var n=r(8866),i=r(1442),o=r(1704);t.exports=function(t){return n(t,o,i)}},5050:(t,e,r)=>{var n=r(7019);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},1499:(t,e,r)=>{var n=r(9162),i=r(3674);t.exports=function(t){for(var e=i(t),r=e.length;r--;){var o=e[r],a=t[o];e[r]=[o,a,n(a)]}return e}},852:(t,e,r)=>{var n=r(8458),i=r(7801);t.exports=function(t,e){var r=i(t,e);return n(r)?r:void 0}},5924:(t,e,r)=>{var n=r(5569)(Object.getPrototypeOf,Object);t.exports=n},9607:(t,e,r)=>{var n=r(2705),i=Object.prototype,o=i.hasOwnProperty,a=i.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=o.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var i=a.call(t);return n&&(e?t[c]=r:delete t[c]),i}},9551:(t,e,r)=>{var n=r(4963),i=r(479),o=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,c=a?function(t){return null==t?[]:(t=Object(t),n(a(t),(function(e){return o.call(t,e)})))}:i;t.exports=c},1442:(t,e,r)=>{var n=r(2488),i=r(5924),o=r(9551),a=r(479),c=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)n(e,o(t)),t=i(t);return e}:a;t.exports=c},4160:(t,e,r)=>{var n=r(8552),i=r(7071),o=r(3818),a=r(8525),c=r(577),u=r(4239),s=r(346),l="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=s(n),v=s(i),m=s(o),g=s(a),b=s(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||i&&x(new i)!=l||o&&x(o.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?s(r):"";if(n)switch(n){case y:return d;case v:return l;case m:return f;case g:return p;case b:return h}return e}),t.exports=x},7801:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},222:(t,e,r)=>{var n=r(1811),i=r(5694),o=r(1469),a=r(5776),c=r(1780),u=r(327);t.exports=function(t,e,r){for(var s=-1,l=(e=n(e,t)).length,f=!1;++s<l;){var p=u(e[s]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++s!=l?f:!!(l=null==t?0:t.length)&&c(l)&&a(p,l)&&(o(t)||i(t))}},2689:t=>{var e=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},1789:(t,e,r)=>{var n=r(4536);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},401:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},7667:(t,e,r)=>{var n=r(4536),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return i.call(e,t)?e[t]:void 0}},1327:(t,e,r)=>{var n=r(4536),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:i.call(e,t)}},1866:(t,e,r)=>{var n=r(4536);t.exports=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},3824:t=>{var e=Object.prototype.hasOwnProperty;t.exports=function(t){var r=t.length,n=new t.constructor(r);return r&&"string"==typeof t[0]&&e.call(t,"index")&&(n.index=t.index,n.input=t.input),n}},9148:(t,e,r)=>{var n=r(4318),i=r(7157),o=r(3147),a=r(419),c=r(7133);t.exports=function(t,e,r){var u=t.constructor;switch(e){case"[object ArrayBuffer]":return n(t);case"[object Boolean]":case"[object Date]":return new u(+t);case"[object DataView]":return i(t,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return c(t,r);case"[object Map]":case"[object Set]":return new u;case"[object Number]":case"[object String]":return new u(t);case"[object RegExp]":return o(t);case"[object Symbol]":return a(t)}}},8517:(t,e,r)=>{var n=r(3118),i=r(5924),o=r(5726);t.exports=function(t){return"function"!=typeof t.constructor||o(t)?{}:n(i(t))}},7285:(t,e,r)=>{var n=r(2705),i=r(5694),o=r(1469),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return o(t)||i(t)||!!(a&&t&&t[a])}},5776:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},6612:(t,e,r)=>{var n=r(7813),i=r(8612),o=r(5776),a=r(3218);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return!!("number"==c?i(r)&&o(e,r.length):"string"==c&&e in r)&&n(r[e],t)}},5403:(t,e,r)=>{var n=r(1469),i=r(3448),o=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=t&&!i(t))||(a.test(t)||!o.test(t)||null!=e&&t in Object(e))}},7019:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},5346:(t,e,r)=>{var n,i=r(4429),o=(n=/[^.]+$/.exec(i&&i.keys&&i.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";t.exports=function(t){return!!o&&o in t}},5726:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},9162:(t,e,r)=>{var n=r(3218);t.exports=function(t){return t==t&&!n(t)}},7040:t=>{t.exports=function(){this.__data__=[],this.size=0}},4125:(t,e,r)=>{var n=r(8470),i=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():i.call(e,r,1),--this.size,!0)}},2117:(t,e,r)=>{var n=r(8470);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},7518:(t,e,r)=>{var n=r(8470);t.exports=function(t){return n(this.__data__,t)>-1}},4705:(t,e,r)=>{var n=r(8470);t.exports=function(t,e){var r=this.__data__,i=n(r,t);return i<0?(++this.size,r.push([t,e])):r[i][1]=e,this}},4785:(t,e,r)=>{var n=r(1989),i=r(8407),o=r(7071);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(o||i),string:new n}}},1285:(t,e,r)=>{var n=r(5050);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=e?1:0,e}},6e3:(t,e,r)=>{var n=r(5050);t.exports=function(t){return n(this,t).get(t)}},9916:(t,e,r)=>{var n=r(5050);t.exports=function(t){return n(this,t).has(t)}},5265:(t,e,r)=>{var n=r(5050);t.exports=function(t,e){var r=n(this,t),i=r.size;return r.set(t,e),this.size+=r.size==i?0:1,this}},8776:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t,n){r[++e]=[n,t]})),r}},2634:t=>{t.exports=function(t,e){return function(r){return null!=r&&(r[t]===e&&(void 0!==e||t in Object(r)))}}},4523:(t,e,r)=>{var n=r(8306);t.exports=function(t){var e=n(t,(function(t){return 500===r.size&&r.clear(),t})),r=e.cache;return e}},4536:(t,e,r)=>{var n=r(852)(Object,"create");t.exports=n},6916:(t,e,r)=>{var n=r(5569)(Object.keys,Object);t.exports=n},3498:t=>{t.exports=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e}},1167:(t,e,r)=>{t=r.nmd(t);var n=r(1957),i=e&&!e.nodeType&&e,o=i&&t&&!t.nodeType&&t,a=o&&o.exports===i&&n.process,c=function(){try{var t=o&&o.require&&o.require("util").types;return t||a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},2333:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},5569:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},5357:(t,e,r)=>{var n=r(6874),i=Math.max;t.exports=function(t,e,r){return e=i(void 0===e?t.length-1:e,0),function(){for(var o=arguments,a=-1,c=i(o.length-e,0),u=Array(c);++a<c;)u[a]=o[e+a];a=-1;for(var s=Array(e+1);++a<e;)s[a]=o[a];return s[e]=r(u),n(t,this,s)}}},292:(t,e,r)=>{var n=r(7786),i=r(4259);t.exports=function(t,e){return e.length<2?t:n(t,i(e,0,-1))}},5639:(t,e,r)=>{var n=r(1957),i="object"==typeof self&&self&&self.Object===Object&&self,o=n||i||Function("return this")();t.exports=o},619:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},2385:t=>{t.exports=function(t){return this.__data__.has(t)}},1814:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r}},61:(t,e,r)=>{var n=r(6560),i=r(1275)(n);t.exports=i},1275:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var i=e(),o=16-(i-n);if(n=i,o>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},7465:(t,e,r)=>{var n=r(8407);t.exports=function(){this.__data__=new n,this.size=0}},3779:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},7599:t=>{t.exports=function(t){return this.__data__.get(t)}},4758:t=>{t.exports=function(t){return this.__data__.has(t)}},4309:(t,e,r)=>{var n=r(8407),i=r(7071),o=r(3369);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!i||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new o(a)}return r.set(t,e),this.size=r.size,this}},2351:t=>{t.exports=function(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return-1}},3140:(t,e,r)=>{var n=r(4286),i=r(2689),o=r(676);t.exports=function(t){return i(t)?o(t):n(t)}},5514:(t,e,r)=>{var n=r(4523),i=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,o=/\\(\\)?/g,a=n((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(i,(function(t,r,n,i){e.push(n?i.replace(o,"$1"):r||t)})),e}));t.exports=a},327:(t,e,r)=>{var n=r(3448);t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-Infinity?"-0":e}},346:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},7990:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},676:t=>{var e="\\ud800-\\udfff",r="["+e+"]",n="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",i="\\ud83c[\\udffb-\\udfff]",o="[^"+e+"]",a="(?:\\ud83c[\\udde6-\\uddff]){2}",c="[\\ud800-\\udbff][\\udc00-\\udfff]",u="(?:"+n+"|"+i+")"+"?",s="[\\ufe0e\\ufe0f]?",l=s+u+("(?:\\u200d(?:"+[o,a,c].join("|")+")"+s+u+")*"),f="(?:"+[o+n+"?",n,a,c,r].join("|")+")",p=RegExp(i+"(?="+i+")|"+f+l,"g");t.exports=function(t){return t.match(p)||[]}},5703:t=>{t.exports=function(t){return function(){return t}}},3279:(t,e,r)=>{var n=r(3218),i=r(7771),o=r(4841),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,s,l,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function g(e){var r=u,n=s;return u=s=void 0,d=e,f=t.apply(n,r)}function b(t){return d=t,p=setTimeout(w,e),y?g(t):f}function x(t){var r=t-h;return void 0===h||r>=e||r<0||v&&t-d>=l}function w(){var t=i();if(x(t))return O(t);p=setTimeout(w,function(t){var r=e-(t-h);return v?c(r,l-(t-d)):r}(t))}function O(t){return p=void 0,m&&u?g(t):(u=s=void 0,f)}function S(){var t=i(),r=x(t);if(u=arguments,s=this,h=t,r){if(void 0===p)return b(h);if(v)return clearTimeout(p),p=setTimeout(w,e),g(h)}return void 0===p&&(p=setTimeout(w,e)),f}return e=o(e)||0,n(r)&&(y=!!r.leading,l=(v="maxWait"in r)?a(o(r.maxWait)||0,e):l,m="trailing"in r?!!r.trailing:m),S.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=s=p=void 0},S.flush=function(){return void 0===p?f:O(i())},S}},7813:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},711:(t,e,r)=>{var n=r(6193),i=r(3239),o=r(7206),a=r(1469),c=r(6612);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},3311:(t,e,r)=>{var n=r(7740)(r(998));t.exports=n},998:(t,e,r)=>{var n=r(1848),i=r(7206),o=r(554),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return-1;var u=null==r?0:o(r);return u<0&&(u=a(c+u,0)),n(t,i(e,3),u)}},8804:(t,e,r)=>{t.exports=r(1175)},4654:(t,e,r)=>{var n=r(1078),i=r(5161);t.exports=function(t,e){return n(i(t,e),1)}},5564:(t,e,r)=>{var n=r(1078);t.exports=function(t){return(null==t?0:t.length)?n(t,1):[]}},7361:(t,e,r)=>{var n=r(7786);t.exports=function(t,e,r){var i=null==t?void 0:n(t,e);return void 0===i?r:i}},9095:(t,e,r)=>{var n=r(13),i=r(222);t.exports=function(t,e){return null!=t&&i(t,e,n)}},1175:t=>{t.exports=function(t){return t&&t.length?t[0]:void 0}},6557:t=>{t.exports=function(t){return t}},5694:(t,e,r)=>{var n=r(9454),i=r(7005),o=Object.prototype,a=o.hasOwnProperty,c=o.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(t){return i(t)&&a.call(t,"callee")&&!c.call(t,"callee")};t.exports=u},1469:t=>{var e=Array.isArray;t.exports=e},8612:(t,e,r)=>{var n=r(3560),i=r(1780);t.exports=function(t){return null!=t&&i(t.length)&&!n(t)}},1584:(t,e,r)=>{var n=r(4239),i=r(7005);t.exports=function(t){return!0===t||!1===t||i(t)&&"[object Boolean]"==n(t)}},4144:(t,e,r)=>{t=r.nmd(t);var n=r(5639),i=r(5062),o=e&&!e.nodeType&&e,a=o&&t&&!t.nodeType&&t,c=a&&a.exports===o?n.Buffer:void 0,u=(c?c.isBuffer:void 0)||i;t.exports=u},8446:(t,e,r)=>{var n=r(939);t.exports=function(t,e){return n(t,e)}},3560:(t,e,r)=>{var n=r(4239),i=r(3218);t.exports=function(t){if(!i(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},1780:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},6688:(t,e,r)=>{var n=r(5588),i=r(1717),o=r(1167),a=o&&o.isMap,c=a?i(a):n;t.exports=c},7654:(t,e,r)=>{var n=r(1763);t.exports=function(t){return n(t)&&t!=+t}},4293:t=>{t.exports=function(t){return null==t}},1763:(t,e,r)=>{var n=r(4239),i=r(7005);t.exports=function(t){return"number"==typeof t||i(t)&&"[object Number]"==n(t)}},3218:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},7005:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},8630:(t,e,r)=>{var n=r(4239),i=r(5924),o=r(7005),a=Function.prototype,c=Object.prototype,u=a.toString,s=c.hasOwnProperty,l=u.call(Object);t.exports=function(t){if(!o(t)||"[object Object]"!=n(t))return!1;var e=i(t);if(null===e)return!0;var r=s.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&u.call(r)==l}},2928:(t,e,r)=>{var n=r(9221),i=r(1717),o=r(1167),a=o&&o.isSet,c=a?i(a):n;t.exports=c},7037:(t,e,r)=>{var n=r(4239),i=r(1469),o=r(7005);t.exports=function(t){return"string"==typeof t||!i(t)&&o(t)&&"[object String]"==n(t)}},3448:(t,e,r)=>{var n=r(4239),i=r(7005);t.exports=function(t){return"symbol"==typeof t||i(t)&&"[object Symbol]"==n(t)}},6719:(t,e,r)=>{var n=r(8749),i=r(1717),o=r(1167),a=o&&o.isTypedArray,c=a?i(a):n;t.exports=c},3674:(t,e,r)=>{var n=r(4636),i=r(280),o=r(8612);t.exports=function(t){return o(t)?n(t):i(t)}},1704:(t,e,r)=>{var n=r(4636),i=r(313),o=r(8612);t.exports=function(t){return o(t)?n(t,!0):i(t)}},928:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5161:(t,e,r)=>{var n=r(9932),i=r(7206),o=r(9199),a=r(1469);t.exports=function(t,e){return(a(t)?n:o)(t,i(e,3))}},6604:(t,e,r)=>{var n=r(9465),i=r(7816),o=r(7206);t.exports=function(t,e){var r={};return e=o(e,3),i(t,(function(t,i,o){n(r,i,e(t,i,o))})),r}},6162:(t,e,r)=>{var n=r(6029),i=r(3325),o=r(6557);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},4753:(t,e,r)=>{var n=r(6029),i=r(3325),o=r(7206);t.exports=function(t,e){return t&&t.length?n(t,o(e,2),i):void 0}},8306:(t,e,r)=>{var n=r(3369);function i(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var r=function(){var n=arguments,i=e?e.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var a=t.apply(this,n);return r.cache=o.set(i,a)||o,a};return r.cache=new(i.Cache||n),r}i.Cache=n,t.exports=i},3632:(t,e,r)=>{var n=r(6029),i=r(433),o=r(6557);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},2762:(t,e,r)=>{var n=r(6029),i=r(7206),o=r(433);t.exports=function(t,e){return t&&t.length?n(t,i(e,2),o):void 0}},308:t=>{t.exports=function(){}},7771:(t,e,r)=>{var n=r(5639);t.exports=function(){return n.Date.now()}},7557:(t,e,r)=>{var n=r(9932),i=r(5990),o=r(7406),a=r(1811),c=r(8363),u=r(696),s=r(9021),l=r(6904),f=s((function(t,e){var r={};if(null==t)return r;var s=!1;e=n(e,(function(e){return e=a(e,t),s||(s=e.length>1),e})),c(t,l(t),r),s&&(r=i(r,7,u));for(var f=e.length;f--;)o(r,e[f]);return r}));t.exports=f},9601:(t,e,r)=>{var n=r(371),i=r(9152),o=r(5403),a=r(327);t.exports=function(t){return o(t)?n(a(t)):i(t)}},6026:(t,e,r)=>{var n=r(7445)();t.exports=n},9704:(t,e,r)=>{var n=r(2908),i=r(7206),o=r(5076),a=r(1469),c=r(6612);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},9734:(t,e,r)=>{var n=r(1078),i=r(9556),o=r(5976),a=r(6612),c=o((function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),i(t,n(e,1),[])}));t.exports=c},479:t=>{t.exports=function(){return[]}},5062:t=>{t.exports=function(){return!1}},3303:(t,e,r)=>{var n=r(7206),i=r(7762);t.exports=function(t,e){return t&&t.length?i(t,n(e,2)):0}},3493:(t,e,r)=>{var n=r(3279),i=r(3218);t.exports=function(t,e,r){var o=!0,a=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return i(r)&&(o="leading"in r?!!r.leading:o,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:o,maxWait:e,trailing:a})}},8601:(t,e,r)=>{var n=r(4841),i=1/0;t.exports=function(t){return t?(t=n(t))===i||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}},554:(t,e,r)=>{var n=r(8601);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},4841:(t,e,r)=>{var n=r(7561),i=r(3218),o=r(3448),a=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,u=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(o(t))return NaN;if(i(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=i(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=c.test(t);return r||u.test(t)?s(t.slice(2),r?2:8):a.test(t)?NaN:+t}},9833:(t,e,r)=>{var n=r(531);t.exports=function(t){return null==t?"":n(t)}},5578:(t,e,r)=>{var n=r(7206),i=r(5652);t.exports=function(t,e){return t&&t.length?i(t,n(e,2)):[]}},1700:(t,e,r)=>{var n=r(8805)("toUpperCase");t.exports=n},9921:(t,e)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,c=r?Symbol.for("react.profiler"):60114,u=r?Symbol.for("react.provider"):60109,s=r?Symbol.for("react.context"):60110,l=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,p=r?Symbol.for("react.forward_ref"):60112,h=r?Symbol.for("react.suspense"):60113,d=r?Symbol.for("react.suspense_list"):60120,y=r?Symbol.for("react.memo"):60115,v=r?Symbol.for("react.lazy"):60116,m=r?Symbol.for("react.block"):60121,g=r?Symbol.for("react.fundamental"):60117,b=r?Symbol.for("react.responder"):60118,x=r?Symbol.for("react.scope"):60119;function w(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case l:case f:case o:case c:case a:case h:return t;default:switch(t=t&&t.$$typeof){case s:case p:case v:case y:case u:return t;default:return e}}case i:return e}}}function O(t){return w(t)===f}e.isFragment=function(t){return w(t)===o}},9864:(t,e,r)=>{"use strict";t.exports=r(9921)},6871:(t,e,r)=>{"use strict";function n(){var t=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=t&&this.setState(t)}function i(t){this.setState(function(e){var r=this.constructor.getDerivedStateFromProps(t,e);return null!=r?r:null}.bind(this))}function o(t,e){try{var r=this.props,n=this.state;this.props=t,this.state=e,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(r,n)}finally{this.props=r,this.state=n}}function a(t){var e=t.prototype;if(!e||!e.isReactComponent)throw new Error("Can only polyfill class components");if("function"!=typeof t.getDerivedStateFromProps&&"function"!=typeof e.getSnapshotBeforeUpdate)return t;var r=null,a=null,c=null;if("function"==typeof e.componentWillMount?r="componentWillMount":"function"==typeof e.UNSAFE_componentWillMount&&(r="UNSAFE_componentWillMount"),"function"==typeof e.componentWillReceiveProps?a="componentWillReceiveProps":"function"==typeof e.UNSAFE_componentWillReceiveProps&&(a="UNSAFE_componentWillReceiveProps"),"function"==typeof e.componentWillUpdate?c="componentWillUpdate":"function"==typeof e.UNSAFE_componentWillUpdate&&(c="UNSAFE_componentWillUpdate"),null!==r||null!==a||null!==c){var u=t.displayName||t.name,s="function"==typeof t.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+u+" uses "+s+" but also contains the following legacy lifecycles:"+(null!==r?"\n  "+r:"")+(null!==a?"\n  "+a:"")+(null!==c?"\n  "+c:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")}if("function"==typeof t.getDerivedStateFromProps&&(e.componentWillMount=n,e.componentWillReceiveProps=i),"function"==typeof e.getSnapshotBeforeUpdate){if("function"!=typeof e.componentDidUpdate)throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");e.componentWillUpdate=o;var l=e.componentDidUpdate;e.componentDidUpdate=function(t,e,r){var n=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:r;l.call(this,t,e,n)}}return t}r.r(e),r.d(e,{polyfill:()=>a}),n.__suppressDeprecationWarning=!0,i.__suppressDeprecationWarning=!0,o.__suppressDeprecationWarning=!0},129:(t,e,r)=>{"use strict";e.__esModule=!0,e.default=void 0;!function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){var n=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(t,r):{};n.get||n.set?Object.defineProperty(e,r,n):e[r]=t[r]}e.default=t}(r(7619));var n=c(r(8141)),i=c(r(602)),o=c(r(9787)),a=c(r(644));r(4726);function c(t){return t&&t.__esModule?t:{default:t}}function u(){return u=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}var s=function(t,e){return t&&e&&e.split(" ").forEach((function(e){return(0,n.default)(t,e)}))},l=function(t,e){return t&&e&&e.split(" ").forEach((function(e){return(0,i.default)(t,e)}))},f=function(t){var e,r;function n(){for(var e,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return(e=t.call.apply(t,[this].concat(n))||this).onEnter=function(t,r){var n=e.getClassNames(r?"appear":"enter").className;e.removeClasses(t,"exit"),s(t,n),e.props.onEnter&&e.props.onEnter(t,r)},e.onEntering=function(t,r){var n=e.getClassNames(r?"appear":"enter").activeClassName;e.reflowAndAddClass(t,n),e.props.onEntering&&e.props.onEntering(t,r)},e.onEntered=function(t,r){var n=e.getClassNames("appear").doneClassName,i=e.getClassNames("enter").doneClassName,o=r?n+" "+i:i;e.removeClasses(t,r?"appear":"enter"),s(t,o),e.props.onEntered&&e.props.onEntered(t,r)},e.onExit=function(t){var r=e.getClassNames("exit").className;e.removeClasses(t,"appear"),e.removeClasses(t,"enter"),s(t,r),e.props.onExit&&e.props.onExit(t)},e.onExiting=function(t){var r=e.getClassNames("exit").activeClassName;e.reflowAndAddClass(t,r),e.props.onExiting&&e.props.onExiting(t)},e.onExited=function(t){var r=e.getClassNames("exit").doneClassName;e.removeClasses(t,"exit"),s(t,r),e.props.onExited&&e.props.onExited(t)},e.getClassNames=function(t){var r=e.props.classNames,n="string"==typeof r,i=n?(n&&r?r+"-":"")+t:r[t];return{className:i,activeClassName:n?i+"-active":r[t+"Active"],doneClassName:n?i+"-done":r[t+"Done"]}},e}r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var i=n.prototype;return i.removeClasses=function(t,e){var r=this.getClassNames(e),n=r.className,i=r.activeClassName,o=r.doneClassName;n&&l(t,n),i&&l(t,i),o&&l(t,o)},i.reflowAndAddClass=function(t,e){e&&(t&&t.scrollTop,s(t,e))},i.render=function(){var t=u({},this.props);return delete t.classNames,o.default.createElement(a.default,u({},t,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},n}(o.default.Component);f.defaultProps={classNames:""},f.propTypes={};var p=f;e.default=p,t.exports=e.default},6093:(t,e,r)=>{"use strict";e.__esModule=!0,e.default=void 0;a(r(7619));var n=a(r(9787)),i=r(5156),o=a(r(2381));function a(t){return t&&t.__esModule?t:{default:t}}var c=function(t){var e,r;function a(){for(var e,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return(e=t.call.apply(t,[this].concat(n))||this).handleEnter=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e.handleLifecycle("onEnter",0,r)},e.handleEntering=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e.handleLifecycle("onEntering",0,r)},e.handleEntered=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e.handleLifecycle("onEntered",0,r)},e.handleExit=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e.handleLifecycle("onExit",1,r)},e.handleExiting=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e.handleLifecycle("onExiting",1,r)},e.handleExited=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e.handleLifecycle("onExited",1,r)},e}r=t,(e=a).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var c=a.prototype;return c.handleLifecycle=function(t,e,r){var o,a=this.props.children,c=n.default.Children.toArray(a)[e];c.props[t]&&(o=c.props)[t].apply(o,r),this.props[t]&&this.props[t]((0,i.findDOMNode)(this))},c.render=function(){var t=this.props,e=t.children,r=t.in,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,["children","in"]),a=n.default.Children.toArray(e),c=a[0],u=a[1];return delete i.onEnter,delete i.onEntering,delete i.onEntered,delete i.onExit,delete i.onExiting,delete i.onExited,n.default.createElement(o.default,i,r?n.default.cloneElement(c,{key:"first",onEnter:this.handleEnter,onEntering:this.handleEntering,onEntered:this.handleEntered}):n.default.cloneElement(u,{key:"second",onEnter:this.handleExit,onEntering:this.handleExiting,onEntered:this.handleExited}))},a}(n.default.Component);c.propTypes={};var u=c;e.default=u,t.exports=e.default},644:(t,e,r)=>{"use strict";e.__esModule=!0,e.default=e.EXITING=e.ENTERED=e.ENTERING=e.EXITED=e.UNMOUNTED=void 0;var n=function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){var n=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(t,r):{};n.get||n.set?Object.defineProperty(e,r,n):e[r]=t[r]}return e.default=t,e}(r(7619)),i=c(r(9787)),o=c(r(5156)),a=r(6871);r(4726);function c(t){return t&&t.__esModule?t:{default:t}}var u="unmounted";e.UNMOUNTED=u;var s="exited";e.EXITED=s;var l="entering";e.ENTERING=l;var f="entered";e.ENTERED=f;var p="exiting";e.EXITING=p;var h=function(t){var e,r;function n(e,r){var n;n=t.call(this,e,r)||this;var i,o=r.transitionGroup,a=o&&!o.isMounting?e.enter:e.appear;return n.appearStatus=null,e.in?a?(i=s,n.appearStatus=l):i=f:i=e.unmountOnExit||e.mountOnEnter?u:s,n.state={status:i},n.nextCallback=null,n}r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=n.prototype;return a.getChildContext=function(){return{transitionGroup:null}},n.getDerivedStateFromProps=function(t,e){return t.in&&e.status===u?{status:s}:null},a.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},a.componentDidUpdate=function(t){var e=null;if(t!==this.props){var r=this.state.status;this.props.in?r!==l&&r!==f&&(e=l):r!==l&&r!==f||(e=p)}this.updateStatus(!1,e)},a.componentWillUnmount=function(){this.cancelNextCallback()},a.getTimeouts=function(){var t,e,r,n=this.props.timeout;return t=e=r=n,null!=n&&"number"!=typeof n&&(t=n.exit,e=n.enter,r=void 0!==n.appear?n.appear:e),{exit:t,enter:e,appear:r}},a.updateStatus=function(t,e){if(void 0===t&&(t=!1),null!==e){this.cancelNextCallback();var r=o.default.findDOMNode(this);e===l?this.performEnter(r,t):this.performExit(r)}else this.props.unmountOnExit&&this.state.status===s&&this.setState({status:u})},a.performEnter=function(t,e){var r=this,n=this.props.enter,i=this.context.transitionGroup?this.context.transitionGroup.isMounting:e,o=this.getTimeouts(),a=i?o.appear:o.enter;e||n?(this.props.onEnter(t,i),this.safeSetState({status:l},(function(){r.props.onEntering(t,i),r.onTransitionEnd(t,a,(function(){r.safeSetState({status:f},(function(){r.props.onEntered(t,i)}))}))}))):this.safeSetState({status:f},(function(){r.props.onEntered(t)}))},a.performExit=function(t){var e=this,r=this.props.exit,n=this.getTimeouts();r?(this.props.onExit(t),this.safeSetState({status:p},(function(){e.props.onExiting(t),e.onTransitionEnd(t,n.exit,(function(){e.safeSetState({status:s},(function(){e.props.onExited(t)}))}))}))):this.safeSetState({status:s},(function(){e.props.onExited(t)}))},a.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},a.safeSetState=function(t,e){e=this.setNextCallback(e),this.setState(t,e)},a.setNextCallback=function(t){var e=this,r=!0;return this.nextCallback=function(n){r&&(r=!1,e.nextCallback=null,t(n))},this.nextCallback.cancel=function(){r=!1},this.nextCallback},a.onTransitionEnd=function(t,e,r){this.setNextCallback(r);var n=null==e&&!this.props.addEndListener;t&&!n?(this.props.addEndListener&&this.props.addEndListener(t,this.nextCallback),null!=e&&setTimeout(this.nextCallback,e)):setTimeout(this.nextCallback,0)},a.render=function(){var t=this.state.status;if(t===u)return null;var e=this.props,r=e.children,n=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(e,["children"]);if(delete n.in,delete n.mountOnEnter,delete n.unmountOnExit,delete n.appear,delete n.enter,delete n.exit,delete n.timeout,delete n.addEndListener,delete n.onEnter,delete n.onEntering,delete n.onEntered,delete n.onExit,delete n.onExiting,delete n.onExited,"function"==typeof r)return r(t,n);var o=i.default.Children.only(r);return i.default.cloneElement(o,n)},n}(i.default.Component);function d(){}h.contextTypes={transitionGroup:n.object},h.childContextTypes={transitionGroup:function(){}},h.propTypes={},h.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:d,onEntering:d,onEntered:d,onExit:d,onExiting:d,onExited:d},h.UNMOUNTED=0,h.EXITED=1,h.ENTERING=2,h.ENTERED=3,h.EXITING=4;var y=(0,a.polyfill)(h);e.default=y},2381:(t,e,r)=>{"use strict";e.__esModule=!0,e.default=void 0;var n=c(r(7619)),i=c(r(9787)),o=r(6871),a=r(537);function c(t){return t&&t.__esModule?t:{default:t}}function u(){return u=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}function s(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}var l=Object.values||function(t){return Object.keys(t).map((function(e){return t[e]}))},f=function(t){var e,r;function n(e,r){var n,i=(n=t.call(this,e,r)||this).handleExited.bind(s(s(n)));return n.state={handleExited:i,firstRender:!0},n}r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var o=n.prototype;return o.getChildContext=function(){return{transitionGroup:{isMounting:!this.appeared}}},o.componentDidMount=function(){this.appeared=!0,this.mounted=!0},o.componentWillUnmount=function(){this.mounted=!1},n.getDerivedStateFromProps=function(t,e){var r=e.children,n=e.handleExited;return{children:e.firstRender?(0,a.getInitialChildMapping)(t,n):(0,a.getNextChildMapping)(t,r,n),firstRender:!1}},o.handleExited=function(t,e){var r=(0,a.getChildMapping)(this.props.children);t.key in r||(t.props.onExited&&t.props.onExited(e),this.mounted&&this.setState((function(e){var r=u({},e.children);return delete r[t.key],{children:r}})))},o.render=function(){var t=this.props,e=t.component,r=t.childFactory,n=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,["component","childFactory"]),o=l(this.state.children).map(r);return delete n.appear,delete n.enter,delete n.exit,null===e?o:i.default.createElement(e,n,o)},n}(i.default.Component);f.childContextTypes={transitionGroup:n.default.object.isRequired},f.propTypes={},f.defaultProps={component:"div",childFactory:function(t){return t}};var p=(0,o.polyfill)(f);e.default=p,t.exports=e.default},4317:(t,e,r)=>{"use strict";var n=c(r(129)),i=c(r(6093)),o=c(r(2381)),a=c(r(644));function c(t){return t&&t.__esModule?t:{default:t}}t.exports={Transition:a.default,TransitionGroup:o.default,ReplaceTransition:i.default,CSSTransition:n.default}},537:(t,e,r)=>{"use strict";e.__esModule=!0,e.getChildMapping=i,e.mergeChildMappings=o,e.getInitialChildMapping=function(t,e){return i(t.children,(function(r){return(0,n.cloneElement)(r,{onExited:e.bind(null,r),in:!0,appear:a(r,"appear",t),enter:a(r,"enter",t),exit:a(r,"exit",t)})}))},e.getNextChildMapping=function(t,e,r){var c=i(t.children),u=o(e,c);return Object.keys(u).forEach((function(i){var o=u[i];if((0,n.isValidElement)(o)){var s=i in e,l=i in c,f=e[i],p=(0,n.isValidElement)(f)&&!f.props.in;!l||s&&!p?l||!s||p?l&&s&&(0,n.isValidElement)(f)&&(u[i]=(0,n.cloneElement)(o,{onExited:r.bind(null,o),in:f.props.in,exit:a(o,"exit",t),enter:a(o,"enter",t)})):u[i]=(0,n.cloneElement)(o,{in:!1}):u[i]=(0,n.cloneElement)(o,{onExited:r.bind(null,o),in:!0,exit:a(o,"exit",t),enter:a(o,"enter",t)})}})),u};var n=r(9787);function i(t,e){var r=Object.create(null);return t&&n.Children.map(t,(function(t){return t})).forEach((function(t){r[t.key]=function(t){return e&&(0,n.isValidElement)(t)?e(t):t}(t)})),r}function o(t,e){function r(r){return r in e?e[r]:t[r]}t=t||{},e=e||{};var n,i=Object.create(null),o=[];for(var a in t)a in e?o.length&&(i[a]=o,o=[]):o.push(a);var c={};for(var u in e){if(i[u])for(n=0;n<i[u].length;n++){var s=i[u][n];c[i[u][n]]=r(s)}c[u]=r(u)}for(n=0;n<o.length;n++)c[o[n]]=r(o[n]);return c}function a(t,e,r){return null!=r[e]?r[e]:t.props[e]}},4726:(t,e,r)=>{"use strict";e.__esModule=!0,e.classNamesShape=e.timeoutsShape=void 0;var n;(n=r(7619))&&n.__esModule;e.timeoutsShape=null;e.classNamesShape=null},4275:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=c(r(8336)),i=r(210),o=c(r(174)),a=c(r(3697));function c(t){return t&&t.__esModule?t:{default:t}}var u=/((?:\-[a-z]+\-)?calc)/;e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;return(0,n.default)(t).walk((function(t){if("function"===t.type&&u.test(t.value)){var r=n.default.stringify(t.nodes);if(!(r.indexOf("constant")>=0||r.indexOf("env")>=0)){var c=i.parser.parse(r),s=(0,o.default)(c,e);t.type="word",t.value=(0,a.default)(t.value,s,e)}}}),!0).toString()},t.exports=e.default},460:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,i=r(3258),o=(n=i)&&n.__esModule?n:{default:n};e.default=function(t,e,r){switch(t.type){case"LengthValue":case"AngleValue":case"TimeValue":case"FrequencyValue":case"ResolutionValue":return function(t,e,r){e.type===t.type&&(e={type:t.type,value:(0,o.default)(e.value,e.unit,t.unit,r),unit:t.unit});return{left:t,right:e}}(t,e,r);default:return{left:t,right:e}}},t.exports=e.default},174:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.flip=s;var n,i=r(460),o=(n=i)&&n.__esModule?n:{default:n};function a(t,e){return"MathExpression"===t.type?function(t,e){switch(t=function(t,e){var r=(0,o.default)(t.left,t.right,e),n=a(r.left,e),i=a(r.right,e);"MathExpression"===n.type&&"MathExpression"===i.type&&("/"===n.operator&&"*"===i.operator||"-"===n.operator&&"+"===i.operator||"*"===n.operator&&"/"===i.operator||"+"===n.operator&&"-"===i.operator)&&(c(n.right,i.right)?r=(0,o.default)(n.left,i.left,e):c(n.right,i.left)&&(r=(0,o.default)(n.left,i.right,e)),n=a(r.left,e),i=a(r.right,e));return t.left=n,t.right=i,t}(t,e),t.operator){case"+":case"-":return function(t,e){var r=t,n=r.left,i=r.right,o=r.operator;if("CssVariable"===n.type||"CssVariable"===i.type)return t;if(0===i.value)return n;if(0===n.value&&"+"===o)return i;if(0===n.value&&"-"===o)return l(i);n.type===i.type&&u(n.type)&&((t=Object.assign({},n)).value="+"===o?n.value+i.value:n.value-i.value);if(u(n.type)&&("+"===i.operator||"-"===i.operator)&&"MathExpression"===i.type){if(n.type===i.left.type)return(t=Object.assign({},t)).left=a({type:"MathExpression",operator:o,left:n,right:i.left},e),t.right=i.right,t.operator="-"===o?s(i.operator):i.operator,a(t,e);if(n.type===i.right.type)return(t=Object.assign({},t)).left=a({type:"MathExpression",operator:"-"===o?s(i.operator):i.operator,left:n,right:i.right},e),t.right=i.left,a(t,e)}if("MathExpression"===n.type&&("+"===n.operator||"-"===n.operator)&&u(i.type)){if(i.type===n.left.type)return(t=Object.assign({},n)).left=a({type:"MathExpression",operator:o,left:n.left,right:i},e),a(t,e);if(i.type===n.right.type)return t=Object.assign({},n),"-"===n.operator?(t.right=a({type:"MathExpression",operator:"-"===o?"+":"-",left:i,right:n.right},e),t.operator="-"===o?"-":"+"):t.right=a({type:"MathExpression",operator:o,left:n.right,right:i},e),t.right.value<0&&(t.right.value*=-1,t.operator="-"===t.operator?"+":"-"),a(t,e)}return t}(t,e);case"/":return function(t,e){if(!u(t.right.type))return t;if("Value"!==t.right.type)throw new Error('Cannot divide by "'+t.right.unit+'", number expected');if(0===t.right.value)throw new Error("Cannot divide by zero");if("MathExpression"===t.left.type)return u(t.left.left.type)&&u(t.left.right.type)?(t.left.left.value/=t.right.value,t.left.right.value/=t.right.value,a(t.left,e)):t;if(u(t.left.type))return t.left.value/=t.right.value,t.left;return t}(t,e);case"*":return function(t){if("MathExpression"===t.left.type&&"Value"===t.right.type){if(u(t.left.left.type)&&u(t.left.right.type))return t.left.left.value*=t.right.value,t.left.right.value*=t.right.value,t.left}else{if(u(t.left.type)&&"Value"===t.right.type)return t.left.value*=t.right.value,t.left;if("Value"===t.left.type&&"MathExpression"===t.right.type){if(u(t.right.left.type)&&u(t.right.right.type))return t.right.left.value*=t.left.value,t.right.right.value*=t.left.value,t.right}else if("Value"===t.left.type&&u(t.right.type))return t.right.value*=t.left.value,t.right}return t}(t)}return t}(t,e):"Calc"===t.type?a(t.value,e):t}function c(t,e){return t.type===e.type&&t.value===e.value}function u(t){switch(t){case"LengthValue":case"AngleValue":case"TimeValue":case"FrequencyValue":case"ResolutionValue":case"EmValue":case"ExValue":case"ChValue":case"RemValue":case"VhValue":case"VwValue":case"VminValue":case"VmaxValue":case"PercentageValue":case"Value":return!0}return!1}function s(t){return"+"===t?"-":"+"}function l(t){return u(t.type)?t.value=-t.value:"MathExpression"==t.type&&(t.left=l(t.left),t.right=l(t.right)),t}e.default=a},3697:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e,r){var n=a(e,r);return"MathExpression"===e.type&&(n=t+"("+n+")"),n};var n=r(174),i={"*":0,"/":0,"+":1,"-":1};function o(t,e){if(!1!==e){var r=Math.pow(10,e);return Math.round(t*r)/r}return t}function a(t,e){switch(t.type){case"MathExpression":var r=t.left,c=t.right,u=t.operator,s="";return"MathExpression"===r.type&&i[u]<i[r.operator]?s+="("+a(r,e)+")":s+=a(r,e),s+=" "+t.operator+" ","MathExpression"===c.type&&i[u]<i[c.operator]?s+="("+a(c,e)+")":"MathExpression"===c.type&&"-"===u&&["+","-"].includes(c.operator)?(c.operator=(0,n.flip)(c.operator),s+=a(c,e)):s+=a(c,e),s;case"Value":return o(t.value,e);case"CssVariable":return t.fallback?"var("+t.value+", "+a(t.fallback,e)+")":"var("+t.value+")";case"Calc":return t.prefix?"-"+t.prefix+"-calc("+a(t.value,e)+")":"calc("+a(t.value,e)+")";default:return o(t.value,e)+t.unit}}t.exports=e.default},210:(t,e)=>{var r=function(){function t(t,e){var r;if(Object.defineProperty(this,"name",{enumerable:!1,writable:!1,value:"JisonParserError"}),null==t&&(t="???"),Object.defineProperty(this,"message",{enumerable:!1,writable:!0,value:t}),this.hash=e,e&&e.exception instanceof Error){var n=e.exception;this.message=n.message||t,r=n.stack}r||(Error.hasOwnProperty("captureStackTrace")?Error.captureStackTrace(this,this.constructor):r=new Error(t).stack),r&&Object.defineProperty(this,"stack",{enumerable:!1,writable:!1,value:r})}function e(t,e,r){r=r||0;for(var n=0;n<e;n++)this.push(t),t+=r}function r(t,e){for(e+=t=this.length-t;t<e;t++)this.push(this[t])}function n(t){for(var e=[],r=0,n=t.length;r<n;r++){var i=t[r];"function"==typeof i?(r++,i.apply(e,t[r])):e.push(i)}return e}"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf(t.prototype,Error.prototype):t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t.prototype.name="JisonParserError";var i={trace:function(){},JisonParserError:t,yy:{},options:{type:"lalr",hasPartialLrUpgradeOnConflict:!0,errorRecoveryTokenDiscardCount:3},symbols_:{$accept:0,$end:1,ADD:3,ANGLE:16,CHS:22,COMMA:14,CSS_CPROP:13,CSS_VAR:12,DIV:6,EMS:20,EOF:1,EXS:21,FREQ:18,LENGTH:15,LPAREN:7,MUL:5,NESTED_CALC:9,NUMBER:11,PERCENTAGE:28,PREFIX:10,REMS:23,RES:19,RPAREN:8,SUB:4,TIME:17,VHS:24,VMAXS:27,VMINS:26,VWS:25,css_value:33,css_variable:32,error:2,expression:29,math_expression:30,value:31},terminals_:{1:"EOF",2:"error",3:"ADD",4:"SUB",5:"MUL",6:"DIV",7:"LPAREN",8:"RPAREN",9:"NESTED_CALC",10:"PREFIX",11:"NUMBER",12:"CSS_VAR",13:"CSS_CPROP",14:"COMMA",15:"LENGTH",16:"ANGLE",17:"TIME",18:"FREQ",19:"RES",20:"EMS",21:"EXS",22:"CHS",23:"REMS",24:"VHS",25:"VWS",26:"VMINS",27:"VMAXS",28:"PERCENTAGE"},TERROR:2,EOF:1,originalQuoteName:null,originalParseError:null,cleanupAfterParse:null,constructParseErrorInfo:null,yyMergeLocationInfo:null,__reentrant_call_depth:0,__error_infos:[],__error_recovery_infos:[],quoteName:function(t){return'"'+t+'"'},getSymbolName:function(t){if(this.terminals_[t])return this.terminals_[t];var e=this.symbols_;for(var r in e)if(e[r]===t)return r;return null},describeSymbol:function(t){if(t!==this.EOF&&this.terminal_descriptions_&&this.terminal_descriptions_[t])return this.terminal_descriptions_[t];if(t===this.EOF)return"end of input";var e=this.getSymbolName(t);return e?this.quoteName(e):null},collect_expected_token_set:function(t,e){var r=this.TERROR,n=[],i={};if(!e&&this.state_descriptions_&&this.state_descriptions_[t])return[this.state_descriptions_[t]];for(var o in this.table[t])if((o=+o)!==r){var a=e?o:this.describeSymbol(o);a&&!i[a]&&(n.push(a),i[a]=!0)}return n},productions_:function(t){for(var e=[],r=t.pop,n=t.rule,i=0,o=r.length;i<o;i++)e.push([r[i],n[i]]);return e}({pop:n([29,e,[30,10],31,31,32,32,e,[33,15]]),rule:n([2,e,[3,5],4,7,e,[1,4],2,4,6,e,[1,14],2])}),performAction:function(t,e,r){var n=this.yy;n.parser,n.lexer;switch(t){case 0:case 6:this.$=r[e-1];break;case 1:return this.$=r[e-1],r[e-1];case 2:case 3:case 4:case 5:this.$={type:"MathExpression",operator:r[e-1],left:r[e-2],right:r[e]};break;case 7:this.$={type:"Calc",value:r[e-1]};break;case 8:this.$={type:"Calc",value:r[e-1],prefix:r[e-5]};break;case 9:case 10:case 11:this.$=r[e];break;case 12:this.$={type:"Value",value:parseFloat(r[e])};break;case 13:this.$={type:"Value",value:-1*parseFloat(r[e])};break;case 14:this.$={type:"CssVariable",value:r[e-1]};break;case 15:this.$={type:"CssVariable",value:r[e-3],fallback:r[e-1]};break;case 16:this.$={type:"LengthValue",value:parseFloat(r[e]),unit:/[a-z]+/.exec(r[e])[0]};break;case 17:this.$={type:"AngleValue",value:parseFloat(r[e]),unit:/[a-z]+/.exec(r[e])[0]};break;case 18:this.$={type:"TimeValue",value:parseFloat(r[e]),unit:/[a-z]+/.exec(r[e])[0]};break;case 19:this.$={type:"FrequencyValue",value:parseFloat(r[e]),unit:/[a-z]+/.exec(r[e])[0]};break;case 20:this.$={type:"ResolutionValue",value:parseFloat(r[e]),unit:/[a-z]+/.exec(r[e])[0]};break;case 21:this.$={type:"EmValue",value:parseFloat(r[e]),unit:"em"};break;case 22:this.$={type:"ExValue",value:parseFloat(r[e]),unit:"ex"};break;case 23:this.$={type:"ChValue",value:parseFloat(r[e]),unit:"ch"};break;case 24:this.$={type:"RemValue",value:parseFloat(r[e]),unit:"rem"};break;case 25:this.$={type:"VhValue",value:parseFloat(r[e]),unit:"vh"};break;case 26:this.$={type:"VwValue",value:parseFloat(r[e]),unit:"vw"};break;case 27:this.$={type:"VminValue",value:parseFloat(r[e]),unit:"vmin"};break;case 28:this.$={type:"VmaxValue",value:parseFloat(r[e]),unit:"vmax"};break;case 29:this.$={type:"PercentageValue",value:parseFloat(r[e]),unit:"%"};break;case 30:var i=r[e];i.value*=-1,this.$=i}},table:function(t){for(var e=[],r=t.len,n=t.symbol,i=t.type,o=t.state,a=t.mode,c=t.goto,u=0,s=r.length;u<s;u++){for(var l=r[u],f={},p=0;p<l;p++){var h=n.shift();switch(i.shift()){case 2:f[h]=[a.shift(),c.shift()];break;case 0:f[h]=o.shift();break;default:f[h]=[3]}}e.push(f)}return e}({len:n([24,1,5,23,1,18,e,[0,3],1,e,[0,16],e,[23,4],r,[28,3],0,0,16,1,6,6,e,[0,3],5,1,2,r,[37,3],r,[20,3],5,0,0]),symbol:n([4,7,9,11,12,e,[15,19,1],1,1,e,[3,4,1],r,[30,19],r,[29,4],7,4,10,11,r,[22,14],r,[19,3],r,[43,22],r,[23,69],r,[139,4],8,r,[51,24],4,r,[138,15],13,r,[186,5],8,r,[6,6],r,[5,5],9,8,14,r,[159,47],r,[60,10]]),type:n([e,[2,19],e,[0,5],1,e,[2,24],e,[0,4],r,[22,19],r,[43,42],r,[23,70],r,[28,25],r,[45,25],r,[113,54]]),state:n([1,2,8,6,7,30,r,[4,3],33,37,r,[5,3],38,r,[4,3],39,r,[4,3],40,r,[4,3],42,r,[21,4],50,r,[5,3],51,r,[4,3]]),mode:n([e,[1,179],e,[2,3],r,[5,5],r,[6,4],e,[1,57]]),goto:n([5,3,4,24,e,[9,15,1],e,[25,5,1],r,[24,19],31,35,32,34,r,[18,14],36,r,[38,19],r,[19,57],r,[118,4],41,r,[24,19],43,35,r,[16,14],44,e,[2,3],28,29,2,e,[3,3],28,29,3,r,[53,4],e,[45,5,1],r,[100,42],52,r,[5,4],53])}),defaultActions:function(t){for(var e={},r=t.idx,n=t.goto,i=0,o=r.length;i<o;i++){e[r[i]]=n[i]}return e}({idx:n([6,7,8,e,[10,16,1],33,34,39,40,41,45,47,52,53]),goto:n([9,10,11,e,[16,14,1],12,1,30,13,e,[4,4,1],14,15,8])}),parseError:function(t,e,r){if(!e.recoverable)throw"function"==typeof this.trace&&this.trace(t),r||(r=this.JisonParserError),new r(t,e);"function"==typeof this.trace&&this.trace(t),e.destroy()},parse:function(t){var e,r=this,n=new Array(128),i=new Array(128),o=new Array(128),a=this.table,c=0,u=0,s=(this.TERROR,this.EOF),l=(this.options.errorRecoveryTokenDiscardCount,[0,54]);e=this.__lexer__?this.__lexer__:this.__lexer__=Object.create(this.lexer);var f={parseError:void 0,quoteName:void 0,lexer:void 0,parser:void 0,pre_parse:void 0,post_parse:void 0,pre_lex:void 0,post_lex:void 0};function p(){var t=e.fastLex();return"number"!=typeof t&&(t=r.symbols_[t]||t),t||s}"function"!=typeof assert||assert,this.yyGetSharedState=function(){return f},function(t,e){for(var r in e)void 0===t[r]&&Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}(f,this.yy),f.lexer=e,f.parser=this,"function"==typeof f.parseError?this.parseError=function(t,e,r){return r||(r=this.JisonParserError),f.parseError.call(this,t,e,r)}:this.parseError=this.originalParseError,"function"==typeof f.quoteName?this.quoteName=function(t){return f.quoteName.call(this,t)}:this.quoteName=this.originalQuoteName,this.cleanupAfterParse=function(t,r,a){var u,s;r&&((f.post_parse||this.post_parse)&&(s=this.constructParseErrorInfo(null,null,null,!1)),f.post_parse&&void 0!==(u=f.post_parse.call(this,f,t,s))&&(t=u),this.post_parse&&void 0!==(u=this.post_parse.call(this,f,t,s))&&(t=u),s&&s.destroy&&s.destroy());if(this.__reentrant_call_depth>1)return t;if(e.cleanupAfterLex&&e.cleanupAfterLex(a),f&&(f.lexer=void 0,f.parser=void 0,e.yy===f&&(e.yy=void 0)),f=void 0,this.parseError=this.originalParseError,this.quoteName=this.originalQuoteName,n.length=0,i.length=0,o.length=0,c=0,!a){for(var l=this.__error_infos.length-1;l>=0;l--){var p=this.__error_infos[l];p&&"function"==typeof p.destroy&&p.destroy()}this.__error_infos.length=0}return t},this.constructParseErrorInfo=function(t,r,a,s){var l={errStr:t,exception:r,text:e.match,value:e.yytext,token:this.describeSymbol(u)||u,token_id:u,line:e.yylineno,expected:a,recoverable:s,state:h,action:d,new_state:x,symbol_stack:n,state_stack:i,value_stack:o,stack_pointer:c,yy:f,lexer:e,parser:this,destroy:function(){var t=!!this.recoverable;for(var e in this)this.hasOwnProperty(e)&&"object"==typeof e&&(this[e]=void 0);this.recoverable=t}};return this.__error_infos.push(l),l};var h,d,y,v,m,g,b,x,w=function(){var t=e.lex();return"number"!=typeof t&&(t=r.symbols_[t]||t),t||s},O={$:!0,_$:void 0,yy:f},S=!1;try{if(this.__reentrant_call_depth++,e.setInput(t,f),"function"==typeof e.canIUse)e.canIUse().fastLex&&(w=p);for(o[c]=null,i[c]=0,n[c]=0,++c,this.pre_parse&&this.pre_parse.call(this,f),f.pre_parse&&f.pre_parse.call(this,f),x=i[c-1];;){if(h=x,this.defaultActions[h])d=2,x=this.defaultActions[h];else if(u||(u=w()),v=a[h]&&a[h][u]||l,x=v[1],!(d=v[0])){var A,j=this.describeSymbol(u)||u,E=this.collect_expected_token_set(h);A="number"==typeof e.yylineno?"Parse error on line "+(e.yylineno+1)+": ":"Parse error: ","function"==typeof e.showPosition&&(A+="\n"+e.showPosition(69,10)+"\n"),E.length?A+="Expecting "+E.join(", ")+", got unexpected "+j:A+="Unexpected "+j,m=this.constructParseErrorInfo(A,null,E,!1),void 0!==(y=this.parseError(m.errStr,m,this.JisonParserError))&&(S=y);break}switch(d){default:if(d instanceof Array){m=this.constructParseErrorInfo("Parse Error: multiple actions possible at state: "+h+", token: "+u,null,null,!1),void 0!==(y=this.parseError(m.errStr,m,this.JisonParserError))&&(S=y);break}m=this.constructParseErrorInfo("Parsing halted. No viable error recovery approach available due to internal system failure.",null,null,!1),void 0!==(y=this.parseError(m.errStr,m,this.JisonParserError))&&(S=y);break;case 1:n[c]=u,o[c]=e.yytext,i[c]=x,++c,u=0;continue;case 2:if(g=(b=this.productions_[x-1])[1],void 0!==(y=this.performAction.call(O,x,c-1,o))){S=y;break}c-=g;var k=b[0];n[c]=k,o[c]=O.$,x=a[i[c-1]][k],i[c]=x,++c;continue;case 3:-2!==c&&(S=!0,c--,void 0!==o[c]&&(S=o[c]))}break}}catch(t){if(t instanceof this.JisonParserError)throw t;if(e&&"function"==typeof e.JisonLexerError&&t instanceof e.JisonLexerError)throw t;m=this.constructParseErrorInfo("Parsing aborted due to exception.",t,null,!1),S=!1,void 0!==(y=this.parseError(m.errStr,m,this.JisonParserError))&&(S=y)}finally{S=this.cleanupAfterParse(S,!0,!0),this.__reentrant_call_depth--}return S}};i.originalParseError=i.parseError,i.originalQuoteName=i.quoteName;var o=function(){function t(t,e){var r;if(Object.defineProperty(this,"name",{enumerable:!1,writable:!1,value:"JisonLexerError"}),null==t&&(t="???"),Object.defineProperty(this,"message",{enumerable:!1,writable:!0,value:t}),this.hash=e,e&&e.exception instanceof Error){var n=e.exception;this.message=n.message||t,r=n.stack}r||(Error.hasOwnProperty("captureStackTrace")?Error.captureStackTrace(this,this.constructor):r=new Error(t).stack),r&&Object.defineProperty(this,"stack",{enumerable:!1,writable:!1,value:r})}"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf(t.prototype,Error.prototype):t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t.prototype.name="JisonLexerError";var e={EOF:1,ERROR:2,__currentRuleSet__:null,__error_infos:[],__decompressed:!1,done:!1,_backtrack:!1,_input:"",_more:!1,_signaled_error_token:!1,conditionStack:[],match:"",matched:"",matches:!1,yytext:"",offset:0,yyleng:0,yylineno:0,yylloc:null,constructLexErrorInfo:function(t,e,r){if(t=""+t,null==r&&(r=!(t.indexOf("\n")>0&&t.indexOf("^")>0)),this.yylloc&&r)if("function"==typeof this.prettyPrintRange){this.prettyPrintRange(this.yylloc);/\n\s*$/.test(t)||(t+="\n"),t+="\n  Erroneous area:\n"+this.prettyPrintRange(this.yylloc)}else if("function"==typeof this.showPosition){var n=this.showPosition();n&&(t.length&&"\n"!==t[t.length-1]&&"\n"!==n[0]?t+="\n"+n:t+=n)}var i={errStr:t,recoverable:!!e,text:this.match,token:null,line:this.yylineno,loc:this.yylloc,yy:this.yy,lexer:this,destroy:function(){var t=!!this.recoverable;for(var e in this)this.hasOwnProperty(e)&&"object"==typeof e&&(this[e]=void 0);this.recoverable=t}};return this.__error_infos.push(i),i},parseError:function(t,e,r){if(r||(r=this.JisonLexerError),this.yy){if(this.yy.parser&&"function"==typeof this.yy.parser.parseError)return this.yy.parser.parseError.call(this,t,e,r)||this.ERROR;if("function"==typeof this.yy.parseError)return this.yy.parseError.call(this,t,e,r)||this.ERROR}throw new r(t,e)},yyerror:function(t){var e="";this.yylloc&&(e=" on line "+(this.yylineno+1));var r=this.constructLexErrorInfo("Lexical error"+e+": "+t,this.options.lexerErrorsAreRecoverable),n=Array.prototype.slice.call(arguments,1);return n.length&&(r.extra_error_attributes=n),this.parseError(r.errStr,r,this.JisonLexerError)||this.ERROR},cleanupAfterLex:function(t){if(this.setInput("",{}),!t){for(var e=this.__error_infos.length-1;e>=0;e--){var r=this.__error_infos[e];r&&"function"==typeof r.destroy&&r.destroy()}this.__error_infos.length=0}return this},clear:function(){this.yytext="",this.yyleng=0,this.match="",this.matches=!1,this._more=!1,this._backtrack=!1;var t=this.yylloc?this.yylloc.last_column:0;this.yylloc={first_line:this.yylineno+1,first_column:t,last_line:this.yylineno+1,last_column:t,range:[this.offset,this.offset]}},setInput:function(t,e){if(this.yy=e||this.yy||{},!this.__decompressed){for(var r=this.rules,n=0,i=r.length;n<i;n++){"number"==typeof(p=r[n])&&(r[n]=r[p])}var o=this.conditions;for(var a in o){var c=o[a],u=c.rules,s=(i=u.length,new Array(i+1)),l=new Array(i+1);for(n=0;n<i;n++){var f=u[n],p=r[f];s[n+1]=p,l[n+1]=f}c.rules=l,c.__rule_regexes=s,c.__rule_count=i}this.__decompressed=!0}return this._input=t||"",this.clear(),this._signaled_error_token=!1,this.done=!1,this.yylineno=0,this.matched="",this.conditionStack=["INITIAL"],this.__currentRuleSet__=null,this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0,range:[0,0]},this.offset=0,this},editRemainingInput:function(t,e){var r=t.call(this,this._input,e);return"string"!=typeof r?r&&(this._input=""+r):this._input=r,this},input:function(){if(!this._input)return null;var t=this._input[0];this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t;var e=1,r=!1;if("\n"===t)r=!0;else if("\r"===t){r=!0;var n=this._input[1];"\n"===n&&(e++,t+=n,this.yytext+=n,this.yyleng++,this.offset++,this.match+=n,this.matched+=n,this.yylloc.range[1]++)}return r?(this.yylineno++,this.yylloc.last_line++,this.yylloc.last_column=0):this.yylloc.last_column++,this.yylloc.range[1]++,this._input=this._input.slice(e),t},unput:function(t){var e=t.length,r=t.split(/(?:\r\n?|\n)/g);if(this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.yyleng=this.yytext.length,this.offset-=e,this.match=this.match.substr(0,this.match.length-e),this.matched=this.matched.substr(0,this.matched.length-e),r.length>1){this.yylineno-=r.length-1,this.yylloc.last_line=this.yylineno+1;var n=this.match,i=n.split(/(?:\r\n?|\n)/g);1===i.length&&(i=(n=this.matched).split(/(?:\r\n?|\n)/g)),this.yylloc.last_column=i[i.length-1].length}else this.yylloc.last_column-=e;return this.yylloc.range[1]=this.yylloc.range[0]+this.yyleng,this.done=!1,this},more:function(){return this._more=!0,this},reject:function(){if(this.options.backtrack_lexer)this._backtrack=!0;else{var t="";this.yylloc&&(t=" on line "+(this.yylineno+1));var e=this.constructLexErrorInfo("Lexical error"+t+": You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).",!1);this._signaled_error_token=this.parseError(e.errStr,e,this.JisonLexerError)||this.ERROR}return this},less:function(t){return this.unput(this.match.slice(t))},pastInput:function(t,e){var r=this.matched.substring(0,this.matched.length-this.match.length);t<0?t=r.length:t||(t=20),e<0?e=r.length:e||(e=1);var n=(r=r.substr(2*-t-2)).replace(/\r\n|\r/g,"\n").split("\n");return(r=(n=n.slice(-e)).join("\n")).length>t&&(r="..."+r.substr(-t)),r},upcomingInput:function(t,e){var r=this.match;t<0?t=r.length+this._input.length:t||(t=20),e<0?e=t:e||(e=1),r.length<2*t+2&&(r+=this._input.substring(0,2*t+2));var n=r.replace(/\r\n|\r/g,"\n").split("\n");return(r=(n=n.slice(0,e)).join("\n")).length>t&&(r=r.substring(0,t)+"..."),r},showPosition:function(t,e){var r=this.pastInput(t).replace(/\s/g," "),n=new Array(r.length+1).join("-");return r+this.upcomingInput(e).replace(/\s/g," ")+"\n"+n+"^"},deriveLocationInfo:function(t,e,r,n){var i={first_line:1,first_column:0,last_line:1,last_column:0,range:[0,0]};return t&&(i.first_line=0|t.first_line,i.last_line=0|t.last_line,i.first_column=0|t.first_column,i.last_column=0|t.last_column,t.range&&(i.range[0]=0|t.range[0],i.range[1]=0|t.range[1])),(i.first_line<=0||i.last_line<i.first_line)&&(i.first_line<=0&&e&&(i.first_line=0|e.last_line,i.first_column=0|e.last_column,e.range&&(i.range[0]=0|t.range[1])),(i.last_line<=0||i.last_line<i.first_line)&&r&&(i.last_line=0|r.first_line,i.last_column=0|r.first_column,r.range&&(i.range[1]=0|t.range[0])),i.first_line<=0&&n&&(i.last_line<=0||n.last_line<=i.last_line)&&(i.first_line=0|n.first_line,i.first_column=0|n.first_column,n.range&&(i.range[0]=0|n.range[0])),i.last_line<=0&&n&&(i.first_line<=0||n.first_line>=i.first_line)&&(i.last_line=0|n.last_line,i.last_column=0|n.last_column,n.range&&(i.range[1]=0|n.range[1]))),i.last_line<=0&&(i.first_line<=0?(i.first_line=this.yylloc.first_line,i.last_line=this.yylloc.last_line,i.first_column=this.yylloc.first_column,i.last_column=this.yylloc.last_column,i.range[0]=this.yylloc.range[0],i.range[1]=this.yylloc.range[1]):(i.last_line=this.yylloc.last_line,i.last_column=this.yylloc.last_column,i.range[1]=this.yylloc.range[1])),i.first_line<=0&&(i.first_line=i.last_line,i.first_column=0,i.range[1]=i.range[0]),i.first_column<0&&(i.first_column=0),i.last_column<0&&(i.last_column=i.first_column>0?i.first_column:80),i},prettyPrintRange:function(t,e,r){t=this.deriveLocationInfo(t,e,r);var n=(this.matched+this._input).split("\n"),i=Math.max(1,e?e.first_line:t.first_line-3),o=Math.max(1,r?r.last_line:t.last_line+1),a=1+Math.log10(1|o)|0,c=new Array(a).join(" "),u=[],s=n.slice(i-1,o+1).map((function(e,r){var n=r+i,o=(c+n).substr(-a)+": "+e,s=new Array(a+1).join("^"),l=3,f=0;(n===t.first_line?(l+=t.first_column,f=Math.max(2,(n===t.last_line?t.last_column:e.length)-t.first_column+1)):n===t.last_line?f=Math.max(2,t.last_column+1):n>t.first_line&&n<t.last_line&&(f=Math.max(2,e.length+1)),f)&&(o+="\n"+s+new Array(l).join(".")+new Array(f).join("^"),e.trim().length>0&&u.push(r));return o=o.replace(/\t/g," ")}));if(u.length>4){var l=u[1]+1,f=u[u.length-2]-1,p=new Array(a+1).join(" ")+"  (...continued...)";p+="\n"+new Array(a+1).join("-")+"  (---------------)",s.splice(l,f-l+1,p)}return s.join("\n")},describeYYLLOC:function(t,e){var r,n=t.first_line,i=t.last_line,o=t.first_column,a=t.last_column;if(0===i-n?(r="line "+n+", ",r+=a-o<=1?"column "+o:"columns "+o+" .. "+a):r="lines "+n+"(column "+o+") .. "+i+"(column "+a+")",t.range&&e){var c=t.range[0],u=t.range[1]-1;r+=u<=c?" {String Offset: "+c+"}":" {String Offset range: "+c+" .. "+u+"}"}return r},test_match:function(t,e){var r,n,i,o,a;if(this.options.backtrack_lexer&&(i={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.yylloc.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column,range:this.yylloc.range.slice(0)},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done}),a=(o=t[0]).length,(n=o.split(/(?:\r\n?|\n)/g)).length>1?(this.yylineno+=n.length-1,this.yylloc.last_line=this.yylineno+1,this.yylloc.last_column=n[n.length-1].length):this.yylloc.last_column+=a,this.yytext+=o,this.match+=o,this.matched+=o,this.matches=t,this.yyleng=this.yytext.length,this.yylloc.range[1]+=a,this.offset+=a,this._more=!1,this._backtrack=!1,this._input=this._input.slice(a),r=this.performAction.call(this,this.yy,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),r)return r;if(this._backtrack){for(var c in i)this[c]=i[c];return this.__currentRuleSet__=null,!1}return!!this._signaled_error_token&&(r=this._signaled_error_token,this._signaled_error_token=!1,r)},next:function(){if(this.done)return this.clear(),this.EOF;var t,e,r,n;this._input||(this.done=!0),this._more||this.clear();var i=this.__currentRuleSet__;if(!(i||(i=this.__currentRuleSet__=this._currentRules())&&i.rules)){var o="";this.options.trackPosition&&(o=" on line "+(this.yylineno+1));var a=this.constructLexErrorInfo("Internal lexer engine error"+o+': The lex grammar programmer pushed a non-existing condition name "'+this.topState()+'"; this is a fatal error and should be reported to the application programmer team!',!1);return this.parseError(a.errStr,a,this.JisonLexerError)||this.ERROR}for(var c=i.rules,u=i.__rule_regexes,s=i.__rule_count,l=1;l<=s;l++)if((r=this._input.match(u[l]))&&(!e||r[0].length>e[0].length)){if(e=r,n=l,this.options.backtrack_lexer){if(!1!==(t=this.test_match(r,c[l])))return t;if(this._backtrack){e=void 0;continue}return!1}if(!this.options.flex)break}if(e)return!1!==(t=this.test_match(e,c[n]))&&t;if(this._input){o="";this.options.trackPosition&&(o=" on line "+(this.yylineno+1));a=this.constructLexErrorInfo("Lexical error"+o+": Unrecognized text.",this.options.lexerErrorsAreRecoverable);var f=this._input,p=this.topState(),h=this.conditionStack.length;return(t=this.parseError(a.errStr,a,this.JisonLexerError)||this.ERROR)===this.ERROR&&(this.matches||f!==this._input||p!==this.topState()||h!==this.conditionStack.length||this.input()),t}return this.done=!0,this.clear(),this.EOF},lex:function(){var t;for("function"==typeof this.pre_lex&&(t=this.pre_lex.call(this,0)),"function"==typeof this.options.pre_lex&&(t=this.options.pre_lex.call(this,t)||t),this.yy&&"function"==typeof this.yy.pre_lex&&(t=this.yy.pre_lex.call(this,t)||t);!t;)t=this.next();return this.yy&&"function"==typeof this.yy.post_lex&&(t=this.yy.post_lex.call(this,t)||t),"function"==typeof this.options.post_lex&&(t=this.options.post_lex.call(this,t)||t),"function"==typeof this.post_lex&&(t=this.post_lex.call(this,t)||t),t},fastLex:function(){for(var t;!t;)t=this.next();return t},canIUse:function(){return{fastLex:!("function"==typeof this.pre_lex||"function"==typeof this.options.pre_lex||this.yy&&"function"==typeof this.yy.pre_lex||this.yy&&"function"==typeof this.yy.post_lex||"function"==typeof this.options.post_lex||"function"==typeof this.post_lex)&&"function"==typeof this.fastLex}},begin:function(t){return this.pushState(t)},pushState:function(t){return this.conditionStack.push(t),this.__currentRuleSet__=null,this},popState:function(){return this.conditionStack.length-1>0?(this.__currentRuleSet__=null,this.conditionStack.pop()):this.conditionStack[0]},topState:function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]]:this.conditions.INITIAL},stateStackSize:function(){return this.conditionStack.length},options:{trackPosition:!0},JisonLexerError:t,performAction:function(t,e,r){if(1!==e)return this.simpleCaseActionClusters[e]},simpleCaseActionClusters:{0:13,2:5,3:6,4:3,5:4,6:15,7:15,8:15,9:15,10:15,11:15,12:16,13:16,14:16,15:16,16:17,17:17,18:18,19:18,20:19,21:19,22:19,23:20,24:21,25:22,26:23,27:25,28:24,29:26,30:27,31:28,32:11,33:9,34:12,35:10,36:7,37:8,38:14,39:1},rules:[/^(?:(--[\d\-A-Za-z]*))/,/^(?:\s+)/,/^(?:\*)/,/^(?:\/)/,/^(?:\+)/,/^(?:-)/,/^(?:(\d+(\.\d*)?|\.\d+)px\b)/,/^(?:(\d+(\.\d*)?|\.\d+)cm\b)/,/^(?:(\d+(\.\d*)?|\.\d+)mm\b)/,/^(?:(\d+(\.\d*)?|\.\d+)in\b)/,/^(?:(\d+(\.\d*)?|\.\d+)pt\b)/,/^(?:(\d+(\.\d*)?|\.\d+)pc\b)/,/^(?:(\d+(\.\d*)?|\.\d+)deg\b)/,/^(?:(\d+(\.\d*)?|\.\d+)grad\b)/,/^(?:(\d+(\.\d*)?|\.\d+)rad\b)/,/^(?:(\d+(\.\d*)?|\.\d+)turn\b)/,/^(?:(\d+(\.\d*)?|\.\d+)s\b)/,/^(?:(\d+(\.\d*)?|\.\d+)ms\b)/,/^(?:(\d+(\.\d*)?|\.\d+)Hz\b)/,/^(?:(\d+(\.\d*)?|\.\d+)kHz\b)/,/^(?:(\d+(\.\d*)?|\.\d+)dpi\b)/,/^(?:(\d+(\.\d*)?|\.\d+)dpcm\b)/,/^(?:(\d+(\.\d*)?|\.\d+)dppx\b)/,/^(?:(\d+(\.\d*)?|\.\d+)em\b)/,/^(?:(\d+(\.\d*)?|\.\d+)ex\b)/,/^(?:(\d+(\.\d*)?|\.\d+)ch\b)/,/^(?:(\d+(\.\d*)?|\.\d+)rem\b)/,/^(?:(\d+(\.\d*)?|\.\d+)vw\b)/,/^(?:(\d+(\.\d*)?|\.\d+)vh\b)/,/^(?:(\d+(\.\d*)?|\.\d+)vmin\b)/,/^(?:(\d+(\.\d*)?|\.\d+)vmax\b)/,/^(?:(\d+(\.\d*)?|\.\d+)%)/,/^(?:(\d+(\.\d*)?|\.\d+)\b)/,/^(?:(calc))/,/^(?:(var))/,/^(?:([a-z]+))/,/^(?:\()/,/^(?:\))/,/^(?:,)/,/^(?:$)/],conditions:{INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39],inclusive:!0}}};return e}();function a(){this.yy={}}return i.lexer=o,a.prototype=i,i.Parser=a,new a}();e.parser=r,e.Parser=r.Parser,e.parse=function(){return r.parse.apply(r,arguments)}},8336:(t,e,r)=>{var n=r(8219),i=r(6461),o=r(1088);function a(t){return this instanceof a?(this.nodes=n(t),this):new a(t)}a.prototype.toString=function(){return Array.isArray(this.nodes)?o(this.nodes):""},a.prototype.walk=function(t,e){return i(this.nodes,t,e),this},a.unit=r(4386),a.walk=i,a.stringify=o,t.exports=a},8219:t=>{var e="(".charCodeAt(0),r=")".charCodeAt(0),n="'".charCodeAt(0),i='"'.charCodeAt(0),o="\\".charCodeAt(0),a="/".charCodeAt(0),c=",".charCodeAt(0),u=":".charCodeAt(0),s="*".charCodeAt(0);t.exports=function(t){for(var l,f,p,h,d,y,v,m,g=[],b=t,x=0,w=b.charCodeAt(x),O=b.length,S=[{nodes:g}],A=0,j="",E="",k="";x<O;)if(w<=32){l=x;do{l+=1,w=b.charCodeAt(l)}while(w<=32);h=b.slice(x,l),p=g[g.length-1],w===r&&A?k=h:p&&"div"===p.type?p.after=h:w===c||w===u||w===a&&b.charCodeAt(l+1)!==s?E=h:g.push({type:"space",sourceIndex:x,value:h}),x=l}else if(w===n||w===i){l=x,h={type:"string",sourceIndex:x,quote:f=w===n?"'":'"'};do{if(d=!1,~(l=b.indexOf(f,l+1)))for(y=l;b.charCodeAt(y-1)===o;)y-=1,d=!d;else l=(b+=f).length-1,h.unclosed=!0}while(d);h.value=b.slice(x+1,l),g.push(h),x=l+1,w=b.charCodeAt(x)}else if(w===a&&b.charCodeAt(x+1)===s)h={type:"comment",sourceIndex:x},-1===(l=b.indexOf("*/",x))&&(h.unclosed=!0,l=b.length),h.value=b.slice(x+2,l),g.push(h),x=l+2,w=b.charCodeAt(x);else if(w===a||w===c||w===u)h=b[x],g.push({type:"div",sourceIndex:x-E.length,value:h,before:E,after:""}),E="",x+=1,w=b.charCodeAt(x);else if(e===w){l=x;do{l+=1,w=b.charCodeAt(l)}while(w<=32);if(h={type:"function",sourceIndex:x-j.length,value:j,before:b.slice(x+1,l)},x=l,"url"===j&&w!==n&&w!==i){l-=1;do{if(d=!1,~(l=b.indexOf(")",l+1)))for(y=l;b.charCodeAt(y-1)===o;)y-=1,d=!d;else l=(b+=")").length-1,h.unclosed=!0}while(d);v=l;do{v-=1,w=b.charCodeAt(v)}while(w<=32);h.nodes=x!==v+1?[{type:"word",sourceIndex:x,value:b.slice(x,v+1)}]:[],h.unclosed&&v+1!==l?(h.after="",h.nodes.push({type:"space",sourceIndex:v+1,value:b.slice(v+1,l)})):h.after=b.slice(v+1,l),x=l+1,w=b.charCodeAt(x),g.push(h)}else A+=1,h.after="",g.push(h),S.push(h),g=h.nodes=[],m=h;j=""}else if(r===w&&A)x+=1,w=b.charCodeAt(x),m.after=k,k="",A-=1,S.pop(),g=(m=S[A]).nodes;else{l=x;do{w===o&&(l+=1),l+=1,w=b.charCodeAt(l)}while(l<O&&!(w<=32||w===n||w===i||w===c||w===u||w===a||w===e||w===r&&A));h=b.slice(x,l),e===w?j=h:g.push({type:"word",sourceIndex:x,value:h}),x=l}for(x=S.length-1;x;x-=1)S[x].unclosed=!0;return S[0].nodes}},1088:t=>{function e(t,e){var n,i,o=t.type,a=t.value;return e&&void 0!==(i=e(t))?i:"word"===o||"space"===o?a:"string"===o?(n=t.quote||"")+a+(t.unclosed?"":n):"comment"===o?"/*"+a+(t.unclosed?"":"*/"):"div"===o?(t.before||"")+a+(t.after||""):Array.isArray(t.nodes)?(n=r(t.nodes),"function"!==o?n:a+"("+(t.before||"")+n+(t.after||"")+(t.unclosed?"":")")):a}function r(t,r){var n,i;if(Array.isArray(t)){for(n="",i=t.length-1;~i;i-=1)n=e(t[i],r)+n;return n}return e(t,r)}t.exports=r},4386:t=>{var e="-".charCodeAt(0),r="+".charCodeAt(0),n=".".charCodeAt(0),i="e".charCodeAt(0),o="E".charCodeAt(0);t.exports=function(t){for(var a,c=0,u=t.length,s=!1,l=-1,f=!1;c<u;){if((a=t.charCodeAt(c))>=48&&a<=57)f=!0;else if(a===i||a===o){if(l>-1)break;l=c}else if(a===n){if(s)break;s=!0}else{if(a!==r&&a!==e)break;if(0!==c)break}c+=1}return l+1===c&&c--,!!f&&{number:t.slice(0,c),unit:t.slice(c)}}},6461:t=>{t.exports=function t(e,r,n){var i,o,a,c;for(i=0,o=e.length;i<o;i+=1)a=e[i],n||(c=r(a,i,e)),!1!==c&&"function"===a.type&&Array.isArray(a.nodes)&&t(a.nodes,r,n),n&&r(a,i,e)}},7619:t=>{"use strict";t.exports=e},9787:e=>{"use strict";e.exports=t},5156:t=>{"use strict";t.exports=r},4836:t=>{t.exports=function(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports.default=t.exports}},i={};function o(t){var e=i[t];if(void 0!==e)return e.exports;var r=i[t]={id:t,loaded:!1,exports:{}};return n[t].call(r.exports,r,r.exports,o),r.loaded=!0,r.exports}o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var r in e)o.o(e,r)&&!o.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.nmd=t=>(t.paths=[],t.children||(t.children=[]),t);var a={};return(()=>{"use strict";o.r(a),o.d(a,{Area:()=>gm,AreaChart:()=>Bb,Bar:()=>Oy,BarChart:()=>Cg,Brush:()=>Qd,CartesianAxis:()=>Sv,CartesianGrid:()=>Bv,Cell:()=>Un,ComposedChart:()=>Fb,Cross:()=>Gp,Curve:()=>Pp,Customized:()=>Vf,DefaultLegendContent:()=>le,DefaultTooltipContent:()=>ln,Dot:()=>Vp,ErrorBar:()=>xl,Funnel:()=>cx,FunnelChart:()=>ux,Global:()=>fn,Label:()=>Af,LabelList:()=>zf,Layer:()=>ht,Legend:()=>ke,Line:()=>rm,LineChart:()=>Tg,Pie:()=>td,PieChart:()=>Ig,PolarAngleAxis:()=>zh,PolarGrid:()=>ih,PolarRadiusAxis:()=>Ah,Polygon:()=>Wp,Radar:()=>vd,RadarChart:()=>Rb,RadialBar:()=>Cd,RadialBarChart:()=>zb,Rectangle:()=>Np,ReferenceArea:()=>Qy,ReferenceDot:()=>Gy,ReferenceLine:()=>Uy,ResponsiveContainer:()=>Fn,Sankey:()=>Db,Scatter:()=>Im,ScatterChart:()=>Lb,Sector:()=>Xf,Surface:()=>st,Symbols:()=>Jt,Text:()=>pi,Tooltip:()=>wn,Trapezoid:()=>Hb,Treemap:()=>ib,XAxis:()=>Nm,YAxis:()=>Dm,ZAxis:()=>bm});var t={};o.r(t),o.d(t,{scaleBand:()=>Ci,scaleDiverging:()=>Ms,scaleDivergingLog:()=>Ts,scaleDivergingPow:()=>Is,scaleDivergingSqrt:()=>Ns,scaleDivergingSymlog:()=>Cs,scaleIdentity:()=>Oa,scaleImplicit:()=>Mi,scaleLinear:()=>wa,scaleLog:()=>Ta,scaleOrdinal:()=>Ti,scalePoint:()=>Ni,scalePow:()=>Fa,scaleQuantile:()=>Qa,scaleQuantize:()=>tc,scaleRadial:()=>Va,scaleSequential:()=>Os,scaleSequentialLog:()=>Ss,scaleSequentialPow:()=>js,scaleSequentialQuantile:()=>ks,scaleSequentialSqrt:()=>Es,scaleSequentialSymlog:()=>As,scaleSqrt:()=>Ua,scaleSymlog:()=>Da,scaleThreshold:()=>ec,scaleTime:()=>gs,scaleUtc:()=>bs,tickFormat:()=>ba});var e=o(9787),r=o.n(e),n=o(4184),i=o.n(n),c=o(3218),u=o.n(c),s=o(3560),l=o.n(s),f=o(7037),p=o.n(f),h=o(7361),d=o.n(h),y=o(4293),v=o.n(y),m=o(1469),g=o.n(m),b=o(9864),x=o(7654),w=o.n(x),O=o(1763),S=o.n(O),A=function(t){return 0===t?0:t>0?1:-1},j=function(t){return p()(t)&&t.indexOf("%")===t.length-1},E=function(t){return S()(t)&&!w()(t)},k=function(t){return E(t)||p()(t)},P=0,_=function(t){var e=++P;return"".concat(t||"").concat(e)},M=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!E(t)&&!p()(t))return n;if(j(t)){var o=t.indexOf("%");r=e*parseFloat(t.slice(0,o))/100}else r=+t;return w()(r)&&(r=n),i&&r>e&&(r=e),r},T=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},C=function(t,e){return E(t)&&E(e)?function(r){return t+r*(e-t)}:function(){return e}};function I(t,e,r){return t&&t.length?t.find((function(t){return t&&("function"==typeof e?e(t):d()(t,e))===r})):null}function N(t,e){for(var r in t)if({}.hasOwnProperty.call(t,r)&&(!{}.hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if({}.hasOwnProperty.call(e,n)&&!{}.hasOwnProperty.call(t,n))return!1;return!0}function D(t){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},D(t)}var R=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],L=["points","pathLength"],B={svg:["viewBox","children"],polygon:L,polyline:L},z=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],F=function(t,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,e.isValidElement)(t)&&(n=t.props),!u()(n))return null;var i={};return Object.keys(n).forEach((function(t){z.includes(t)&&(i[t]=r||function(e){return n[t](n,e)})})),i},U=function(t,e,r){if(!u()(t)||"object"!==D(t))return null;var n=null;return Object.keys(t).forEach((function(i){var o=t[i];z.includes(i)&&"function"==typeof o&&(n||(n={}),n[i]=function(t,e,r){return function(n){return t(e,r,n),null}}(o,e,r))})),n},W=["children"],$=["children"];function V(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var H={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"},q=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},G=null,X=null,K=function t(r){if(r===G&&g()(X))return X;var n=[];return e.Children.forEach(r,(function(e){v()(e)||((0,b.isFragment)(e)?n=n.concat(t(e.props.children)):n.push(e))})),X=n,G=r,n};function Y(t,e){var r=[],n=[];return n=g()(e)?e.map((function(t){return q(t)})):[q(e)],K(t).forEach((function(t){var e=d()(t,"type.displayName")||d()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)})),r}function J(t,e){var r=Y(t,e);return r&&r[0]}var Z=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!(!E(r)||r<=0||!E(n)||n<=0)},Q=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tt=function(t){return t&&t.type&&p()(t.type)&&Q.indexOf(t.type)>=0},et=function(t){var e=[];return K(t).forEach((function(t){tt(t)&&e.push(t)})),e},rt=function(t,r,n){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var i=t;if((0,e.isValidElement)(t)&&(i=t.props),!u()(i))return null;var o={};return Object.keys(i).forEach((function(t){var e;(function(t,e,r,n){var i,o=null!==(i=null==B?void 0:B[n])&&void 0!==i?i:[];return!l()(t)&&(n&&o.includes(e)||R.includes(e))||r&&z.includes(e)})(null===(e=i)||void 0===e?void 0:e[t],t,r,n)&&(o[t]=i[t])})),o},nt=function t(r,n){if(r===n)return!0;var i=e.Children.count(r);if(i!==e.Children.count(n))return!1;if(0===i)return!0;if(1===i)return it(g()(r)?r[0]:r,g()(n)?n[0]:n);for(var o=0;o<i;o++){var a=r[o],c=n[o];if(g()(a)||g()(c)){if(!t(a,c))return!1}else if(!it(a,c))return!1}return!0},it=function(t,e){if(v()(t)&&v()(e))return!0;if(!v()(t)&&!v()(e)){var r=t.props||{},n=r.children,i=V(r,W),o=e.props||{},a=o.children,c=V(o,$);return n&&a?N(i,c)&&nt(n,a):!n&&!a&&N(i,c)}return!1},ot=function(t,e){var r=[],n={};return K(t).forEach((function(t,i){if(tt(t))r.push(t);else if(t){var o=q(t.type),a=e[o]||{},c=a.handler,u=a.once;if(c&&(!u||!n[o])){var s=c(t,o,i);r.push(s),n[o]=!0}}})),r},at=["children","width","height","viewBox","className","style"];function ct(){return ct=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ct.apply(this,arguments)}function ut(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function st(t){var e=t.children,n=t.width,o=t.height,a=t.viewBox,c=t.className,u=t.style,s=ut(t,at),l=a||{width:n,height:o,x:0,y:0},f=i()("recharts-surface",c);return r().createElement("svg",ct({},rt(s,!0,"svg"),{className:f,width:n,height:o,style:u,viewBox:"".concat(l.x," ").concat(l.y," ").concat(l.width," ").concat(l.height)}),r().createElement("title",null,t.title),r().createElement("desc",null,t.desc),e)}var lt=["children","className"];function ft(){return ft=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ft.apply(this,arguments)}function pt(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var ht=r().forwardRef((function(t,e){var n=t.children,o=t.className,a=pt(t,lt),c=i()("recharts-layer",o);return r().createElement("g",ft({className:c},rt(a,!0),{ref:e}),n)})),dt=o(5578),yt=o.n(dt),vt=o(1700),mt=o.n(vt);Math.abs,Math.atan2;const gt=Math.cos,bt=(Math.max,Math.min,Math.sin),xt=Math.sqrt,wt=Math.PI,Ot=2*wt;const St={draw(t,e){const r=xt(e/wt);t.moveTo(r,0),t.arc(0,0,r,0,Ot)}},At={draw(t,e){const r=xt(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},jt=xt(1/3),Et=2*jt,kt={draw(t,e){const r=xt(e/Et),n=r*jt;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},Pt={draw(t,e){const r=xt(e),n=-r/2;t.rect(n,n,r,r)}},_t=bt(wt/10)/bt(7*wt/10),Mt=bt(Ot/10)*_t,Tt=-gt(Ot/10)*_t,Ct={draw(t,e){const r=xt(.8908130915292852*e),n=Mt*r,i=Tt*r;t.moveTo(0,-r),t.lineTo(n,i);for(let e=1;e<5;++e){const o=Ot*e/5,a=gt(o),c=bt(o);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*i,c*n+a*i)}t.closePath()}},It=xt(3),Nt={draw(t,e){const r=-xt(e/(3*It));t.moveTo(0,2*r),t.lineTo(-It*r,-r),t.lineTo(It*r,-r),t.closePath()}},Dt=-.5,Rt=xt(3)/2,Lt=1/xt(12),Bt=3*(Lt/2+1),zt={draw(t,e){const r=xt(e/Bt),n=r/2,i=r*Lt,o=n,a=r*Lt+r,c=-o,u=a;t.moveTo(n,i),t.lineTo(o,a),t.lineTo(c,u),t.lineTo(Dt*n-Rt*i,Rt*n+Dt*i),t.lineTo(Dt*o-Rt*a,Rt*o+Dt*a),t.lineTo(Dt*c-Rt*u,Rt*c+Dt*u),t.lineTo(Dt*n+Rt*i,Dt*i-Rt*n),t.lineTo(Dt*o+Rt*a,Dt*a-Rt*o),t.lineTo(Dt*c+Rt*u,Dt*u-Rt*c),t.closePath()}};function Ft(t){return function(){return t}}const Ut=Math.PI,Wt=2*Ut,$t=1e-6,Vt=Wt-$t;function Ht(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class qt{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?Ht:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return Ht;const r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,o){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+o}`}arcTo(t,e,r,n,i){if(t=+t,e=+e,r=+r,n=+n,(i=+i)<0)throw new Error(`negative radius: ${i}`);let o=this._x1,a=this._y1,c=r-t,u=n-e,s=o-t,l=a-e,f=s*s+l*l;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>$t)if(Math.abs(l*c-u*s)>$t&&i){let p=r-o,h=n-a,d=c*c+u*u,y=p*p+h*h,v=Math.sqrt(d),m=Math.sqrt(f),g=i*Math.tan((Ut-Math.acos((d+f-y)/(2*v*m)))/2),b=g/m,x=g/v;Math.abs(b-1)>$t&&this._append`L${t+b*s},${e+b*l}`,this._append`A${i},${i},0,0,${+(l*p>s*h)},${this._x1=t+x*c},${this._y1=e+x*u}`}else this._append`L${this._x1=t},${this._y1=e}`;else;}arc(t,e,r,n,i,o){if(t=+t,e=+e,o=!!o,(r=+r)<0)throw new Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,s=e+c,l=1^o,f=o?n-i:i-n;null===this._x1?this._append`M${u},${s}`:(Math.abs(this._x1-u)>$t||Math.abs(this._y1-s)>$t)&&this._append`L${u},${s}`,r&&(f<0&&(f=f%Wt+Wt),f>Vt?this._append`A${r},${r},0,1,${l},${t-a},${e-c}A${r},${r},0,1,${l},${this._x1=u},${this._y1=s}`:f>$t&&this._append`A${r},${r},0,${+(f>=Ut)},${l},${this._x1=t+r*Math.cos(i)},${this._y1=e+r*Math.sin(i)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function Gt(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{const t=Math.floor(r);if(!(t>=0))throw new RangeError(`invalid digits: ${r}`);e=t}return t},()=>new qt(e)}xt(3),xt(3);function Xt(){return Xt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Xt.apply(this,arguments)}var Kt={symbolCircle:St,symbolCross:At,symbolDiamond:kt,symbolSquare:Pt,symbolStar:Ct,symbolTriangle:Nt,symbolWye:zt},Yt=Math.PI/180,Jt=function(t){var e=t.className,n=t.cx,o=t.cy,a=t.size,c=rt(t,!0);return n===+n&&o===+o&&a===+a?r().createElement("path",Xt({},c,{className:i()("recharts-symbols",e),transform:"translate(".concat(n,", ").concat(o,")"),d:function(){var e=t.size,r=t.sizeType,n=t.type,i=function(t){var e="symbol".concat(mt()(t));return Kt[e]||St}(n),o=function(t,e){let r=null,n=Gt(i);function i(){let i;if(r||(r=i=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),i)return r=null,i+""||null}return t="function"==typeof t?t:Ft(t||St),e="function"==typeof e?e:Ft(void 0===e?64:+e),i.type=function(e){return arguments.length?(t="function"==typeof e?e:Ft(e),i):t},i.size=function(t){return arguments.length?(e="function"==typeof t?t:Ft(+t),i):e},i.context=function(t){return arguments.length?(r=null==t?null:t,i):r},i}().type(i).size(function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return.5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*Yt;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}}(e,r,n));return o()}()})):null};function Zt(t){return Zt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zt(t)}function Qt(){return Qt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Qt.apply(this,arguments)}function te(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ee(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function re(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ue(n.key),n)}}function ne(t,e){return ne=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ne(t,e)}function ie(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=ae(t);if(e){var i=ae(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return oe(this,r)}}function oe(t,e){if(e&&("object"===Zt(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function ae(t){return ae=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ae(t)}function ce(t,e,r){return(e=ue(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ue(t){var e=function(t,e){if("object"!==Zt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Zt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Zt(e)?e:String(e)}Jt.defaultProps={type:"circle",size:64,sizeType:"area"},Jt.registerSymbol=function(t,e){Kt["symbol".concat(mt()(t))]=e};var se=32,le=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ne(t,e)}(c,t);var e,n,o,a=ie(c);function c(){return ee(this,c),a.apply(this,arguments)}return e=c,n=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,n=16,i=se/6,o=se/3,a=t.inactive?e:t.color;if("plainline"===t.type)return r().createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:n,x2:se,y2:n,className:"recharts-legend-icon"});if("line"===t.type)return r().createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(n,"h").concat(o,"\n            A").concat(i,",").concat(i,",0,1,1,").concat(2*o,",").concat(n,"\n            H").concat(se,"M").concat(2*o,",").concat(n,"\n            A").concat(i,",").concat(i,",0,1,1,").concat(o,",").concat(n),className:"recharts-legend-icon"});if("rect"===t.type)return r().createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(se,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(r().isValidElement(t.legendIcon)){var c=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?te(Object(r),!0).forEach((function(e){ce(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):te(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},t);return delete c.legendIcon,r().cloneElement(t.legendIcon,c)}return r().createElement(Jt,{fill:a,cx:n,cy:n,size:se,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,n=e.payload,o=e.iconSize,a=e.layout,c=e.formatter,u=e.inactiveColor,s={x:0,y:0,width:se,height:se},l={display:"horizontal"===a?"inline-block":"block",marginRight:10},f={display:"inline-block",verticalAlign:"middle",marginRight:4};return n.map((function(e,n){var a,p=e.formatter||c,h=i()((ce(a={"recharts-legend-item":!0},"legend-item-".concat(n),!0),ce(a,"inactive",e.inactive),a));if("none"===e.type)return null;var d=e.inactive?u:e.color;return r().createElement("li",Qt({className:h,style:l,key:"legend-item-".concat(n)},U(t.props,e,n)),r().createElement(st,{width:o,height:o,viewBox:s,style:f},t.renderIcon(e)),r().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},p?p(e.value,e,n):e.value))}))}},{key:"render",value:function(){var t=this.props,e=t.payload,n=t.layout,i=t.align;if(!e||!e.length)return null;var o={padding:0,margin:0,textAlign:"horizontal"===n?i:"left"};return r().createElement("ul",{className:"recharts-default-legend",style:o},this.renderItems())}}],n&&re(e.prototype,n),o&&re(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.PureComponent);function fe(t){return fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fe(t)}ce(le,"displayName","Legend"),ce(le,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var pe=["ref"];function he(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function de(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?he(Object(r),!0).forEach((function(e){Oe(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):he(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ye(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ve(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Se(n.key),n)}}function me(t,e){return me=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},me(t,e)}function ge(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=we(t);if(e){var i=we(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return be(this,r)}}function be(t,e){if(e&&("object"===fe(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return xe(t)}function xe(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function we(t){return we=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},we(t)}function Oe(t,e,r){return(e=Se(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Se(t){var e=function(t,e){if("object"!==fe(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==fe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===fe(e)?e:String(e)}function Ae(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function je(t){return t.value}function Ee(t,e){return!0===t?yt()(e,je):l()(t)?yt()(e,t):e}var ke=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&me(t,e)}(a,t);var e,n,i,o=ge(a);function a(){var t;ye(this,a);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return Oe(xe(t=o.call.apply(o,[this].concat(r))),"state",{boxWidth:-1,boxHeight:-1}),t}return e=a,i=[{key:"getWithHeight",value:function(t,e){var r=t.props.layout;return"vertical"===r&&E(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],(n=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){return this.wrapperNode&&this.wrapperNode.getBoundingClientRect?this.wrapperNode.getBoundingClientRect():null}},{key:"getBBoxSnapshot",value:function(){var t=this.state,e=t.boxWidth,r=t.boxHeight;return e>=0&&r>=0?{width:e,height:r}:null}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,i=n.layout,o=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,s=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===o&&"vertical"===i?{left:((u||0)-(this.getBBoxSnapshot()||{width:0}).width)/2}:"right"===o?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((s||0)-(this.getBBoxSnapshot()||{height:0}).height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),de(de({},e),r)}},{key:"updateBBox",value:function(){var t=this.state,e=t.boxWidth,r=t.boxHeight,n=this.props.onBBoxUpdate;if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var i=this.wrapperNode.getBoundingClientRect();(Math.abs(i.width-e)>1||Math.abs(i.height-r)>1)&&this.setState({boxWidth:i.width,boxHeight:i.height},(function(){n&&n(i)}))}else-1===e&&-1===r||this.setState({boxWidth:-1,boxHeight:-1},(function(){n&&n(null)}))}},{key:"render",value:function(){var t=this,e=this.props,n=e.content,i=e.width,o=e.height,a=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,s=de(de({position:"absolute",width:i||"auto",height:o||"auto"},this.getDefaultPosition(a)),a);return r().createElement("div",{className:"recharts-legend-wrapper",style:s,ref:function(e){t.wrapperNode=e}},function(t,e){if(r().isValidElement(t))return r().cloneElement(t,e);if(l()(t))return r().createElement(t,e);e.ref;var n=Ae(e,pe);return r().createElement(le,n)}(n,de(de({},this.props),{},{payload:Ee(c,u)})))}}])&&ve(e.prototype,n),i&&ve(e,i),Object.defineProperty(e,"prototype",{writable:!1}),a}(e.PureComponent);Oe(ke,"displayName","Legend"),Oe(ke,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var Pe=o(7619),_e=o.n(Pe),Me=o(8367);function Te(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1,n=function n(i){r<0&&(r=i),i-r>e?(t(i),r=-1):requestAnimationFrame(n)};requestAnimationFrame(n)}function Ce(t){return Ce="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ce(t)}function Ie(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Ne(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ne(t,e)}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ne(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function De(){var t=function(){return null},e=!1,r=function r(n){if(!e){if(Array.isArray(n)){if(!n.length)return;var i=Ie(n),o=i[0],a=i.slice(1);return"number"==typeof o?void Te(r.bind(null,a),o):(r(o),void Te(r.bind(null,a)))}"object"===Ce(n)&&t(n),"function"==typeof n&&n()}};return{stop:function(){e=!0},start:function(t){e=!1,r(t)},subscribe:function(e){return t=e,function(){t=function(){return null}}}}}function Re(t){return Re="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Re(t)}function Le(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Be(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Le(Object(r),!0).forEach((function(e){ze(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Le(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ze(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Re(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Re(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Re(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Fe=["Webkit","Moz","O","ms"],Ue=["-webkit-","-moz-","-o-","-ms-"],We=["transform","transformOrigin","transition"],$e=function(t){return t},Ve=function(t,e){return Object.keys(e).reduce((function(r,n){return Be(Be({},r),{},ze({},n,t(n,e[n])))}),{})},He=function(t){return Object.keys(t).reduce((function(t,e){return Be(Be({},t),function(t,e){if(-1===We.indexOf(t))return ze({},t,e);var r="transition"===t,n=t.replace(/(\w)/,(function(t){return t.toUpperCase()})),i=e;return Fe.reduce((function(t,o,a){return r&&(i=e.replace(/(transform|transform-origin)/gim,"".concat(Ue[a],"$1"))),Be(Be({},t),{},ze({},o+n,i))}),{})}(e,t[e]))}),t)},qe=function(t,e,r){return t.map((function(t){return"".concat((n=t,n.replace(/([A-Z])/g,(function(t){return"-".concat(t.toLowerCase())})))," ").concat(e,"ms ").concat(r);var n})).join(",")};function Ge(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||Ke(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Xe(t){return function(t){if(Array.isArray(t))return Ye(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Ke(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ke(t,e){if(t){if("string"==typeof t)return Ye(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ye(t,e):void 0}}function Ye(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Je=1e-4,Ze=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},Qe=function(t,e){return t.map((function(t,r){return t*Math.pow(e,r)})).reduce((function(t,e){return t+e}))},tr=function(t,e){return function(r){var n=Ze(t,e);return Qe(n,r)}},er=function(t,e){return function(r){var n=Ze(t,e),i=[].concat(Xe(n.map((function(t,e){return t*e})).slice(1)),[0]);return Qe(i,r)}},rr=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0],i=e[1],o=e[2],a=e[3];if(1===e.length)switch(e[0]){case"linear":n=0,i=0,o=1,a=1;break;case"ease":n=.25,i=.1,o=.25,a=1;break;case"ease-in":n=.42,i=0,o=1,a=1;break;case"ease-out":n=.42,i=0,o=.58,a=1;break;case"ease-in-out":n=0,i=0,o=.58,a=1;break;default:var c=e[0].split("(");if("cubic-bezier"===c[0]&&4===c[1].split(")")[0].split(",").length){var u=c[1].split(")")[0].split(",").map((function(t){return parseFloat(t)})),s=Ge(u,4);n=s[0],i=s[1],o=s[2],a=s[3]}}[n,o,i,a].every((function(t){return"number"==typeof t&&t>=0&&t<=1}));var l=tr(n,o),f=tr(i,a),p=er(n,o),h=function(t){return t>1?1:t<0?0:t},d=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i=l(r)-e,o=p(r);if(Math.abs(i-e)<Je||o<Je)return f(r);r=h(r-i/o)}return f(r)};return d.isStepper=!1,d},nr=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,i=void 0===n?8:n,o=t.dt,a=void 0===o?17:o,c=function(t,e,n){var o=n+(-(t-e)*r-n*i)*a/1e3,c=n*a/1e3+t;return Math.abs(c-e)<Je&&Math.abs(o)<Je?[e,0]:[c,o]};return c.isStepper=!0,c.dt=a,c};function ir(t){return ir="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ir(t)}function or(t){return function(t){if(Array.isArray(t))return fr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||lr(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ar(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function cr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ar(Object(r),!0).forEach((function(e){ur(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ar(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ur(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==ir(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ir(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===ir(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sr(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||lr(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lr(t,e){if(t){if("string"==typeof t)return fr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?fr(t,e):void 0}}function fr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var pr=function(t,e,r){return t+(e-t)*r},hr=function(t){return t.from!==t.to},dr=function t(e,r,n){var i=Ve((function(t,r){if(hr(r)){var n=sr(e(r.from,r.to,r.velocity),2),i=n[0],o=n[1];return cr(cr({},r),{},{from:i,velocity:o})}return r}),r);return n<1?Ve((function(t,e){return hr(e)?cr(cr({},e),{},{velocity:pr(e.velocity,i[t].velocity,n),from:pr(e.from,i[t].from,n)}):e}),r):t(e,i,n-1)};const yr=function(t,e,r,n,i){var o,a,c,u,s=(o=t,a=e,[Object.keys(o),Object.keys(a)].reduce((function(t,e){return t.filter((function(t){return e.includes(t)}))}))),l=s.reduce((function(r,n){return cr(cr({},r),{},ur({},n,[t[n],e[n]]))}),{}),f=s.reduce((function(r,n){return cr(cr({},r),{},ur({},n,{from:t[n],velocity:0,to:e[n]}))}),{}),p=-1,h=function(){return null};return h=r.isStepper?function(n){c||(c=n);var o=(n-c)/r.dt;f=dr(r,f,o),i(cr(cr(cr({},t),e),Ve((function(t,e){return e.from}),f))),c=n,Object.values(f).filter(hr).length&&(p=requestAnimationFrame(h))}:function(o){u||(u=o);var a=(o-u)/n,c=Ve((function(t,e){return pr.apply(void 0,or(e).concat([r(a)]))}),l);if(i(cr(cr(cr({},t),e),c)),a<1)p=requestAnimationFrame(h);else{var s=Ve((function(t,e){return pr.apply(void 0,or(e).concat([r(1)]))}),l);i(cr(cr(cr({},t),e),s))}},function(){return requestAnimationFrame(h),function(){cancelAnimationFrame(p)}}};var vr=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function mr(t){return mr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mr(t)}function gr(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function br(t){return function(t){if(Array.isArray(t))return xr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return xr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xr(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function wr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Or(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?wr(Object(r),!0).forEach((function(e){_r(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):wr(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Sr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Mr(n.key),n)}}function Ar(t,e){return Ar=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ar(t,e)}function jr(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Pr(t);if(e){var i=Pr(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return Er(this,r)}}function Er(t,e){if(e&&("object"===mr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return kr(t)}function kr(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Pr(t){return Pr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Pr(t)}function _r(t,e,r){return(e=Mr(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Mr(t){var e=function(t,e){if("object"!==mr(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==mr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===mr(e)?e:String(e)}var Tr=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ar(t,e)}(c,t);var n,i,o,a=jr(c);function c(t,e){var r;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,c);var n=(r=a.call(this,t,e)).props,i=n.isActive,o=n.attributeName,u=n.from,s=n.to,l=n.steps,f=n.children;if(r.handleStyleChange=r.handleStyleChange.bind(kr(r)),r.changeStyle=r.changeStyle.bind(kr(r)),!i)return r.state={style:{}},"function"==typeof f&&(r.state={style:s}),Er(r);if(l&&l.length)r.state={style:l[0].style};else if(u){if("function"==typeof f)return r.state={style:u},Er(r);r.state={style:o?_r({},o,u):u}}else r.state={style:{}};return r}return n=c,i=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,i=e.attributeName,o=e.shouldReAnimate;if(n)if(r){if(!((0,Me.deepEqual)(t.to,this.props.to)&&t.canBegin&&t.isActive)){var a=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var c=a||o?this.props.from:t.to;if(this.state&&this.state.style){var u={style:i?_r({},i,c):c};(i&&this.state.style[i]!==c||!i&&this.state.style!==c)&&this.setState(u)}this.runAnimation(Or(Or({},this.props),{},{from:c,begin:0}))}}else{var s={style:i?_r({},i,this.props.to):this.props.to};this.state&&this.state.style&&(i&&this.state.style[i]!==this.props.to||!i&&this.state.style!==this.props.to)&&this.setState(s)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1,this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation()}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,i=t.duration,o=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,s=yr(r,n,function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rr(n);case"spring":return nr();default:if("cubic-bezier"===n.split("(")[0])return rr(n)}return"function"==typeof n?n:null}(o),i,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=s()},i,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,i=t.onAnimationStart,o=r[0],a=o.style,c=o.duration,u=void 0===c?0:c;return this.manager.start([i].concat(br(r.reduce((function(t,n,i){if(0===i)return t;var o=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,s=n.properties,l=n.onAnimationEnd,f=i>0?r[i-1]:n,p=s||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(br(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:o,easing:c}),o]);var h=qe(p,o,c),d=Or(Or(Or({},f.style),u),{},{transition:h});return[].concat(br(t),[d,o,l]).filter($e)}),[a,Math.max(u,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=De());var e=t.begin,r=t.duration,n=t.attributeName,i=t.to,o=t.easing,a=t.onAnimationStart,c=t.onAnimationEnd,u=t.steps,s=t.children,l=this.manager;if(this.unSubscribe=l.subscribe(this.handleStyleChange),"function"!=typeof o&&"function"!=typeof s&&"spring"!==o)if(u.length>1)this.runStepAnimation(t);else{var f=n?_r({},n,i):i,p=qe(Object.keys(f),r,o);l.start([a,e,Or(Or({},f),{},{transition:p}),r,c])}else this.runJSAnimation(t)}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"render",value:function(){var t=this.props,n=t.children,i=(t.begin,t.duration,t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,gr(t,vr)),a=e.Children.count(n),c=He(this.state.style);if("function"==typeof n)return n(c);if(!i||0===a)return n;var u=function(t){var r=t.props,n=r.style,i=void 0===n?{}:n,a=r.className;return(0,e.cloneElement)(t,Or(Or({},o),{},{style:Or(Or({},i),c),className:a}))};return 1===a?u(e.Children.only(n)):r().createElement("div",null,e.Children.map(n,(function(t){return u(t)})))}}],i&&Sr(n.prototype,i),o&&Sr(n,o),Object.defineProperty(n,"prototype",{writable:!1}),c}(e.PureComponent);_r(Tr,"displayName","Animate"),_r(Tr,"propTypes",{from:_e().oneOfType([_e().object,_e().string]),to:_e().oneOfType([_e().object,_e().string]),attributeName:_e().string,duration:_e().number,begin:_e().number,easing:_e().oneOfType([_e().string,_e().func]),steps:_e().arrayOf(_e().shape({duration:_e().number.isRequired,style:_e().object.isRequired,easing:_e().oneOfType([_e().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),_e().func]),properties:_e().arrayOf("string"),onAnimationEnd:_e().func})),children:_e().oneOfType([_e().node,_e().func]),isActive:_e().bool,canBegin:_e().bool,onAnimationEnd:_e().func,shouldReAnimate:_e().bool,onAnimationStart:_e().func,onAnimationReStart:_e().func}),_r(Tr,"defaultProps",{begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}});const Cr=Tr;var Ir=o(4317),Nr=["children","appearOptions","enterOptions","leaveOptions"];function Dr(t){return Dr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(t)}function Rr(){return Rr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Rr.apply(this,arguments)}function Lr(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function Br(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function zr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Br(Object(r),!0).forEach((function(e){Gr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Br(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Fr(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ur(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Xr(n.key),n)}}function Wr(t,e){return Wr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Wr(t,e)}function $r(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=qr(t);if(e){var i=qr(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return Vr(this,r)}}function Vr(t,e){if(e&&("object"===Dr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Hr(t)}function Hr(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function qr(t){return qr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},qr(t)}function Gr(t,e,r){return(e=Xr(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Xr(t){var e=function(t,e){if("object"!==Dr(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Dr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Dr(e)?e:String(e)}void 0===Number.isFinite&&(Number.isFinite=function(t){return"number"==typeof t&&isFinite(t)});var Kr=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.steps,r=t.duration;return e&&e.length?e.reduce((function(t,e){return t+(Number.isFinite(e.duration)&&e.duration>0?e.duration:0)}),0):Number.isFinite(r)?r:0},Yr=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Wr(t,e)}(c,t);var n,i,o,a=$r(c);function c(){var t;Fr(this,c);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return Gr(Hr(t=a.call.apply(a,[this].concat(r))),"state",{isActive:!1}),Gr(Hr(t),"handleEnter",(function(e,r){var n=t.props,i=n.appearOptions,o=n.enterOptions;t.handleStyleActive(r?i:o)})),Gr(Hr(t),"handleExit",(function(){t.handleStyleActive(t.props.leaveOptions)})),t}return n=c,(i=[{key:"handleStyleActive",value:function(t){if(t){var e=t.onAnimationEnd?function(){t.onAnimationEnd()}:null;this.setState(zr(zr({},t),{},{onAnimationEnd:e,isActive:!0}))}}},{key:"parseTimeout",value:function(){var t=this.props,e=t.appearOptions,r=t.enterOptions,n=t.leaveOptions;return Kr(e)+Kr(r)+Kr(n)}},{key:"render",value:function(){var t=this,n=this.props,i=n.children,o=(n.appearOptions,n.enterOptions,n.leaveOptions,Lr(n,Nr));return r().createElement(Ir.Transition,Rr({},o,{onEnter:this.handleEnter,onExit:this.handleExit,timeout:this.parseTimeout()}),(function(){return r().createElement(Cr,t.state,e.Children.only(i))}))}}])&&Ur(n.prototype,i),o&&Ur(n,o),Object.defineProperty(n,"prototype",{writable:!1}),c}(e.Component);Gr(Yr,"propTypes",{appearOptions:_e().object,enterOptions:_e().object,leaveOptions:_e().object,children:_e().element});const Jr=Yr;function Zr(t){var n=t.component,i=t.children,o=t.appear,a=t.enter,c=t.leave;return r().createElement(Ir.TransitionGroup,{component:n},e.Children.map(i,(function(t,e){return r().createElement(Jr,{appearOptions:o,enterOptions:a,leaveOptions:c,key:"child-".concat(e)},t)})))}Zr.propTypes={appear:_e().object,enter:_e().object,leave:_e().object,children:_e().oneOfType([_e().array,_e().element]),component:_e().any},Zr.defaultProps={component:"span"};const Qr=Cr;var tn=o(9734),en=o.n(tn);function rn(t){return rn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rn(t)}function nn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return on(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return on(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function on(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function an(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function cn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?an(Object(r),!0).forEach((function(e){un(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):an(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function un(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==rn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===rn(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sn(t){return g()(t)&&k(t[0])&&k(t[1])?t.join(" ~ "):t}var ln=function(t){var e=t.separator,n=void 0===e?" : ":e,o=t.contentStyle,a=void 0===o?{}:o,c=t.itemStyle,u=void 0===c?{}:c,s=t.labelStyle,l=void 0===s?{}:s,f=t.payload,p=t.formatter,h=t.itemSorter,d=t.wrapperClassName,y=t.labelClassName,m=t.label,g=t.labelFormatter,b=cn({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),x=cn({margin:0},l),w=!v()(m),O=w?m:"",S=i()("recharts-default-tooltip",d),A=i()("recharts-tooltip-label",y);return w&&g&&null!=f&&(O=g(m,f)),r().createElement("div",{className:S,style:b},r().createElement("p",{className:A,style:x},r().isValidElement(O)?O:"".concat(O)),function(){if(f&&f.length){var t=(h?en()(f,h):f).map((function(t,e){if("none"===t.type)return null;var i=cn({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},u),o=t.formatter||p||sn,a=t.value,c=t.name,s=a,l=c;if(o&&null!=s&&null!=l){var h=o(a,c,t,e,f);if(Array.isArray(h)){var d=nn(h,2);s=d[0],l=d[1]}else s=h}return r().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:i},k(l)?r().createElement("span",{className:"recharts-tooltip-item-name"},l):null,k(l)?r().createElement("span",{className:"recharts-tooltip-item-separator"},n):null,r().createElement("span",{className:"recharts-tooltip-item-value"},s),r().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))}));return r().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())},fn={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return fn[t]},set:function(t,e){if("string"==typeof t)fn[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach((function(e){fn[e]=t[e]}))}}};function pn(t){return pn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pn(t)}function hn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function dn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hn(Object(r),!0).forEach((function(e){yn(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function yn(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==pn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==pn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===pn(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function vn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return mn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mn(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function mn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var gn="recharts-tooltip-wrapper";function bn(t){return t.dataKey}var xn={active:!1,allowEscapeViewBox:{x:!1,y:!1},reverseDirection:{x:!1,y:!1},offset:10,viewBox:{x:0,y:0,height:0,width:0},coordinate:{x:0,y:0},cursorStyle:{},separator:" : ",wrapperStyle:{},contentStyle:{},itemStyle:{},labelStyle:{},cursor:!0,trigger:"hover",isAnimationActive:!fn.isSsr,animationEasing:"ease",animationDuration:400,filterNull:!0,useTranslate3d:!1},wn=function(t){var n,o=vn((0,e.useState)(-1),2),a=o[0],c=o[1],u=vn((0,e.useState)(-1),2),s=u[0],f=u[1],p=vn((0,e.useState)(!1),2),h=p[0],d=p[1],y=vn((0,e.useState)({x:0,y:0}),2),m=y[0],g=y[1],b=(0,e.useRef)(),x=t.allowEscapeViewBox,w=t.reverseDirection,O=t.coordinate,S=t.offset,A=t.position,j=t.viewBox,k=(0,e.useCallback)((function(t){"Escape"===t.key&&(d(!0),g((function(t){return dn(dn({},t),{},{x:null==O?void 0:O.x,y:null==O?void 0:O.y})})))}),[null==O?void 0:O.x,null==O?void 0:O.y]);(0,e.useEffect)((function(){return function(){if(h?(document.removeEventListener("keydown",k),(null==O?void 0:O.x)===m.x&&(null==O?void 0:O.y)===m.y||d(!1)):document.addEventListener("keydown",k),b.current&&b.current.getBoundingClientRect){var t=b.current.getBoundingClientRect();(Math.abs(t.width-a)>1||Math.abs(t.height-s)>1)&&(c(t.width),f(t.height))}else-1===a&&-1===s||(c(-1),f(-1))}(),function(){document.removeEventListener("keydown",k)}}),[s,a,O,h,m.x,m.y,k]);var P,_,M=function(t){var e=t.key,r=t.tooltipDimension,n=t.viewBoxDimension;if(A&&E(A[e]))return A[e];var i=O[e]-r-S,o=O[e]+S;return null!=x&&x[e]?w[e]?i:o:null!=w&&w[e]?i<j[e]?Math.max(o,j[e]):Math.max(i,j[e]):o+r>j[e]+n?Math.max(i,j[e]):Math.max(o,j[e])},T=t.payload,C=t.payloadUniqBy,I=t.filterNull,N=t.active,D=t.wrapperStyle,R=t.useTranslate3d,L=t.isAnimationActive,B=t.animationDuration,z=t.animationEasing,F=function(t,e){return!0===t?yt()(e,bn):l()(t)?yt()(e,t):e}(C,I&&T&&T.length?T.filter((function(t){return!v()(t.value)})):T),U=F&&F.length,W=t.content,$=dn({pointerEvents:"none",visibility:!h&&N&&U?"visible":"hidden",position:"absolute",top:0,left:0},D);A&&E(A.x)&&E(A.y)?(P=A.x,_=A.y):a>0&&s>0&&O?(P=M({key:"x",tooltipDimension:a,viewBoxDimension:j.width}),_=M({key:"y",tooltipDimension:s,viewBoxDimension:j.height})):$.visibility="hidden",$=dn(dn({},He({transform:R?"translate3d(".concat(P,"px, ").concat(_,"px, 0)"):"translate(".concat(P,"px, ").concat(_,"px)")})),$),L&&N&&($=dn(dn({},He({transition:"transform ".concat(B,"ms ").concat(z)})),$));var V=i()(gn,(yn(n={},"".concat(gn,"-right"),E(P)&&O&&E(O.x)&&P>=O.x),yn(n,"".concat(gn,"-left"),E(P)&&O&&E(O.x)&&P<O.x),yn(n,"".concat(gn,"-bottom"),E(_)&&O&&E(O.y)&&_>=O.y),yn(n,"".concat(gn,"-top"),E(_)&&O&&E(O.y)&&_<O.y),n));return r().createElement("div",{tabIndex:-1,role:"dialog",className:V,style:$,ref:b},function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?r().createElement(t,e):r().createElement(ln,e)}(W,dn(dn({},t),{},{payload:F})))};wn.displayName="Tooltip",wn.defaultProps=xn;var On=o(5156),Sn=o(3279),An=o.n(Sn),jn=o(3493),En=o.n(jn),kn=function(t,e){return kn=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},kn(t,e)};function Pn(t,e){function r(){this.constructor=t}kn(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function _n(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(t);i<n.length;i++)e.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(t,n[i])&&(r[n[i]]=t[n[i]])}return r}var Mn=function(t,e,r,n){switch(e){case"debounce":return An()(t,r,n);case"throttle":return En()(t,r,n);default:return t}},Tn=function(t){return"function"==typeof t},Cn=function(){return"undefined"==typeof window},In=function(t){return t instanceof Element||t instanceof HTMLDocument},Nn=function(t,e,r,n){return function(i){var o=i.width,a=i.height;e((function(e){return e.width===o&&e.height===a||e.width===o&&!n||e.height===a&&!r?e:(null==t||t(o,a),{width:o,height:a})}))}},Dn=function(t){function n(r){var n=t.call(this,r)||this;n.cancelHandler=function(){n.resizeHandler&&n.resizeHandler.cancel&&(n.resizeHandler.cancel(),n.resizeHandler=null)},n.attachObserver=function(){var t=n.props,e=t.targetRef,r=t.observerOptions;if(!Cn()){e&&e.current&&(n.targetRef.current=e.current);var i=n.getElement();i&&(n.observableElement&&n.observableElement===i||(n.observableElement=i,n.resizeObserver.observe(i,r)))}},n.getElement=function(){var t=n.props,e=t.querySelector,r=t.targetDomEl;if(Cn())return null;if(e)return document.querySelector(e);if(r&&In(r))return r;if(n.targetRef&&In(n.targetRef.current))return n.targetRef.current;var i=(0,On.findDOMNode)(n);if(!i)return null;switch(n.getRenderType()){case"renderProp":case"childFunction":case"child":case"childArray":return i;default:return i.parentElement}},n.createResizeHandler=function(t){var e=n.props,r=e.handleWidth,i=void 0===r||r,o=e.handleHeight,a=void 0===o||o,c=e.onResize;if(i||a){var u=Nn(c,n.setState.bind(n),i,a);t.forEach((function(t){var e=t&&t.contentRect||{},r=e.width,i=e.height;!n.skipOnMount&&!Cn()&&u({width:r,height:i}),n.skipOnMount=!1}))}},n.getRenderType=function(){var t=n.props,r=t.render,i=t.children;return Tn(r)?"renderProp":Tn(i)?"childFunction":(0,e.isValidElement)(i)?"child":Array.isArray(i)?"childArray":"parent"};var i=r.skipOnMount,o=r.refreshMode,a=r.refreshRate,c=void 0===a?1e3:a,u=r.refreshOptions;return n.state={width:void 0,height:void 0},n.skipOnMount=i,n.targetRef=(0,e.createRef)(),n.observableElement=null,Cn()||(n.resizeHandler=Mn(n.createResizeHandler,o,c,u),n.resizeObserver=new window.ResizeObserver(n.resizeHandler)),n}return Pn(n,t),n.prototype.componentDidMount=function(){this.attachObserver()},n.prototype.componentDidUpdate=function(){this.attachObserver()},n.prototype.componentWillUnmount=function(){Cn()||(this.observableElement=null,this.resizeObserver.disconnect(),this.cancelHandler())},n.prototype.render=function(){var t=this.props,n=t.render,i=t.children,o=t.nodeType,a=void 0===o?"div":o,c=this.state,u={width:c.width,height:c.height,targetRef:this.targetRef};switch(this.getRenderType()){case"renderProp":return null==n?void 0:n(u);case"childFunction":var s=i;return null==s?void 0:s(u);case"child":var l=i;if(l.type&&"string"==typeof l.type){var f=_n(u,["targetRef"]);return(0,e.cloneElement)(l,f)}return(0,e.cloneElement)(l,u);case"childArray":return i.map((function(t){return!!t&&(0,e.cloneElement)(t,u)}));default:return r().createElement(a,null)}},n}(e.PureComponent);Cn()?e.useEffect:e.useLayoutEffect;var Rn=function(t,e){for(var r=arguments.length,n=new Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]};function Ln(){return Ln=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ln.apply(this,arguments)}function Bn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return zn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zn(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Fn=(0,e.forwardRef)((function(t,n){var o=t.aspect,a=t.initialDimension,c=void 0===a?{width:-1,height:-1}:a,u=t.width,s=void 0===u?"100%":u,l=t.height,f=void 0===l?"100%":l,p=t.minWidth,h=void 0===p?0:p,d=t.minHeight,y=t.maxHeight,v=t.children,m=t.debounce,g=void 0===m?0:m,b=t.id,x=t.className,w=t.onResize,O=Bn((0,e.useState)({containerWidth:c.width,containerHeight:c.height}),2),S=O[0],A=O[1],E=(0,e.useRef)(null);(0,e.useImperativeHandle)(n,(function(){return E}),[E]);var k=(0,e.useCallback)((function(){return E.current?{containerWidth:E.current.clientWidth,containerHeight:E.current.clientHeight}:null}),[]),P=(0,e.useCallback)((function(){var t=k();if(t){var e=t.containerWidth,r=t.containerHeight;w&&w(e,r),A((function(t){var n=t.containerWidth,i=t.containerHeight;return e!==n||r!==i?{containerWidth:e,containerHeight:r}:t}))}}),[k,w]),_=(0,e.useMemo)((function(){var t=S.containerWidth,r=S.containerHeight;if(t<0||r<0)return null;Rn(j(s)||j(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",s,f),Rn(!o||o>0,"The aspect(%s) must be greater than zero.",o);var n=j(s)?t:s,i=j(f)?r:f;return o&&o>0&&(n?i=n/o:i&&(n=i*o),y&&i>y&&(i=y)),Rn(n>0||i>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,i,s,f,h,d,o),(0,e.cloneElement)(v,{width:n,height:i})}),[o,v,f,y,d,h,S,s]);(0,e.useEffect)((function(){var t=k();t&&A(t)}),[k]);var M={width:s,height:f,minWidth:h,minHeight:d,maxHeight:y};return r().createElement(Dn,{handleWidth:!0,handleHeight:!0,onResize:P,targetRef:E,refreshMode:g>0?"debounce":void 0,refreshRate:g},r().createElement("div",Ln({},null!=b?{id:"".concat(b)}:{},{className:i()("recharts-responsive-container",x),style:M,ref:E}),_))})),Un=function(t){return null};Un.displayName="Cell";var Wn=o(4275),$n=o.n(Wn);function Vn(t){return Vn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vn(t)}function Hn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function qn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Hn(Object(r),!0).forEach((function(e){Gn(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Hn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Gn(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Vn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Vn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Vn(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Xn(t){return function(t){if(Array.isArray(t))return Kn(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Kn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Kn(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Yn={widthCache:{},cacheCount:0},Jn={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Zn=["minWidth","maxWidth","width","minHeight","maxHeight","height","top","left","fontSize","lineHeight","padding","margin","paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom"],Qn="recharts_measurement_span";var ti=function(t){return Object.keys(t).reduce((function(e,r){return"".concat(e).concat((n=r,n.split("").reduce((function(t,e){return e===e.toUpperCase()?[].concat(Xn(t),["-",e.toLowerCase()]):[].concat(Xn(t),[e])}),[]).join("")),":").concat(function(t,e){return Zn.indexOf(t)>=0&&e===+e?"".concat(e,"px"):e}(r,t[r]),";");var n}),"")},ei=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||fn.isSsr)return{width:0,height:0};var r="".concat(t),n=ti(e),i="".concat(r,"-").concat(n);if(Yn.widthCache[i])return Yn.widthCache[i];try{var o=document.getElementById(Qn);o||((o=document.createElement("span")).setAttribute("id",Qn),o.setAttribute("aria-hidden","true"),document.body.appendChild(o));var a=qn(qn({},Jn),e);Object.keys(a).map((function(t){return o.style[t]=a[t],t})),o.textContent=r;var c=o.getBoundingClientRect(),u={width:c.width,height:c.height};return Yn.widthCache[i]=u,++Yn.cacheCount>2e3&&(Yn.cacheCount=0,Yn.widthCache={}),u}catch(t){return{width:0,height:0}}},ri=["dx","dy","textAnchor","verticalAnchor","scaleToFit","angle","lineHeight","capHeight","className","breakAll"];function ni(){return ni=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ni.apply(this,arguments)}function ii(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function oi(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return ai(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ai(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ai(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var ci=/[ \f\n\r\t\v\u2028\u2029]+/,ui=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var i=[];return v()(e)||(i=r?e.toString().split(""):e.toString().split(ci)),{wordsWithComputedWidth:i.map((function(t){return{word:t,width:ei(t,n).width}})),spaceWidth:r?0:ei(" ",n).width}}catch(t){return null}},si=function(t){return[{words:v()(t)?[]:t.toString().split(ci)}]},li=function(t){var e=t.width,r=t.scaleToFit,n=t.children,i=t.style,o=t.breakAll,a=t.maxLines;if((e||r)&&!fn.isSsr){var c=ui({breakAll:o,children:n,style:i});return c?function(t,e,r,n,i){var o=t.maxLines,a=t.children,c=t.style,u=t.breakAll,s=E(o),l=a,f=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce((function(t,e){var o=e.word,a=e.width,c=t[t.length-1];if(c&&(null==n||i||c.width+a+r<Number(n)))c.words.push(o),c.width+=a+r;else{var u={words:[o],width:a};t.push(u)}return t}),[])},p=f(e);if(!s)return p;for(var h,d=function(t){var e=l.slice(0,t),r=ui({breakAll:u,style:c,children:e+"…"}).wordsWithComputedWidth,i=f(r),a=i.length>o||function(t){return t.reduce((function(t,e){return t.width>e.width?t:e}))}(i).width>Number(n);return[a,i]},y=0,v=l.length-1,m=0;y<=v&&m<=l.length-1;){var g=Math.floor((y+v)/2),b=oi(d(g-1),2),x=b[0],w=b[1],O=oi(d(g),1)[0];if(x||O||(y=g+1),x&&O&&(v=g-1),!x&&O){h=w;break}m++}return h||p}({breakAll:o,children:n,maxLines:a,style:i},c.wordsWithComputedWidth,c.spaceWidth,e,r):si(n)}return si(n)},fi={x:0,y:0,lineHeight:"1em",capHeight:"0.71em",scaleToFit:!1,textAnchor:"start",verticalAnchor:"end",fill:"#808080"},pi=function(t){var n=(0,e.useMemo)((function(){return li({breakAll:t.breakAll,children:t.children,maxLines:t.maxLines,scaleToFit:t.scaleToFit,style:t.style,width:t.width})}),[t.breakAll,t.children,t.maxLines,t.scaleToFit,t.style,t.width]),o=t.dx,a=t.dy,c=t.textAnchor,u=t.verticalAnchor,s=t.scaleToFit,l=t.angle,f=t.lineHeight,p=t.capHeight,h=t.className,d=t.breakAll,y=ii(t,ri);if(!k(y.x)||!k(y.y))return null;var v,m=y.x+(E(o)?o:0),g=y.y+(E(a)?a:0);switch(u){case"start":v=$n()("calc(".concat(p,")"));break;case"middle":v=$n()("calc(".concat((n.length-1)/2," * -").concat(f," + (").concat(p," / 2))"));break;default:v=$n()("calc(".concat(n.length-1," * -").concat(f,")"))}var b=[];if(s){var x=n[0].width,w=t.width;b.push("scale(".concat((E(w)?w/x:1)/x,")"))}return l&&b.push("rotate(".concat(l,", ").concat(m,", ").concat(g,")")),b.length&&(y.transform=b.join(" ")),r().createElement("text",ni({},rt(y,!0),{x:m,y:g,className:i()("recharts-text",h),textAnchor:c,fill:y.fill.includes("url")?fi.fill:y.fill}),n.map((function(t,e){return r().createElement("tspan",{x:m,dy:0===e?v:f,key:e},t.words.join(d?"":" "))})))};pi.defaultProps=fi;var hi=o(8446),di=o.n(hi),yi=o(6162),vi=o.n(yi),mi=o(3632),gi=o.n(mi),bi=o(4654),xi=o.n(bi);function wi(t,e,r){t=+t,e=+e,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),o=new Array(i);++n<i;)o[n]=t+n*r;return o}function Oi(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function Si(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class Ai extends Map{constructor(t,e=_i){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[e,r]of t)this.set(e,r)}get(t){return super.get(Ei(this,t))}has(t){return super.has(Ei(this,t))}set(t,e){return super.set(ki(this,t),e)}delete(t){return super.delete(Pi(this,t))}}class ji extends Set{constructor(t,e=_i){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const e of t)this.add(e)}has(t){return super.has(Ei(this,t))}add(t){return super.add(ki(this,t))}delete(t){return super.delete(Pi(this,t))}}function Ei({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):r}function ki({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}function Pi({_intern:t,_key:e},r){const n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}function _i(t){return null!==t&&"object"==typeof t?t.valueOf():t}const Mi=Symbol("implicit");function Ti(){var t=new Ai,e=[],r=[],n=Mi;function i(i){let o=t.get(i);if(void 0===o){if(n!==Mi)return n;t.set(i,o=e.push(i)-1)}return r[o%r.length]}return i.domain=function(r){if(!arguments.length)return e.slice();e=[],t=new Ai;for(const n of r)t.has(n)||t.set(n,e.push(n)-1);return i},i.range=function(t){return arguments.length?(r=Array.from(t),i):r.slice()},i.unknown=function(t){return arguments.length?(n=t,i):n},i.copy=function(){return Ti(e,r).unknown(n)},Oi.apply(i,arguments),i}function Ci(){var t,e,r=Ti().unknown(void 0),n=r.domain,i=r.range,o=0,a=1,c=!1,u=0,s=0,l=.5;function f(){var r=n().length,f=a<o,p=f?a:o,h=f?o:a;t=(h-p)/Math.max(1,r-u+2*s),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*l,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var d=wi(r).map((function(e){return p+t*e}));return i(f?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([o,a]=t,o=+o,a=+a,f()):[o,a]},r.rangeRound=function(t){return[o,a]=t,o=+o,a=+a,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,s=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(s=+t,f()):s},r.align=function(t){return arguments.length?(l=Math.max(0,Math.min(1,t)),f()):l},r.copy=function(){return Ci(n(),[o,a]).round(c).paddingInner(u).paddingOuter(s).align(l)},Oi.apply(f(),arguments)}function Ii(t){var e=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return Ii(e())},t}function Ni(){return Ii(Ci.apply(null,arguments).paddingInner(1))}const Di=Math.sqrt(50),Ri=Math.sqrt(10),Li=Math.sqrt(2);function Bi(t,e,r){const n=(e-t)/Math.max(0,r),i=Math.floor(Math.log10(n)),o=n/Math.pow(10,i),a=o>=Di?10:o>=Ri?5:o>=Li?2:1;let c,u,s;return i<0?(s=Math.pow(10,-i)/a,c=Math.round(t*s),u=Math.round(e*s),c/s<t&&++c,u/s>e&&--u,s=-s):(s=Math.pow(10,i)*a,c=Math.round(t/s),u=Math.round(e/s),c*s<t&&++c,u*s>e&&--u),u<c&&.5<=r&&r<2?Bi(t,e,2*r):[c,u,s]}function zi(t,e,r){if(!((r=+r)>0))return[];if((t=+t)===(e=+e))return[t];const n=e<t,[i,o,a]=n?Bi(e,t,r):Bi(t,e,r);if(!(o>=i))return[];const c=o-i+1,u=new Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=(o-t)/-a;else for(let t=0;t<c;++t)u[t]=(o-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=(i+t)/-a;else for(let t=0;t<c;++t)u[t]=(i+t)*a;return u}function Fi(t,e,r){return Bi(t=+t,e=+e,r=+r)[2]}function Ui(t,e,r){r=+r;const n=(e=+e)<(t=+t),i=n?Fi(e,t,r):Fi(t,e,r);return(n?-1:1)*(i<0?1/-i:i)}function Wi(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function $i(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function Vi(t){let e,r,n;function i(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{const e=i+o>>>1;r(t[e],n)<0?i=e+1:o=e}while(i<o)}return i}return 2!==t.length?(e=Wi,r=(e,r)=>Wi(t(e),r),n=(e,r)=>t(e)-r):(e=t===Wi||t===$i?t:Hi,r=t,n=t),{left:i,center:function(t,e,r=0,o=t.length){const a=i(t,e,r,o-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{const e=i+o>>>1;r(t[e],n)<=0?i=e+1:o=e}while(i<o)}return i}}}function Hi(){return 0}function qi(t){return null===t?NaN:+t}const Gi=Vi(Wi),Xi=Gi.right,Ki=(Gi.left,Vi(qi).center,Xi);function Yi(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function Ji(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function Zi(){}var Qi=.7,to=1/Qi,eo="\\s*([+-]?\\d+)\\s*",ro="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",no="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",io=/^#([0-9a-f]{3,8})$/,oo=new RegExp(`^rgb\\(${eo},${eo},${eo}\\)$`),ao=new RegExp(`^rgb\\(${no},${no},${no}\\)$`),co=new RegExp(`^rgba\\(${eo},${eo},${eo},${ro}\\)$`),uo=new RegExp(`^rgba\\(${no},${no},${no},${ro}\\)$`),so=new RegExp(`^hsl\\(${ro},${no},${no}\\)$`),lo=new RegExp(`^hsla\\(${ro},${no},${no},${ro}\\)$`),fo={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function po(){return this.rgb().formatHex()}function ho(){return this.rgb().formatRgb()}function yo(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=io.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?vo(e):3===r?new xo(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?mo(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?mo(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=oo.exec(t))?new xo(e[1],e[2],e[3],1):(e=ao.exec(t))?new xo(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=co.exec(t))?mo(e[1],e[2],e[3],e[4]):(e=uo.exec(t))?mo(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=so.exec(t))?Eo(e[1],e[2]/100,e[3]/100,1):(e=lo.exec(t))?Eo(e[1],e[2]/100,e[3]/100,e[4]):fo.hasOwnProperty(t)?vo(fo[t]):"transparent"===t?new xo(NaN,NaN,NaN,0):null}function vo(t){return new xo(t>>16&255,t>>8&255,255&t,1)}function mo(t,e,r,n){return n<=0&&(t=e=r=NaN),new xo(t,e,r,n)}function go(t){return t instanceof Zi||(t=yo(t)),t?new xo((t=t.rgb()).r,t.g,t.b,t.opacity):new xo}function bo(t,e,r,n){return 1===arguments.length?go(t):new xo(t,e,r,null==n?1:n)}function xo(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function wo(){return`#${jo(this.r)}${jo(this.g)}${jo(this.b)}`}function Oo(){const t=So(this.opacity);return`${1===t?"rgb(":"rgba("}${Ao(this.r)}, ${Ao(this.g)}, ${Ao(this.b)}${1===t?")":`, ${t})`}`}function So(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Ao(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function jo(t){return((t=Ao(t))<16?"0":"")+t.toString(16)}function Eo(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new Po(t,e,r,n)}function ko(t){if(t instanceof Po)return new Po(t.h,t.s,t.l,t.opacity);if(t instanceof Zi||(t=yo(t)),!t)return new Po;if(t instanceof Po)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),o=Math.max(e,r,n),a=NaN,c=o-i,u=(o+i)/2;return c?(a=e===o?(r-n)/c+6*(r<n):r===o?(n-e)/c+2:(e-r)/c+4,c/=u<.5?o+i:2-o-i,a*=60):c=u>0&&u<1?0:a,new Po(a,c,u,t.opacity)}function Po(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function _o(t){return(t=(t||0)%360)<0?t+360:t}function Mo(t){return Math.max(0,Math.min(1,t||0))}function To(t,e,r){return 255*(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)}function Co(t,e,r,n,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*r+(1+3*t+3*o-3*a)*n+a*i)/6}Yi(Zi,yo,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:po,formatHex:po,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ko(this).formatHsl()},formatRgb:ho,toString:ho}),Yi(xo,bo,Ji(Zi,{brighter(t){return t=null==t?to:Math.pow(to,t),new xo(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?Qi:Math.pow(Qi,t),new xo(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new xo(Ao(this.r),Ao(this.g),Ao(this.b),So(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:wo,formatHex:wo,formatHex8:function(){return`#${jo(this.r)}${jo(this.g)}${jo(this.b)}${jo(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Oo,toString:Oo})),Yi(Po,(function(t,e,r,n){return 1===arguments.length?ko(t):new Po(t,e,r,null==n?1:n)}),Ji(Zi,{brighter(t){return t=null==t?to:Math.pow(to,t),new Po(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?Qi:Math.pow(Qi,t),new Po(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new xo(To(t>=240?t-240:t+120,i,n),To(t,i,n),To(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new Po(_o(this.h),Mo(this.s),Mo(this.l),So(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=So(this.opacity);return`${1===t?"hsl(":"hsla("}${_o(this.h)}, ${100*Mo(this.s)}%, ${100*Mo(this.l)}%${1===t?")":`, ${t})`}`}}));const Io=t=>()=>t;function No(t,e){return function(r){return t+r*e}}function Do(t){return 1==(t=+t)?Ro:function(e,r){return r-e?function(t,e,r){return t=Math.pow(t,r),e=Math.pow(e,r)-t,r=1/r,function(n){return Math.pow(t+n*e,r)}}(e,r,t):Io(isNaN(e)?r:e)}}function Ro(t,e){var r=e-t;return r?No(t,r):Io(isNaN(t)?e:t)}const Lo=function t(e){var r=Do(e);function n(t,e){var n=r((t=bo(t)).r,(e=bo(e)).r),i=r(t.g,e.g),o=r(t.b,e.b),a=Ro(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return n.gamma=t,n}(1);function Bo(t){return function(e){var r,n,i=e.length,o=new Array(i),a=new Array(i),c=new Array(i);for(r=0;r<i;++r)n=bo(e[r]),o[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return o=t(o),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=o(t),n.g=a(t),n.b=c(t),n+""}}}Bo((function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],o=t[n+1],a=n>0?t[n-1]:2*i-o,c=n<e-1?t[n+2]:2*o-i;return Co((r-n/e)*e,a,i,o,c)}})),Bo((function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],o=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return Co((r-n/e)*e,i,o,a,c)}}));function zo(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,o=new Array(i),a=new Array(n);for(r=0;r<i;++r)o[r]=Go(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<i;++r)a[r]=o[r](t);return a}}function Fo(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}function Uo(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}function Wo(t,e){var r,n={},i={};for(r in null!==t&&"object"==typeof t||(t={}),null!==e&&"object"==typeof e||(e={}),e)r in t?n[r]=Go(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}var $o=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Vo=new RegExp($o.source,"g");function Ho(t,e){var r,n,i,o=$o.lastIndex=Vo.lastIndex=0,a=-1,c=[],u=[];for(t+="",e+="";(r=$o.exec(t))&&(n=Vo.exec(e));)(i=n.index)>o&&(i=e.slice(o,i),c[a]?c[a]+=i:c[++a]=i),(r=r[0])===(n=n[0])?c[a]?c[a]+=n:c[++a]=n:(c[++a]=null,u.push({i:a,x:Uo(r,n)})),o=Vo.lastIndex;return o<e.length&&(i=e.slice(o),c[a]?c[a]+=i:c[++a]=i),c.length<2?u[0]?function(t){return function(e){return t(e)+""}}(u[0].x):function(t){return function(){return t}}(e):(e=u.length,function(t){for(var r,n=0;n<e;++n)c[(r=u[n]).i]=r.x(t);return c.join("")})}function qo(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(r=0;r<n;++r)i[r]=t[r]*(1-o)+e[r]*o;return i}}function Go(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?Io(e):("number"===i?Uo:"string"===i?(r=yo(e))?(e=r,Lo):Ho:e instanceof yo?Lo:e instanceof Date?Fo:(n=e,!ArrayBuffer.isView(n)||n instanceof DataView?Array.isArray(e)?zo:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?Wo:Uo:qo))(t,e)}function Xo(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function Ko(t){return+t}var Yo=[0,1];function Jo(t){return t}function Zo(t,e){return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r});var r}function Qo(t,e,r){var n=t[0],i=t[1],o=e[0],a=e[1];return i<n?(n=Zo(i,n),o=r(a,o)):(n=Zo(n,i),o=r(o,a)),function(t){return o(n(t))}}function ta(t,e,r){var n=Math.min(t.length,e.length)-1,i=new Array(n),o=new Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)i[a]=Zo(t[a],t[a+1]),o[a]=r(e[a],e[a+1]);return function(e){var r=Ki(t,e,1,n)-1;return o[r](i[r](e))}}function ea(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function ra(){var t,e,r,n,i,o,a=Yo,c=Yo,u=Go,s=Jo;function l(){var t=Math.min(a.length,c.length);return s!==Jo&&(s=function(t,e){var r;return t>e&&(r=t,t=e,e=r),function(r){return Math.max(t,Math.min(e,r))}}(a[0],a[t-1])),n=t>2?ta:Qo,i=o=null,f}function f(e){return null==e||isNaN(e=+e)?r:(i||(i=n(a.map(t),c,u)))(t(s(e)))}return f.invert=function(r){return s(e((o||(o=n(c,a.map(t),Uo)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,Ko),l()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),l()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=Xo,l()},f.clamp=function(t){return arguments.length?(s=!!t||Jo,l()):s!==Jo},f.interpolate=function(t){return arguments.length?(u=t,l()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,l()}}function na(){return ra()(Jo,Jo)}var ia,oa=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function aa(t){if(!(e=oa.exec(t)))throw new Error("invalid format: "+t);var e;return new ca({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function ca(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function ua(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function sa(t){return(t=ua(Math.abs(t)))?t[1]:NaN}function la(t,e){var r=ua(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}aa.prototype=ca.prototype,ca.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const fa={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>la(100*t,e),r:la,s:function(t,e){var r=ua(t,e);if(!r)return t+"";var n=r[0],i=r[1],o=i-(ia=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,a=n.length;return o===a?n:o>a?n+new Array(o-a+1).join("0"):o>0?n.slice(0,o)+"."+n.slice(o):"0."+new Array(1-o).join("0")+ua(t,Math.max(0,e+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function pa(t){return t}var ha,da,ya,va=Array.prototype.map,ma=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function ga(t){var e,r,n=void 0===t.grouping||void 0===t.thousands?pa:(e=va.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,o=[],a=0,c=e[0],u=0;i>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),o.push(t.substring(i-=c,i+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return o.reverse().join(r)}),i=void 0===t.currency?"":t.currency[0]+"",o=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",c=void 0===t.numerals?pa:function(t){return function(e){return e.replace(/[0-9]/g,(function(e){return t[+e]}))}}(va.call(t.numerals,String)),u=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",l=void 0===t.nan?"NaN":t.nan+"";function f(t){var e=(t=aa(t)).fill,r=t.align,f=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,g=t.type;"n"===g?(y=!0,g="g"):fa[g]||(void 0===v&&(v=12),m=!0,g="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var b="$"===p?i:"#"===p&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",x="$"===p?o:/[%p]/.test(g)?u:"",w=fa[g],O=/[defgprs%]/.test(g);function S(t){var i,o,u,p=b,S=x;if("c"===g)S=w(t)+S,t="";else{var A=(t=+t)<0||1/t<0;if(t=isNaN(t)?l:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),A&&0==+t&&"+"!==f&&(A=!1),p=(A?"("===f?f:s:"-"===f||"("===f?"":f)+p,S=("s"===g?ma[8+ia/3]:"")+S+(A&&"("===f?")":""),O)for(i=-1,o=t.length;++i<o;)if(48>(u=t.charCodeAt(i))||u>57){S=(46===u?a+t.slice(i+1):t.slice(i))+S,t=t.slice(0,i);break}}y&&!h&&(t=n(t,1/0));var j=p.length+t.length+S.length,E=j<d?new Array(d-j+1).join(e):"";switch(y&&h&&(t=n(E+t,E.length?d-S.length:1/0),E=""),r){case"<":t=p+t+S+E;break;case"=":t=p+E+t+S;break;case"^":t=E.slice(0,j=E.length>>1)+p+t+S+E.slice(j);break;default:t=E+p+t+S}return c(t)}return v=void 0===v?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),S.toString=function(){return t+""},S}return{format:f,formatPrefix:function(t,e){var r=f(((t=aa(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(sa(e)/3))),i=Math.pow(10,-n),o=ma[8+n/3];return function(t){return r(i*t)+o}}}}function ba(t,e,r,n){var i,o=Ui(t,e,r);switch((n=aa(null==n?",f":n)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(i=function(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(sa(e)/3)))-sa(Math.abs(t)))}(o,a))||(n.precision=i),ya(n,a);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=function(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,sa(e)-sa(t))+1}(o,Math.max(Math.abs(t),Math.abs(e))))||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=function(t){return Math.max(0,-sa(Math.abs(t)))}(o))||(n.precision=i-2*("%"===n.type))}return da(n)}function xa(t){var e=t.domain;return t.ticks=function(t){var r=e();return zi(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return ba(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,o=e(),a=0,c=o.length-1,u=o[a],s=o[c],l=10;for(s<u&&(i=u,u=s,s=i,i=a,a=c,c=i);l-- >0;){if((i=Fi(u,s,r))===n)return o[a]=u,o[c]=s,e(o);if(i>0)u=Math.floor(u/i)*i,s=Math.ceil(s/i)*i;else{if(!(i<0))break;u=Math.ceil(u*i)/i,s=Math.floor(s*i)/i}n=i}return t},t}function wa(){var t=na();return t.copy=function(){return ea(t,wa())},Oi.apply(t,arguments),xa(t)}function Oa(t){var e;function r(t){return null==t||isNaN(t=+t)?e:t}return r.invert=r,r.domain=r.range=function(e){return arguments.length?(t=Array.from(e,Ko),r):t.slice()},r.unknown=function(t){return arguments.length?(e=t,r):e},r.copy=function(){return Oa(t).unknown(e)},t=arguments.length?Array.from(t,Ko):[0,1],xa(r)}function Sa(t,e){var r,n=0,i=(t=t.slice()).length-1,o=t[n],a=t[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),t[n]=e.floor(o),t[i]=e.ceil(a),t}function Aa(t){return Math.log(t)}function ja(t){return Math.exp(t)}function Ea(t){return-Math.log(-t)}function ka(t){return-Math.exp(-t)}function Pa(t){return isFinite(t)?+("1e"+t):t<0?0:t}function _a(t){return(e,r)=>-t(-e,r)}function Ma(t){const e=t(Aa,ja),r=e.domain;let n,i,o=10;function a(){return n=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),e=>Math.log(e)/t)}(o),i=function(t){return 10===t?Pa:t===Math.E?Math.exp:e=>Math.pow(t,e)}(o),r()[0]<0?(n=_a(n),i=_a(i),t(Ea,ka)):t(Aa,ja),e}return e.base=function(t){return arguments.length?(o=+t,a()):o},e.domain=function(t){return arguments.length?(r(t),a()):r()},e.ticks=t=>{const e=r();let a=e[0],c=e[e.length-1];const u=c<a;u&&([a,c]=[c,a]);let s,l,f=n(a),p=n(c);const h=null==t?10:+t;let d=[];if(!(o%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),a>0){for(;f<=p;++f)for(s=1;s<o;++s)if(l=f<0?s/i(-f):s*i(f),!(l<a)){if(l>c)break;d.push(l)}}else for(;f<=p;++f)for(s=o-1;s>=1;--s)if(l=f>0?s/i(-f):s*i(f),!(l<a)){if(l>c)break;d.push(l)}2*d.length<h&&(d=zi(a,c,h))}else d=zi(f,p,Math.min(p-f,h)).map(i);return u?d.reverse():d},e.tickFormat=(t,r)=>{if(null==t&&(t=10),null==r&&(r=10===o?"s":","),"function"!=typeof r&&(o%1||null!=(r=aa(r)).precision||(r.trim=!0),r=da(r)),t===1/0)return r;const a=Math.max(1,o*t/e.ticks().length);return t=>{let e=t/i(Math.round(n(t)));return e*o<o-.5&&(e*=o),e<=a?r(t):""}},e.nice=()=>r(Sa(r(),{floor:t=>i(Math.floor(n(t))),ceil:t=>i(Math.ceil(n(t)))})),e}function Ta(){const t=Ma(ra()).domain([1,10]);return t.copy=()=>ea(t,Ta()).base(t.base()),Oi.apply(t,arguments),t}function Ca(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function Ia(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function Na(t){var e=1,r=t(Ca(e),Ia(e));return r.constant=function(r){return arguments.length?t(Ca(e=+r),Ia(e)):e},xa(r)}function Da(){var t=Na(ra());return t.copy=function(){return ea(t,Da()).constant(t.constant())},Oi.apply(t,arguments)}function Ra(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function La(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function Ba(t){return t<0?-t*t:t*t}function za(t){var e=t(Jo,Jo),r=1;function n(){return 1===r?t(Jo,Jo):.5===r?t(La,Ba):t(Ra(r),Ra(1/r))}return e.exponent=function(t){return arguments.length?(r=+t,n()):r},xa(e)}function Fa(){var t=za(ra());return t.copy=function(){return ea(t,Fa()).exponent(t.exponent())},Oi.apply(t,arguments),t}function Ua(){return Fa.apply(null,arguments).exponent(.5)}function Wa(t){return Math.sign(t)*t*t}function $a(t){return Math.sign(t)*Math.sqrt(Math.abs(t))}function Va(){var t,e=na(),r=[0,1],n=!1;function i(r){var i=$a(e(r));return isNaN(i)?t:n?Math.round(i):i}return i.invert=function(t){return e.invert(Wa(t))},i.domain=function(t){return arguments.length?(e.domain(t),i):e.domain()},i.range=function(t){return arguments.length?(e.range((r=Array.from(t,Ko)).map(Wa)),i):r.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(n=!!t,i):n},i.clamp=function(t){return arguments.length?(e.clamp(t),i):e.clamp()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return Va(e.domain(),r).round(n).clamp(e.clamp()).unknown(t)},Oi.apply(i,arguments),xa(i)}function Ha(t,e){let r;if(void 0===e)for(const e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function qa(t,e){let r;if(void 0===e)for(const e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function Ga(t=Wi){if(t===Wi)return Xa;if("function"!=typeof t)throw new TypeError("compare is not a function");return(e,r)=>{const n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}function Xa(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function Ka(t,e,r=0,n=1/0,i){if(e=Math.floor(e),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(t.length-1,n)),!(r<=e&&e<=n))return t;for(i=void 0===i?Xa:Ga(i);n>r;){if(n-r>600){const o=n-r+1,a=e-r+1,c=Math.log(o),u=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*u*(o-u)/o)*(a-o/2<0?-1:1);Ka(t,e,Math.max(r,Math.floor(e-a*u/o+s)),Math.min(n,Math.floor(e+(o-a)*u/o+s)),i)}const o=t[e];let a=r,c=n;for(Ya(t,r,e),i(t[n],o)>0&&Ya(t,r,n);a<c;){for(Ya(t,a,c),++a,--c;i(t[a],o)<0;)++a;for(;i(t[c],o)>0;)--c}0===i(t[r],o)?Ya(t,r,c):(++c,Ya(t,c,n)),c<=e&&(r=c+1),e<=c&&(n=c-1)}return t}function Ya(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function Ja(t,e,r){if(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n=+n)>=n&&(yield n)}}(t,r)),(n=t.length)&&!isNaN(e=+e)){if(e<=0||n<2)return qa(t);if(e>=1)return Ha(t);var n,i=(n-1)*e,o=Math.floor(i),a=Ha(Ka(t,o).subarray(0,o+1));return a+(qa(t.subarray(o+1))-a)*(i-o)}}function Za(t,e,r=qi){if((n=t.length)&&!isNaN(e=+e)){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,o=Math.floor(i),a=+r(t[o],o,t);return a+(+r(t[o+1],o+1,t)-a)*(i-o)}}function Qa(){var t,e=[],r=[],n=[];function i(){var t=0,i=Math.max(1,r.length);for(n=new Array(i-1);++t<i;)n[t-1]=Za(e,t/i);return o}function o(e){return null==e||isNaN(e=+e)?t:r[Ki(n,e)]}return o.invertExtent=function(t){var i=r.indexOf(t);return i<0?[NaN,NaN]:[i>0?n[i-1]:e[0],i<n.length?n[i]:e[e.length-1]]},o.domain=function(t){if(!arguments.length)return e.slice();e=[];for(let r of t)null==r||isNaN(r=+r)||e.push(r);return e.sort(Wi),i()},o.range=function(t){return arguments.length?(r=Array.from(t),i()):r.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return n.slice()},o.copy=function(){return Qa().domain(e).range(r).unknown(t)},Oi.apply(o,arguments)}function tc(){var t,e=0,r=1,n=1,i=[.5],o=[0,1];function a(e){return null!=e&&e<=e?o[Ki(i,e,0,n)]:t}function c(){var t=-1;for(i=new Array(n);++t<n;)i[t]=((t+1)*r-(t-n)*e)/(n+1);return a}return a.domain=function(t){return arguments.length?([e,r]=t,e=+e,r=+r,c()):[e,r]},a.range=function(t){return arguments.length?(n=(o=Array.from(t)).length-1,c()):o.slice()},a.invertExtent=function(t){var a=o.indexOf(t);return a<0?[NaN,NaN]:a<1?[e,i[0]]:a>=n?[i[n-1],r]:[i[a-1],i[a]]},a.unknown=function(e){return arguments.length?(t=e,a):a},a.thresholds=function(){return i.slice()},a.copy=function(){return tc().domain([e,r]).range(o).unknown(t)},Oi.apply(xa(a),arguments)}function ec(){var t,e=[.5],r=[0,1],n=1;function i(i){return null!=i&&i<=i?r[Ki(e,i,0,n)]:t}return i.domain=function(t){return arguments.length?(e=Array.from(t),n=Math.min(e.length,r.length-1),i):e.slice()},i.range=function(t){return arguments.length?(r=Array.from(t),n=Math.min(e.length,r.length-1),i):r.slice()},i.invertExtent=function(t){var n=r.indexOf(t);return[e[n-1],e[n]]},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return ec().domain(e).range(r).unknown(t)},Oi.apply(i,arguments)}ha=ga({thousands:",",grouping:[3],currency:["$",""]}),da=ha.format,ya=ha.formatPrefix;const rc=1e3,nc=6e4,ic=36e5,oc=864e5,ac=6048e5,cc=2592e6,uc=31536e6,sc=new Date,lc=new Date;function fc(t,e,r,n){function i(e){return t(e=0===arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{const e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,o)=>{const a=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n&&o>0))return a;let c;do{a.push(c=new Date(+r)),e(r,o),t(r)}while(c<r&&r<n);return a},i.filter=r=>fc((e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)}),((t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););})),r&&(i.count=(e,n)=>(sc.setTime(+e),lc.setTime(+n),t(sc),t(lc),Math.floor(r(sc,lc))),i.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null)),i}const pc=fc((()=>{}),((t,e)=>{t.setTime(+t+e)}),((t,e)=>e-t));pc.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?fc((e=>{e.setTime(Math.floor(e/t)*t)}),((e,r)=>{e.setTime(+e+r*t)}),((e,r)=>(r-e)/t)):pc:null);pc.range;const hc=fc((t=>{t.setTime(t-t.getMilliseconds())}),((t,e)=>{t.setTime(+t+e*rc)}),((t,e)=>(e-t)/rc),(t=>t.getUTCSeconds())),dc=(hc.range,fc((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*rc)}),((t,e)=>{t.setTime(+t+e*nc)}),((t,e)=>(e-t)/nc),(t=>t.getMinutes()))),yc=(dc.range,fc((t=>{t.setUTCSeconds(0,0)}),((t,e)=>{t.setTime(+t+e*nc)}),((t,e)=>(e-t)/nc),(t=>t.getUTCMinutes()))),vc=(yc.range,fc((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*rc-t.getMinutes()*nc)}),((t,e)=>{t.setTime(+t+e*ic)}),((t,e)=>(e-t)/ic),(t=>t.getHours()))),mc=(vc.range,fc((t=>{t.setUTCMinutes(0,0,0)}),((t,e)=>{t.setTime(+t+e*ic)}),((t,e)=>(e-t)/ic),(t=>t.getUTCHours()))),gc=(mc.range,fc((t=>t.setHours(0,0,0,0)),((t,e)=>t.setDate(t.getDate()+e)),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*nc)/oc),(t=>t.getDate()-1))),bc=(gc.range,fc((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/oc),(t=>t.getUTCDate()-1))),xc=(bc.range,fc((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/oc),(t=>Math.floor(t/oc))));xc.range;function wc(t){return fc((e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)}),((t,e)=>{t.setDate(t.getDate()+7*e)}),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*nc)/ac))}const Oc=wc(0),Sc=wc(1),Ac=wc(2),jc=wc(3),Ec=wc(4),kc=wc(5),Pc=wc(6);Oc.range,Sc.range,Ac.range,jc.range,Ec.range,kc.range,Pc.range;function _c(t){return fc((e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)}),((t,e)=>(e-t)/ac))}const Mc=_c(0),Tc=_c(1),Cc=_c(2),Ic=_c(3),Nc=_c(4),Dc=_c(5),Rc=_c(6),Lc=(Mc.range,Tc.range,Cc.range,Ic.range,Nc.range,Dc.range,Rc.range,fc((t=>{t.setDate(1),t.setHours(0,0,0,0)}),((t,e)=>{t.setMonth(t.getMonth()+e)}),((t,e)=>e.getMonth()-t.getMonth()+12*(e.getFullYear()-t.getFullYear())),(t=>t.getMonth()))),Bc=(Lc.range,fc((t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)}),((t,e)=>e.getUTCMonth()-t.getUTCMonth()+12*(e.getUTCFullYear()-t.getUTCFullYear())),(t=>t.getUTCMonth()))),zc=(Bc.range,fc((t=>{t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,e)=>{t.setFullYear(t.getFullYear()+e)}),((t,e)=>e.getFullYear()-t.getFullYear()),(t=>t.getFullYear())));zc.every=t=>isFinite(t=Math.floor(t))&&t>0?fc((e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)}),((e,r)=>{e.setFullYear(e.getFullYear()+r*t)})):null;zc.range;const Fc=fc((t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)}),((t,e)=>e.getUTCFullYear()-t.getUTCFullYear()),(t=>t.getUTCFullYear()));Fc.every=t=>isFinite(t=Math.floor(t))&&t>0?fc((e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)}),((e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)})):null;Fc.range;function Uc(t,e,r,n,i,o){const a=[[hc,1,rc],[hc,5,5e3],[hc,15,15e3],[hc,30,3e4],[o,1,nc],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,ic],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,oc],[n,2,1728e5],[r,1,ac],[e,1,cc],[e,3,7776e6],[t,1,uc]];function c(e,r,n){const i=Math.abs(r-e)/n,o=Vi((([,,t])=>t)).right(a,i);if(o===a.length)return t.every(Ui(e/uc,r/uc,n));if(0===o)return pc.every(Math.max(Ui(e,r,n),1));const[c,u]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return c.every(u)}return[function(t,e,r){const n=e<t;n&&([t,e]=[e,t]);const i=r&&"function"==typeof r.range?r:c(t,e,r),o=i?i.range(t,+e+1):[];return n?o.reverse():o},c]}const[Wc,$c]=Uc(Fc,Bc,Mc,xc,mc,yc),[Vc,Hc]=Uc(zc,Lc,Oc,gc,vc,dc);function qc(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function Gc(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function Xc(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var Kc,Yc,Jc,Zc={"-":"",_:" ",0:"0"},Qc=/^\s*\d+/,tu=/^%/,eu=/[\\^$*+?|[\]().{}]/g;function ru(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",o=i.length;return n+(o<r?new Array(r-o+1).join(e)+i:i)}function nu(t){return t.replace(eu,"\\$&")}function iu(t){return new RegExp("^(?:"+t.map(nu).join("|")+")","i")}function ou(t){return new Map(t.map(((t,e)=>[t.toLowerCase(),e])))}function au(t,e,r){var n=Qc.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function cu(t,e,r){var n=Qc.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function uu(t,e,r){var n=Qc.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function su(t,e,r){var n=Qc.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function lu(t,e,r){var n=Qc.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function fu(t,e,r){var n=Qc.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function pu(t,e,r){var n=Qc.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function hu(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function du(t,e,r){var n=Qc.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function yu(t,e,r){var n=Qc.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function vu(t,e,r){var n=Qc.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function mu(t,e,r){var n=Qc.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function gu(t,e,r){var n=Qc.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function bu(t,e,r){var n=Qc.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function xu(t,e,r){var n=Qc.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function wu(t,e,r){var n=Qc.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function Ou(t,e,r){var n=Qc.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function Su(t,e,r){var n=tu.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function Au(t,e,r){var n=Qc.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function ju(t,e,r){var n=Qc.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function Eu(t,e){return ru(t.getDate(),e,2)}function ku(t,e){return ru(t.getHours(),e,2)}function Pu(t,e){return ru(t.getHours()%12||12,e,2)}function _u(t,e){return ru(1+gc.count(zc(t),t),e,3)}function Mu(t,e){return ru(t.getMilliseconds(),e,3)}function Tu(t,e){return Mu(t,e)+"000"}function Cu(t,e){return ru(t.getMonth()+1,e,2)}function Iu(t,e){return ru(t.getMinutes(),e,2)}function Nu(t,e){return ru(t.getSeconds(),e,2)}function Du(t){var e=t.getDay();return 0===e?7:e}function Ru(t,e){return ru(Oc.count(zc(t)-1,t),e,2)}function Lu(t){var e=t.getDay();return e>=4||0===e?Ec(t):Ec.ceil(t)}function Bu(t,e){return t=Lu(t),ru(Ec.count(zc(t),t)+(4===zc(t).getDay()),e,2)}function zu(t){return t.getDay()}function Fu(t,e){return ru(Sc.count(zc(t)-1,t),e,2)}function Uu(t,e){return ru(t.getFullYear()%100,e,2)}function Wu(t,e){return ru((t=Lu(t)).getFullYear()%100,e,2)}function $u(t,e){return ru(t.getFullYear()%1e4,e,4)}function Vu(t,e){var r=t.getDay();return ru((t=r>=4||0===r?Ec(t):Ec.ceil(t)).getFullYear()%1e4,e,4)}function Hu(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+ru(e/60|0,"0",2)+ru(e%60,"0",2)}function qu(t,e){return ru(t.getUTCDate(),e,2)}function Gu(t,e){return ru(t.getUTCHours(),e,2)}function Xu(t,e){return ru(t.getUTCHours()%12||12,e,2)}function Ku(t,e){return ru(1+bc.count(Fc(t),t),e,3)}function Yu(t,e){return ru(t.getUTCMilliseconds(),e,3)}function Ju(t,e){return Yu(t,e)+"000"}function Zu(t,e){return ru(t.getUTCMonth()+1,e,2)}function Qu(t,e){return ru(t.getUTCMinutes(),e,2)}function ts(t,e){return ru(t.getUTCSeconds(),e,2)}function es(t){var e=t.getUTCDay();return 0===e?7:e}function rs(t,e){return ru(Mc.count(Fc(t)-1,t),e,2)}function ns(t){var e=t.getUTCDay();return e>=4||0===e?Nc(t):Nc.ceil(t)}function is(t,e){return t=ns(t),ru(Nc.count(Fc(t),t)+(4===Fc(t).getUTCDay()),e,2)}function os(t){return t.getUTCDay()}function as(t,e){return ru(Tc.count(Fc(t)-1,t),e,2)}function cs(t,e){return ru(t.getUTCFullYear()%100,e,2)}function us(t,e){return ru((t=ns(t)).getUTCFullYear()%100,e,2)}function ss(t,e){return ru(t.getUTCFullYear()%1e4,e,4)}function ls(t,e){var r=t.getUTCDay();return ru((t=r>=4||0===r?Nc(t):Nc.ceil(t)).getUTCFullYear()%1e4,e,4)}function fs(){return"+0000"}function ps(){return"%"}function hs(t){return+t}function ds(t){return Math.floor(+t/1e3)}function ys(t){return new Date(t)}function vs(t){return t instanceof Date?+t:+new Date(+t)}function ms(t,e,r,n,i,o,a,c,u,s){var l=na(),f=l.invert,p=l.domain,h=s(".%L"),d=s(":%S"),y=s("%I:%M"),v=s("%I %p"),m=s("%a %d"),g=s("%b %d"),b=s("%B"),x=s("%Y");function w(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:o(t)<t?v:n(t)<t?i(t)<t?m:g:r(t)<t?b:x)(t)}return l.invert=function(t){return new Date(f(t))},l.domain=function(t){return arguments.length?p(Array.from(t,vs)):p().map(ys)},l.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},l.tickFormat=function(t,e){return null==e?w:s(e)},l.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(Sa(r,t)):l},l.copy=function(){return ea(l,ms(t,e,r,n,i,o,a,c,u,s))},l}function gs(){return Oi.apply(ms(Vc,Hc,zc,Lc,Oc,gc,vc,dc,hc,Yc).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function bs(){return Oi.apply(ms(Wc,$c,Fc,Bc,Mc,bc,mc,yc,hc,Jc).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function xs(){var t,e,r,n,i,o=0,a=1,c=Jo,u=!1;function s(e){return null==e||isNaN(e=+e)?i:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function l(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),s):[c(0),c(1)]}}return s.domain=function(i){return arguments.length?([o,a]=i,t=n(o=+o),e=n(a=+a),r=t===e?0:1/(e-t),s):[o,a]},s.clamp=function(t){return arguments.length?(u=!!t,s):u},s.interpolator=function(t){return arguments.length?(c=t,s):c},s.range=l(Go),s.rangeRound=l(Xo),s.unknown=function(t){return arguments.length?(i=t,s):i},function(i){return n=i,t=i(o),e=i(a),r=t===e?0:1/(e-t),s}}function ws(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function Os(){var t=xa(xs()(Jo));return t.copy=function(){return ws(t,Os())},Si.apply(t,arguments)}function Ss(){var t=Ma(xs()).domain([1,10]);return t.copy=function(){return ws(t,Ss()).base(t.base())},Si.apply(t,arguments)}function As(){var t=Na(xs());return t.copy=function(){return ws(t,As()).constant(t.constant())},Si.apply(t,arguments)}function js(){var t=za(xs());return t.copy=function(){return ws(t,js()).exponent(t.exponent())},Si.apply(t,arguments)}function Es(){return js.apply(null,arguments).exponent(.5)}function ks(){var t=[],e=Jo;function r(r){if(null!=r&&!isNaN(r=+r))return e((Ki(t,r,1)-1)/(t.length-1))}return r.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let r of e)null==r||isNaN(r=+r)||t.push(r);return t.sort(Wi),r},r.interpolator=function(t){return arguments.length?(e=t,r):e},r.range=function(){return t.map(((r,n)=>e(n/(t.length-1))))},r.quantiles=function(e){return Array.from({length:e+1},((r,n)=>Ja(t,n/e)))},r.copy=function(){return ks(e).domain(t)},Si.apply(r,arguments)}function Ps(t,e){void 0===e&&(e=t,t=Go);for(var r=0,n=e.length-1,i=e[0],o=new Array(n<0?0:n);r<n;)o[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return o[e](t-e)}}function _s(){var t,e,r,n,i,o,a,c=0,u=.5,s=1,l=1,f=Jo,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+o(t))-e)*(l*t<l*e?n:i),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=Ps(t,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,s]=a,t=o(c=+c),e=o(u=+u),r=o(s=+s),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),l=e<t?-1:1,h):[c,u,s]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(Go),h.rangeRound=d(Xo),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return o=a,t=a(c),e=a(u),r=a(s),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),l=e<t?-1:1,h}}function Ms(){var t=xa(_s()(Jo));return t.copy=function(){return ws(t,Ms())},Si.apply(t,arguments)}function Ts(){var t=Ma(_s()).domain([.1,1,10]);return t.copy=function(){return ws(t,Ts()).base(t.base())},Si.apply(t,arguments)}function Cs(){var t=Na(_s());return t.copy=function(){return ws(t,Cs()).constant(t.constant())},Si.apply(t,arguments)}function Is(){var t=za(_s());return t.copy=function(){return ws(t,Is()).exponent(t.exponent())},Si.apply(t,arguments)}function Ns(){return Is.apply(null,arguments).exponent(.5)}function Ds(t,e){if((i=t.length)>1)for(var r,n,i,o=1,a=t[e[0]],c=a.length;o<i;++o)for(n=a,a=t[e[o]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}!function(t){Kc=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,o=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,s=iu(i),l=ou(i),f=iu(o),p=ou(o),h=iu(a),d=ou(a),y=iu(c),v=ou(c),m=iu(u),g=ou(u),b={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:Eu,e:Eu,f:Tu,g:Wu,G:Vu,H:ku,I:Pu,j:_u,L:Mu,m:Cu,M:Iu,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:hs,s:ds,S:Nu,u:Du,U:Ru,V:Bu,w:zu,W:Fu,x:null,X:null,y:Uu,Y:$u,Z:Hu,"%":ps},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:qu,e:qu,f:Ju,g:us,G:ls,H:Gu,I:Xu,j:Ku,L:Yu,m:Zu,M:Qu,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:hs,s:ds,S:ts,u:es,U:rs,V:is,w:os,W:as,x:null,X:null,y:cs,Y:ss,Z:fs,"%":ps},w={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return A(t,e,r,n)},d:vu,e:vu,f:Ou,g:pu,G:fu,H:gu,I:gu,j:mu,L:wu,m:yu,M:bu,p:function(t,e,r){var n=s.exec(e.slice(r));return n?(t.p=l.get(n[0].toLowerCase()),r+n[0].length):-1},q:du,Q:Au,s:ju,S:xu,u:cu,U:uu,V:su,w:au,W:lu,x:function(t,e,n){return A(t,r,e,n)},X:function(t,e,r){return A(t,n,e,r)},y:pu,Y:fu,Z:hu,"%":Su};function O(t,e){return function(r){var n,i,o,a=[],c=-1,u=0,s=t.length;for(r instanceof Date||(r=new Date(+r));++c<s;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(i=Zc[n=t.charAt(++c)])?n=t.charAt(++c):i="e"===n?" ":"0",(o=e[n])&&(n=o(r,i)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function S(t,e){return function(r){var n,i,o=Xc(1900,void 0,1);if(A(o,t,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(e&&!("Z"in o)&&(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(i=(n=Gc(Xc(o.y,0,1))).getUTCDay(),n=i>4||0===i?Tc.ceil(n):Tc(n),n=bc.offset(n,7*(o.V-1)),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(i=(n=qc(Xc(o.y,0,1))).getDay(),n=i>4||0===i?Sc.ceil(n):Sc(n),n=gc.offset(n,7*(o.V-1)),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),i="Z"in o?Gc(Xc(o.y,0,1)).getUTCDay():qc(Xc(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,Gc(o)):qc(o)}}function A(t,e,r,n){for(var i,o,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return-1;if(37===(i=e.charCodeAt(a++))){if(i=e.charAt(a++),!(o=w[i in Zc?e.charAt(a++):i])||(n=o(t,r,n))<0)return-1}else if(i!=r.charCodeAt(n++))return-1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(e,b),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",b);return e.toString=function(){return t},e},parse:function(t){var e=S(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=S(t+="",!0);return e.toString=function(){return t},e}}}(t),Yc=Kc.format,Kc.parse,Jc=Kc.utcFormat,Kc.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});Array.prototype.slice;function Rs(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function Ls(t){for(var e=t.length,r=new Array(e);--e>=0;)r[e]=e;return r}function Bs(t,e){return t[e]}function zs(t){const e=[];return e.key=t,e}var Fs=o(9887),Us=o.n(Fs);function Ws(t){return function(t){if(Array.isArray(t))return $s(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return $s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $s(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Vs=function(t){return t},Hs={"@@functional/placeholder":!0},qs=function(t){return t===Hs},Gs=function(t){return function e(){return 0===arguments.length||1===arguments.length&&qs(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},Xs=function t(e,r){return 1===e?r:Gs((function(){for(var n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];var a=i.filter((function(t){return t!==Hs})).length;return a>=e?r.apply(void 0,i):t(e-a,Gs((function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=i.map((function(t){return qs(t)?e.shift():t}));return r.apply(void 0,Ws(o).concat(e))})))}))},Ks=function(t){return Xs(t.length,t)},Ys=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},Js=Ks((function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map((function(t){return e[t]})).map(t)})),Zs=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return Vs;var n=e.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce((function(t,e){return e(t)}),i.apply(void 0,arguments))}},Qs=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},tl=function(t){var e=null,r=null;return function(){for(var n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return e&&i.every((function(t,r){return t===e[r]}))?r:(e=i,r=t.apply(void 0,i))}};var el=Ks((function(t,e,r){var n=+t;return n+r*(+e-n)})),rl=Ks((function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)})),nl=Ks((function(t,e,r){var n=e-+t;return n=n||1/0,Math.max(0,Math.min(1,(r-t)/n))}));const il={rangeStep:function(t,e,r){for(var n=new(Us())(t),i=0,o=[];n.lt(e)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o},getDigitCount:function(t){return 0===t?1:Math.floor(new(Us())(t).abs().log(10).toNumber())+1},interpolateNumber:el,uninterpolateNumber:rl,uninterpolateTruncation:nl};function ol(t){return function(t){if(Array.isArray(t))return ul(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||cl(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function al(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var r=[],n=!0,i=!1,o=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==c.return||c.return()}finally{if(i)throw o}}return r}(t,e)||cl(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cl(t,e){if(t){if("string"==typeof t)return ul(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ul(t,e):void 0}}function ul(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function sl(t){var e=al(t,2),r=e[0],n=e[1],i=r,o=n;return r>n&&(i=n,o=r),[i,o]}function ll(t,e,r){if(t.lte(0))return new(Us())(0);var n=il.getDigitCount(t.toNumber()),i=new(Us())(10).pow(n),o=t.div(i),a=1!==n?.05:.1,c=new(Us())(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return e?c:new(Us())(Math.ceil(c))}function fl(t,e,r){var n=1,i=new(Us())(t);if(!i.isint()&&r){var o=Math.abs(t);o<1?(n=new(Us())(10).pow(il.getDigitCount(t)-1),i=new(Us())(Math.floor(i.div(n).toNumber())).mul(n)):o>1&&(i=new(Us())(Math.floor(t)))}else 0===t?i=new(Us())(Math.floor((e-1)/2)):r||(i=new(Us())(Math.floor(t)));var a=Math.floor((e-1)/2);return Zs(Js((function(t){return i.add(new(Us())(t-a).mul(n)).toNumber()})),Ys)(0,e)}function pl(t,e,r,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new(Us())(0),tickMin:new(Us())(0),tickMax:new(Us())(0)};var o,a=ll(new(Us())(e).sub(t).div(r-1),n,i);o=t<=0&&e>=0?new(Us())(0):(o=new(Us())(t).add(e).div(2)).sub(new(Us())(o).mod(a));var c=Math.ceil(o.sub(t).div(a).toNumber()),u=Math.ceil(new(Us())(e).sub(o).div(a).toNumber()),s=c+u+1;return s>r?pl(t,e,r,n,i+1):(s<r&&(u=e>0?u+(r-s):u,c=e>0?c:c+(r-s)),{step:a,tickMin:o.sub(new(Us())(c).mul(a)),tickMax:o.add(new(Us())(u).mul(a))})}var hl=tl((function(t){var e=al(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(i,2),c=sl([r,n]),u=al(c,2),s=u[0],l=u[1];if(s===-1/0||l===1/0){var f=l===1/0?[s].concat(ol(Ys(0,i-1).map((function(){return 1/0})))):[].concat(ol(Ys(0,i-1).map((function(){return-1/0}))),[l]);return r>n?Qs(f):f}if(s===l)return fl(s,i,o);var p=pl(s,l,a,o),h=p.step,d=p.tickMin,y=p.tickMax,v=il.rangeStep(d,y.add(new(Us())(.1).mul(h)),h);return r>n?Qs(v):v})),dl=(tl((function(t){var e=al(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(i,2),c=sl([r,n]),u=al(c,2),s=u[0],l=u[1];if(s===-1/0||l===1/0)return[r,n];if(s===l)return fl(s,i,o);var f=ll(new(Us())(l).sub(s).div(a-1),o,0),p=Zs(Js((function(t){return new(Us())(s).add(new(Us())(t).mul(f)).toNumber()})),Ys),h=p(0,a).filter((function(t){return t>=s&&t<=l}));return r>n?Qs(h):h})),tl((function(t,e){var r=al(t,2),n=r[0],i=r[1],o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=sl([n,i]),c=al(a,2),u=c[0],s=c[1];if(u===-1/0||s===1/0)return[n,i];if(u===s)return[u];var l=Math.max(e,2),f=ll(new(Us())(s).sub(u).div(l-1),o,0),p=[].concat(ol(il.rangeStep(new(Us())(u),new(Us())(s).sub(new(Us())(.99).mul(f)),f)),[s]);return n>i?Qs(p):p}))),yl=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function vl(){return vl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},vl.apply(this,arguments)}function ml(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return gl(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gl(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function gl(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function bl(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function xl(t){var e=t.offset,n=t.layout,i=t.width,o=t.dataKey,a=t.data,c=t.dataPointFormatter,u=t.xAxis,s=t.yAxis,l=bl(t,yl),f=rt(l),p=a.map((function(t,a){var l=c(t,o),p=l.x,h=l.y,d=l.value,y=l.errorVal;if(!y)return null;var v,m,g=[];if(Array.isArray(y)){var b=ml(y,2);v=b[0],m=b[1]}else v=m=y;if("vertical"===n){var x=u.scale,w=h+e,O=w+i,S=w-i,A=x(d-v),j=x(d+m);g.push({x1:j,y1:O,x2:j,y2:S}),g.push({x1:A,y1:w,x2:j,y2:w}),g.push({x1:A,y1:O,x2:A,y2:S})}else if("horizontal"===n){var E=s.scale,k=p+e,P=k-i,_=k+i,M=E(d-v),T=E(d+m);g.push({x1:P,y1:T,x2:_,y2:T}),g.push({x1:k,y1:M,x2:k,y2:T}),g.push({x1:P,y1:M,x2:_,y2:M})}return r().createElement(ht,vl({className:"recharts-errorBar",key:"bar-".concat(a)},f),g.map((function(t,e){return r().createElement("line",vl({},t,{key:"line-".concat(e)}))})))}));return r().createElement(ht,{className:"recharts-errorBars"},p)}function wl(t){return wl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wl(t)}function Ol(t){return function(t){if(Array.isArray(t))return Sl(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Sl(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Sl(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Sl(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Al(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function jl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Al(Object(r),!0).forEach((function(e){El(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Al(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function El(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==wl(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==wl(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===wl(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function kl(t,e,r){return v()(t)||v()(e)?r:k(e)?d()(t,e,r):l()(e)?e(t):r}function Pl(t,e,r,n){var i=xi()(t,(function(t){return kl(t,e)}));if("number"===r){var o=i.filter((function(t){return E(t)||parseFloat(t)}));return o.length?[gi()(o),vi()(o)]:[1/0,-1/0]}return(n?i.filter((function(t){return!v()(t)})):i).map((function(t){return k(t)||t instanceof Date?t:""}))}xl.defaultProps={stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"},xl.displayName="ErrorBar";var _l=function(t){var e,r=t.type.displayName,n=t.props,i=n.stroke,o=n.fill;switch(r){case"Line":e=i;break;case"Area":case"Radar":e=i&&"none"!==i?i:o;break;default:e=o}return e},Ml=function(t){var e,r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,o=t.legendContent,a=J(r,ke);return a?(e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===o?(n||[]).reduce((function(t,e){var r=e.item,n=e.props,i=n.sectors||n.data||[];return t.concat(i.map((function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}})))}),[]):(n||[]).map((function(t){var e=t.item,r=e.props,n=r.dataKey,i=r.name,o=r.legendType;return{inactive:r.hide,dataKey:n,type:a.props.iconType||o||"square",color:_l(e),value:i||n,payload:e.props}})),jl(jl(jl({},a.props),ke.getWithHeight(a,i)),{},{payload:e,item:a})):null},Tl=function(t,e,r,n,i){var o=Y(e.props.children,xl).filter((function(t){return function(t,e,r){return!!v()(e)||("horizontal"===t?"yAxis"===e:"vertical"===t||"x"===r?"xAxis"===e:"y"!==r||"yAxis"===e)}(n,i,t.props.direction)}));if(o&&o.length){var a=o.map((function(t){return t.props.dataKey}));return t.reduce((function(t,e){var n=kl(e,r,0),i=g()(n)?[gi()(n),vi()(n)]:[n,n],o=a.reduce((function(t,r){var n=kl(e,r,0),o=i[0]-Math.abs(g()(n)?n[0]:n),a=i[1]+Math.abs(g()(n)?n[1]:n);return[Math.min(o,t[0]),Math.max(a,t[1])]}),[1/0,-1/0]);return[Math.min(o[0],t[0]),Math.max(o[1],t[1])]}),[1/0,-1/0])}return null},Cl=function(t,e,r,n,i){var o=e.map((function(e){var o=e.props.dataKey;return"number"===r&&o&&Tl(t,e,o,n)||Pl(t,o,r,i)}));if("number"===r)return o.reduce((function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}),[1/0,-1/0]);var a={};return o.reduce((function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t}),[])},Il=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},Nl=function(t,e,r){var n,i,o=t.map((function(t){return t.coordinate===e&&(n=!0),t.coordinate===r&&(i=!0),t.coordinate}));return n||o.push(e),i||o.push(r),o},Dl=function(t,e,r){if(!t)return null;var n=t.scale,i=t.duplicateDomain,o=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===o&&n.bandwidth?n.bandwidth()/c:0;return u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*A(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks)?(t.ticks||t.niceTicks).map((function(t){var e=i?i.indexOf(t):t;return{coordinate:n(e)+u,value:t,offset:u}})).filter((function(t){return!w()(t.coordinate)})):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map((function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}})):n.ticks&&!r?n.ticks(t.tickCount).map((function(t){return{coordinate:n(t)+u,value:t,offset:u}})):n.domain().map((function(t,e){return{coordinate:n(t)+u,value:i?i[t]:t,index:e,offset:u}}))},Rl=function(t,e,r){var n;return l()(r)?n=r:l()(e)&&(n=e),l()(t)||n?function(e,r,i,o){l()(t)&&t(e,r,i,o),l()(n)&&n(e,r,i,o)}:null},Ll=function(e,r,n){var i=e.scale,o=e.type,a=e.layout,c=e.axisType;if("auto"===i)return"radial"===a&&"radiusAxis"===c?{scale:Ci(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:wa(),realScaleType:"linear"}:"category"===o&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:Ni(),realScaleType:"point"}:"category"===o?{scale:Ci(),realScaleType:"band"}:{scale:wa(),realScaleType:"linear"};if(p()(i)){var u="scale".concat(mt()(i));return{scale:(t[u]||Ni)(),realScaleType:t[u]?u:"point"}}return l()(i)?{scale:i}:{scale:Ni(),realScaleType:"point"}},Bl=1e-4,zl=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-Bl,o=Math.max(n[0],n[1])+Bl,a=t(e[0]),c=t(e[r-1]);(a<i||a>o||c<i||c>o)&&t.domain([e[0],e[r-1]])}},Fl=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},Ul=function(t,e){if(!e||2!==e.length||!E(e[0])||!E(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!E(t[0])||t[0]<r)&&(i[0]=r),(!E(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},Wl={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0,a=0;a<e;++a){var c=w()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1]):(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,o=0,a=t[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=t[r][o][1]||0;if(i)for(r=0;r<n;++r)t[r][o][1]/=i}Ds(t,e)}},none:Ds,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],o=i.length;n<o;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;i[n][1]+=i[n][0]=-c/2}Ds(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var c=0,u=0,s=0;c<i;++c){for(var l=t[e[c]],f=l[a][1]||0,p=(f-(l[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,s+=p*f}r[a-1][1]+=r[a-1][0]=o,u&&(o-=s/u)}r[a-1][1]+=r[a-1][0]=o,Ds(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0;o<e;++o){var a=w()(t[o][r][1])?t[o][r][0]:t[o][r][1];a>=0?(t[o][r][0]=i,t[o][r][1]=i+a,i=t[o][r][1]):(t[o][r][0]=0,t[o][r][1]=0)}}},$l=function(t,e,r){var n=e.map((function(t){return t.props.dataKey})),i=function(){var t=Ft([]),e=Ls,r=Ds,n=Bs;function i(i){var o,a,c=Array.from(t.apply(this,arguments),zs),u=c.length,s=-1;for(const t of i)for(o=0,++s;o<u;++o)(c[o][s]=[0,+n(t,c[o].key,s,i)]).data=t;for(o=0,a=Rs(e(c));o<u;++o)c[a[o]].index=o;return r(c,a),c}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:Ft(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:Ft(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?Ls:"function"==typeof t?t:Ft(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?Ds:t,i):r},i}().keys(n).value((function(t,e){return+kl(t,e,0)})).order(Ls).offset(Wl[r]);return i(t)},Vl=function(t,e){var r=e.realScaleType,n=e.type,i=e.tickCount,o=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(i&&"number"===n&&o&&("auto"===o[0]||"auto"===o[1])){var u=t.domain();if(!u.length)return null;var s=hl(u,i,a);return t.domain([gi()(s),vi()(s)]),{niceTicks:s}}if(i&&"number"===n){var l=t.domain();return{niceTicks:dl(l,i,a)}}return null},Hl=function(t){var e=t.axis,r=t.ticks,n=t.bandSize,i=t.entry,o=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!v()(i[e.dataKey])){var c=I(r,"value",i[e.dataKey]);if(c)return c.coordinate+n/2}return r[o]?r[o].coordinate+n/2:null}var u=kl(i,v()(a)?e.dataKey:a);return v()(u)?null:e.scale(u)},ql=function(t){var e=t.axis,r=t.ticks,n=t.offset,i=t.bandSize,o=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=kl(o,e.dataKey,e.domain[a]);return v()(c)?null:e.scale(c)-i/2+n},Gl=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},Xl=function(t,e,r){return Object.keys(t).reduce((function(n,i){var o=t[i].stackedData.reduce((function(t,n){var i=n.slice(e,r+1).reduce((function(t,e){return[gi()(e.concat([t[0]]).filter(E)),vi()(e.concat([t[1]]).filter(E))]}),[1/0,-1/0]);return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]}),[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]}),[1/0,-1/0]).map((function(t){return t===1/0||t===-1/0?0:t}))},Kl=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Yl=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Jl=function(t,e,r){if(l()(t))return t(e,r);if(!g()(t))return e;var n=[];if(E(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(Kl.test(t[0])){var i=+Kl.exec(t[0])[1];n[0]=e[0]-i}else l()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(E(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(Yl.test(t[1])){var o=+Yl.exec(t[1])[1];n[1]=e[1]+o}else l()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},Zl=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var i=en()(e,(function(t){return t.coordinate})),o=1/0,a=1,c=i.length;a<c;a++){var u=i[a],s=i[a-1];o=Math.min((u.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0},Ql=function(t,e,r){return t&&t.length?di()(t,d()(r,"type.defaultProps.domain"))?e:t:e},tf=function(t,e){var r=t.props,n=r.dataKey,i=r.name,o=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType;return jl(jl({},rt(t)),{},{dataKey:n,unit:o,formatter:a,name:i||n,color:_l(t),value:kl(e,n),type:c,payload:e,chartType:u})};function ef(t){return ef="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ef(t)}function rf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function nf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rf(Object(r),!0).forEach((function(e){of(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rf(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function of(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==ef(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ef(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===ef(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function af(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return cf(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cf(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cf(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var uf=Math.PI/180,sf=function(t){return 180*t/Math.PI},lf=function(t,e,r,n){return{x:t+Math.cos(-uf*n)*r,y:e+Math.sin(-uf*n)*r}},ff=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},pf=function(t,e,r,n,i){var o=t.width,a=t.height,c=t.startAngle,u=t.endAngle,s=M(t.cx,o,o/2),l=M(t.cy,a,a/2),f=ff(o,a,r),p=M(t.innerRadius,f,0),h=M(t.outerRadius,f,.8*f);return Object.keys(e).reduce((function(t,r){var o,a=e[r],f=a.domain,d=a.reversed;if(v()(a.range))"angleAxis"===n?o=[c,u]:"radiusAxis"===n&&(o=[p,h]),d&&(o=[o[1],o[0]]);else{var y=af(o=a.range,2);c=y[0],u=y[1]}var m=Ll(a,i),g=m.realScaleType,b=m.scale;b.domain(f).range(o),zl(b);var x=Vl(b,nf(nf({},a),{},{realScaleType:g})),w=nf(nf(nf({},a),x),{},{range:o,radius:h,realScaleType:g,scale:b,cx:s,cy:l,innerRadius:p,outerRadius:h,startAngle:c,endAngle:u});return nf(nf({},t),{},of({},r,w))}),{})},hf=function(t,e){var r=t.x,n=t.y,i=e.cx,o=e.cy,a=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return Math.sqrt(Math.pow(r-i,2)+Math.pow(n-o,2))}({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a};var c=(r-i)/a,u=Math.acos(c);return n>o&&(u=2*Math.PI-u),{radius:a,angle:sf(u),angleInRadian:u}},df=function(t,e){var r=e.startAngle,n=e.endAngle,i=Math.floor(r/360),o=Math.floor(n/360);return t+360*Math.min(i,o)},yf=function(t,e){var r=t.x,n=t.y,i=hf({x:r,y:n},e),o=i.radius,a=i.angle,c=e.innerRadius,u=e.outerRadius;if(o<c||o>u)return!1;if(0===o)return!0;var s,l=function(t){var e=t.startAngle,r=t.endAngle,n=Math.floor(e/360),i=Math.floor(r/360),o=Math.min(n,i);return{startAngle:e-360*o,endAngle:r-360*o}}(e),f=l.startAngle,p=l.endAngle,h=a;if(f<=p){for(;h>p;)h-=360;for(;h<f;)h+=360;s=h>=f&&h<=p}else{for(;h>f;)h-=360;for(;h<p;)h+=360;s=h>=p&&h<=f}return s?nf(nf({},e),{},{radius:o,angle:df(h,e)}):null};function vf(t){return vf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vf(t)}function mf(t){return function(t){if(Array.isArray(t))return gf(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return gf(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gf(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function gf(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function bf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function xf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?bf(Object(r),!0).forEach((function(e){wf(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):bf(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function wf(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==vf(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==vf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===vf(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Of(){return Of=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Of.apply(this,arguments)}var Sf=function(t,e,n){var o,a,c=t.position,u=t.viewBox,s=t.offset,l=t.className,f=u,p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius,m=f.startAngle,g=f.endAngle,b=f.clockWise,x=(d+y)/2,w=function(t,e){return A(e-t)*Math.min(Math.abs(e-t),360)}(m,g),O=w>=0?1:-1;"insideStart"===c?(o=m+O*s,a=b):"insideEnd"===c?(o=g-O*s,a=!b):"end"===c&&(o=g+O*s,a=b),a=w<=0?a:!a;var S=lf(p,h,x,o),j=lf(p,h,x,o+359*(a?1:-1)),E="M".concat(S.x,",").concat(S.y,"\n    A").concat(x,",").concat(x,",0,1,").concat(a?0:1,",\n    ").concat(j.x,",").concat(j.y),k=v()(t.id)?_("recharts-radial-line-"):t.id;return r().createElement("text",Of({},n,{dominantBaseline:"central",className:i()("recharts-radial-bar-label",l)}),r().createElement("defs",null,r().createElement("path",{id:k,d:E})),r().createElement("textPath",{xlinkHref:"#".concat(k)},e))};function Af(t){var n,o=t.viewBox,a=t.position,c=t.value,s=t.children,f=t.content,p=t.className,h=void 0===p?"":p,d=t.textBreakAll;if(!o||v()(c)&&v()(s)&&!(0,e.isValidElement)(f)&&!l()(f))return null;if((0,e.isValidElement)(f))return(0,e.cloneElement)(f,t);if(l()(f)){if(n=(0,e.createElement)(f,t),(0,e.isValidElement)(n))return n}else n=function(t){var e=t.value,r=t.formatter,n=v()(t.children)?e:t.children;return l()(r)?r(n):n}(t);var y=function(t){return"cx"in t&&E(t.cx)}(o),m=rt(t,!0);if(y&&("insideStart"===a||"insideEnd"===a||"end"===a))return Sf(t,n,m);var g=y?function(t){var e=t.viewBox,r=t.offset,n=t.position,i=e,o=i.cx,a=i.cy,c=i.innerRadius,u=i.outerRadius,s=(i.startAngle+i.endAngle)/2;if("outside"===n){var l=lf(o,a,u+r,s),f=l.x;return{x:f,y:l.y,textAnchor:f>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=lf(o,a,(c+u)/2,s);return{x:p.x,y:p.y,textAnchor:"middle",verticalAnchor:"middle"}}(t):function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,i=t.position,o=e,a=o.x,c=o.y,s=o.width,l=o.height,f=l>=0?1:-1,p=f*n,h=f>0?"end":"start",d=f>0?"start":"end",y=s>=0?1:-1,v=y*n,m=y>0?"end":"start",g=y>0?"start":"end";if("top"===i)return xf(xf({},{x:a+s/2,y:c-f*n,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(c-r.y,0),width:s}:{});if("bottom"===i)return xf(xf({},{x:a+s/2,y:c+l+p,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(c+l),0),width:s}:{});if("left"===i){var b={x:a-v,y:c+l/2,textAnchor:m,verticalAnchor:"middle"};return xf(xf({},b),r?{width:Math.max(b.x-r.x,0),height:l}:{})}if("right"===i){var x={x:a+s+v,y:c+l/2,textAnchor:g,verticalAnchor:"middle"};return xf(xf({},x),r?{width:Math.max(r.x+r.width-x.x,0),height:l}:{})}var w=r?{width:s,height:l}:{};return"insideLeft"===i?xf({x:a+v,y:c+l/2,textAnchor:g,verticalAnchor:"middle"},w):"insideRight"===i?xf({x:a+s-v,y:c+l/2,textAnchor:m,verticalAnchor:"middle"},w):"insideTop"===i?xf({x:a+s/2,y:c+p,textAnchor:"middle",verticalAnchor:d},w):"insideBottom"===i?xf({x:a+s/2,y:c+l-p,textAnchor:"middle",verticalAnchor:h},w):"insideTopLeft"===i?xf({x:a+v,y:c+p,textAnchor:g,verticalAnchor:d},w):"insideTopRight"===i?xf({x:a+s-v,y:c+p,textAnchor:m,verticalAnchor:d},w):"insideBottomLeft"===i?xf({x:a+v,y:c+l-p,textAnchor:g,verticalAnchor:h},w):"insideBottomRight"===i?xf({x:a+s-v,y:c+l-p,textAnchor:m,verticalAnchor:h},w):u()(i)&&(E(i.x)||j(i.x))&&(E(i.y)||j(i.y))?xf({x:a+M(i.x,s),y:c+M(i.y,l),textAnchor:"end",verticalAnchor:"end"},w):xf({x:a+s/2,y:c+l/2,textAnchor:"middle",verticalAnchor:"middle"},w)}(t);return r().createElement(pi,Of({className:i()("recharts-label",h)},m,g,{breakAll:d}),n)}Af.displayName="Label",Af.defaultProps={offset:5};var jf=function(t){var e=t.cx,r=t.cy,n=t.angle,i=t.startAngle,o=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,s=t.outerRadius,l=t.x,f=t.y,p=t.top,h=t.left,d=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(E(d)&&E(y)){if(E(l)&&E(f))return{x:l,y:f,width:d,height:y};if(E(p)&&E(h))return{x:p,y:h,width:d,height:y}}return E(l)&&E(f)?{x:l,y:f,width:0,height:0}:E(e)&&E(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:u||0,outerRadius:s||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}},Ef=function(t,n){return t?!0===t?r().createElement(Af,{key:"label-implicit",viewBox:n}):k(t)?r().createElement(Af,{key:"label-implicit",viewBox:n,value:t}):(0,e.isValidElement)(t)?t.type===Af?(0,e.cloneElement)(t,{key:"label-implicit",viewBox:n}):r().createElement(Af,{key:"label-implicit",content:t,viewBox:n}):l()(t)?r().createElement(Af,{key:"label-implicit",content:t,viewBox:n}):u()(t)?r().createElement(Af,Of({viewBox:n},t,{key:"label-implicit"})):null:null};Af.parseViewBox=jf,Af.renderCallByParent=function(t,r){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var i=t.children,o=jf(t),a=Y(i,Af).map((function(t,n){return(0,e.cloneElement)(t,{viewBox:r||o,key:"label-".concat(n)})}));if(!n)return a;var c=Ef(t.label,r||o);return[c].concat(mf(a))};var kf=o(928),Pf=o.n(kf);function _f(t){return _f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_f(t)}var Mf=["data","valueAccessor","dataKey","clockWise","id","textBreakAll"];function Tf(t){return function(t){if(Array.isArray(t))return Cf(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Cf(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Cf(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Cf(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function If(){return If=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},If.apply(this,arguments)}function Nf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Df(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Nf(Object(r),!0).forEach((function(e){Rf(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Nf(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Rf(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==_f(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==_f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===_f(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Lf(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var Bf={valueAccessor:function(t){return g()(t.value)?Pf()(t.value):t.value}};function zf(t){var e=t.data,n=t.valueAccessor,i=t.dataKey,o=t.clockWise,a=t.id,c=t.textBreakAll,u=Lf(t,Mf);return e&&e.length?r().createElement(ht,{className:"recharts-label-list"},e.map((function(t,e){var s=v()(i)?n(t,e):kl(t&&t.payload,i),l=v()(a)?{}:{id:"".concat(a,"-").concat(e)};return r().createElement(Af,If({},rt(t,!0),u,l,{parentViewBox:t.parentViewBox,index:e,value:s,textBreakAll:c,viewBox:Af.parseViewBox(v()(o)?t:Df(Df({},t),{},{clockWise:o})),key:"label-".concat(e)}))}))):null}function Ff(t,e){return t?!0===t?r().createElement(zf,{key:"labelList-implicit",data:e}):r().isValidElement(t)||l()(t)?r().createElement(zf,{key:"labelList-implicit",data:e,content:t}):u()(t)?r().createElement(zf,If({data:e},t,{key:"labelList-implicit"})):null:null}zf.displayName="LabelList",zf.renderCallByParent=function(t,r){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var i=t.children,o=Y(i,zf).map((function(t,n){return(0,e.cloneElement)(t,{data:r,key:"labelList-".concat(n)})}));if(!n)return o;var a=Ff(t.label,r);return[a].concat(Tf(o))},zf.defaultProps=Bf;var Uf=["component"];function Wf(t){return Wf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wf(t)}function $f(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function Vf(t){var n,i=t.component,o=$f(t,Uf);return(0,e.isValidElement)(i)?n=(0,e.cloneElement)(i,o):l()(i)?n=(0,e.createElement)(i,o):Rn(!1,"Customized's props `component` must be React.element or Function, but got %s.",Wf(i)),r().createElement(ht,{className:"recharts-customized-wrapper"},n)}function Hf(){return Hf=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Hf.apply(this,arguments)}Vf.displayName="Customized";var qf=function(t){var e=t.cx,r=t.cy,n=t.radius,i=t.angle,o=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,s=c*(a?1:-1)+n,l=Math.asin(c/s)/uf,f=u?i:i+o*l,p=u?i-o*l:i;return{center:lf(e,r,s,f),circleTangency:lf(e,r,n,f),lineTangency:lf(e,r,s*Math.cos(l*uf),p),theta:l}},Gf=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.startAngle,a=function(t,e){return A(e-t)*Math.min(Math.abs(e-t),359.999)}(o,t.endAngle),c=o+a,u=lf(e,r,i,o),s=lf(e,r,i,c),l="M ".concat(u.x,",").concat(u.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(a)>180),",").concat(+(o>c),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var f=lf(e,r,n,o),p=lf(e,r,n,c);l+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(a)>180),",").concat(+(o<=c),",\n            ").concat(f.x,",").concat(f.y," Z")}else l+="L ".concat(e,",").concat(r," Z");return l},Xf=function(t){var e=t.cx,n=t.cy,o=t.innerRadius,a=t.outerRadius,c=t.cornerRadius,u=t.forceCornerRadius,s=t.cornerIsExternal,l=t.startAngle,f=t.endAngle,p=t.className;if(a<o||l===f)return null;var h,d=i()("recharts-sector",p),y=a-o,v=M(c,y,0,!0);return h=v>0&&Math.abs(l-f)<360?function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,s=t.endAngle,l=A(s-u),f=qf({cx:e,cy:r,radius:i,angle:u,sign:l,cornerRadius:o,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,d=f.theta,y=qf({cx:e,cy:r,radius:i,angle:s,sign:-l,cornerRadius:o,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,g=y.theta,b=c?Math.abs(u-s):Math.abs(u-s)-d-g;if(b<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*-o,",0\n      "):Gf({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:s});var x="M ".concat(h.x,",").concat(h.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(b>180),",").concat(+(l<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=qf({cx:e,cy:r,radius:n,angle:u,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),O=w.circleTangency,S=w.lineTangency,j=w.theta,E=qf({cx:e,cy:r,radius:n,angle:s,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),k=E.circleTangency,P=E.lineTangency,_=E.theta,M=c?Math.abs(u-s):Math.abs(u-s)-j-_;if(M<0&&0===o)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(P.x,",").concat(P.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(k.x,",").concat(k.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(l>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(S.x,",").concat(S.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x}({cx:e,cy:n,innerRadius:o,outerRadius:a,cornerRadius:Math.min(v,y/2),forceCornerRadius:u,cornerIsExternal:s,startAngle:l,endAngle:f}):Gf({cx:e,cy:n,innerRadius:o,outerRadius:a,startAngle:l,endAngle:f}),r().createElement("path",Hf({},rt(t,!0),{className:d,d:h,role:"img"}))};function Kf(){}function Yf(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function Jf(t){this._context=t}function Zf(t){this._context=t}function Qf(t){this._context=t}Xf.defaultProps={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Jf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Yf(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Yf(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},Zf.prototype={areaStart:Kf,areaEnd:Kf,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:Yf(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},Qf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Yf(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class tp{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function ep(t){this._context=t}function rp(t){this._context=t}function np(t){return new rp(t)}function ip(t){return t<0?-1:1}function op(t,e,r){var n=t._x1-t._x0,i=e-t._x1,o=(t._y1-t._y0)/(n||i<0&&-0),a=(r-t._y1)/(i||n<0&&-0),c=(o*i+a*n)/(n+i);return(ip(o)+ip(a))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs(c))||0}function ap(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function cp(t,e,r){var n=t._x0,i=t._y0,o=t._x1,a=t._y1,c=(o-n)/3;t._context.bezierCurveTo(n+c,i+c*e,o-c,a-c*r,o,a)}function up(t){this._context=t}function sp(t){this._context=new lp(t)}function lp(t){this._context=t}function fp(t){this._context=t}function pp(t){var e,r,n=t.length-1,i=new Array(n),o=new Array(n),a=new Array(n);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/o[e-1],o[e]-=r,a[e]-=r*a[e-1];for(i[n-1]=a[n-1]/o[n-1],e=n-2;e>=0;--e)i[e]=(a[e]-i[e+1])/o[e];for(o[n-1]=(t[n]+i[n-1])/2,e=0;e<n-1;++e)o[e]=2*t[e+1]-i[e+1];return[i,o]}function hp(t,e){this._context=t,this._t=e}function dp(t){return t[0]}function yp(t){return t[1]}function vp(t,e){var r=Ft(!0),n=null,i=np,o=null,a=Gt(c);function c(c){var u,s,l,f=(c=Rs(c)).length,p=!1;for(null==n&&(o=i(l=a())),u=0;u<=f;++u)!(u<f&&r(s=c[u],u,c))===p&&((p=!p)?o.lineStart():o.lineEnd()),p&&o.point(+t(s,u,c),+e(s,u,c));if(l)return o=null,l+""||null}return t="function"==typeof t?t:void 0===t?dp:Ft(t),e="function"==typeof e?e:void 0===e?yp:Ft(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:Ft(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:Ft(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:Ft(!!t),c):r},c.curve=function(t){return arguments.length?(i=t,null!=n&&(o=i(n)),c):i},c.context=function(t){return arguments.length?(null==t?n=o=null:o=i(n=t),c):n},c}function mp(t,e,r){var n=null,i=Ft(!0),o=null,a=np,c=null,u=Gt(s);function s(s){var l,f,p,h,d,y=(s=Rs(s)).length,v=!1,m=new Array(y),g=new Array(y);for(null==o&&(c=a(d=u())),l=0;l<=y;++l){if(!(l<y&&i(h=s[l],l,s))===v)if(v=!v)f=l,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=l-1;p>=f;--p)c.point(m[p],g[p]);c.lineEnd(),c.areaEnd()}v&&(m[l]=+t(h,l,s),g[l]=+e(h,l,s),c.point(n?+n(h,l,s):m[l],r?+r(h,l,s):g[l]))}if(d)return c=null,d+""||null}function l(){return vp().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?dp:Ft(+t),e="function"==typeof e?e:Ft(void 0===e?0:+e),r="function"==typeof r?r:void 0===r?yp:Ft(+r),s.x=function(e){return arguments.length?(t="function"==typeof e?e:Ft(+e),n=null,s):t},s.x0=function(e){return arguments.length?(t="function"==typeof e?e:Ft(+e),s):t},s.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:Ft(+t),s):n},s.y=function(t){return arguments.length?(e="function"==typeof t?t:Ft(+t),r=null,s):e},s.y0=function(t){return arguments.length?(e="function"==typeof t?t:Ft(+t),s):e},s.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:Ft(+t),s):r},s.lineX0=s.lineY0=function(){return l().x(t).y(e)},s.lineY1=function(){return l().x(t).y(r)},s.lineX1=function(){return l().x(n).y(e)},s.defined=function(t){return arguments.length?(i="function"==typeof t?t:Ft(!!t),s):i},s.curve=function(t){return arguments.length?(a=t,null!=o&&(c=a(o)),s):a},s.context=function(t){return arguments.length?(null==t?o=c=null:c=a(o=t),s):o},s}function gp(t){return gp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gp(t)}function bp(){return bp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},bp.apply(this,arguments)}function xp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function wp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xp(Object(r),!0).forEach((function(e){Op(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xp(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Op(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==gp(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==gp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===gp(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}ep.prototype={areaStart:Kf,areaEnd:Kf,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},rp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},up.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:cp(this,this._t0,ap(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,cp(this,ap(this,r=op(this,t,e)),r);break;default:cp(this,this._t0,r=op(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(sp.prototype=Object.create(up.prototype)).point=function(t,e){up.prototype.point.call(this,e,t)},lp.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,o){this._context.bezierCurveTo(e,t,n,r,o,i)}},fp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pp(t),i=pp(e),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},hp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var Sp={curveBasisClosed:function(t){return new Zf(t)},curveBasisOpen:function(t){return new Qf(t)},curveBasis:function(t){return new Jf(t)},curveBumpX:function(t){return new tp(t,!0)},curveBumpY:function(t){return new tp(t,!1)},curveLinearClosed:function(t){return new ep(t)},curveLinear:np,curveMonotoneX:function(t){return new up(t)},curveMonotoneY:function(t){return new sp(t)},curveNatural:function(t){return new fp(t)},curveStep:function(t){return new hp(t,.5)},curveStepAfter:function(t){return new hp(t,1)},curveStepBefore:function(t){return new hp(t,0)}},Ap=function(t){return t.x===+t.x&&t.y===+t.y},jp=function(t){return t.x},Ep=function(t){return t.y},kp=function(t){var e,r=t.type,n=t.points,i=t.baseLine,o=t.layout,a=t.connectNulls,c=function(t,e){if(l()(t))return t;var r="curve".concat(mt()(t));return"curveMonotone"!==r&&"curveBump"!==r||!e?Sp[r]||np:Sp["".concat(r).concat("vertical"===e?"Y":"X")]}(r,o),u=a?n.filter((function(t){return Ap(t)})):n;if(g()(i)){var s=a?i.filter((function(t){return Ap(t)})):i,f=u.map((function(t,e){return wp(wp({},t),{},{base:s[e]})}));return(e="vertical"===o?mp().y(Ep).x1(jp).x0((function(t){return t.base.x})):mp().x(jp).y1(Ep).y0((function(t){return t.base.y}))).defined(Ap).curve(c),e(f)}return(e="vertical"===o&&E(i)?mp().y(Ep).x1(jp).x0(i):E(i)?mp().x(jp).y1(Ep).y0(i):vp().x(jp).y(Ep)).defined(Ap).curve(c),e(u)},Pp=function(t){var e=t.className,n=t.points,o=t.path,a=t.pathRef;if(!(n&&n.length||o))return null;var c=n&&n.length?kp(t):o;return r().createElement("path",bp({},rt(t),F(t),{className:i()("recharts-curve",e),d:c,ref:a}))};function _p(){return _p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},_p.apply(this,arguments)}function Mp(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Tp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Tp(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tp(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}Pp.defaultProps={type:"linear",points:[],connectNulls:!1};var Cp=function(t,e,r,n,i){var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,s=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&i instanceof Array){for(var l=[0,0,0,0],f=0;f<4;f++)l[f]=i[f]>a?a:i[f];o="M".concat(t,",").concat(e+c*l[0]),l[0]>0&&(o+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+u*l[0],",").concat(e)),o+="L ".concat(t+r-u*l[1],",").concat(e),l[1]>0&&(o+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,",\n        ").concat(t+r,",").concat(e+c*l[1])),o+="L ".concat(t+r,",").concat(e+n-c*l[2]),l[2]>0&&(o+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,",\n        ").concat(t+r-u*l[2],",").concat(e+n)),o+="L ".concat(t+u*l[3],",").concat(e+n),l[3]>0&&(o+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,",\n        ").concat(t,",").concat(e+n-c*l[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var p=Math.min(a,i);o="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t,",").concat(e+n-c*p," Z")}else o="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},Ip=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,i=e.x,o=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(i,i+a),s=Math.max(i,i+a),l=Math.min(o,o+c),f=Math.max(o,o+c);return r>=u&&r<=s&&n>=l&&n<=f}return!1},Np=function(t){var n=(0,e.useRef)(),o=Mp((0,e.useState)(-1),2),a=o[0],c=o[1];(0,e.useLayoutEffect)((function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&c(t)}catch(t){}}),[]);var u=t.x,s=t.y,l=t.width,f=t.height,p=t.radius,h=t.className,d=t.animationEasing,y=t.animationDuration,v=t.animationBegin,m=t.isAnimationActive,g=t.isUpdateAnimationActive;if(u!==+u||s!==+s||l!==+l||f!==+f||0===l||0===f)return null;var b=i()("recharts-rectangle",h);return g?r().createElement(Qr,{canBegin:a>0,from:{width:l,height:f,x:u,y:s},to:{width:l,height:f,x:u,y:s},duration:y,animationEasing:d,isActive:g},(function(e){var i=e.width,o=e.height,c=e.x,u=e.y;return r().createElement(Qr,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:y,isActive:m,easing:d},r().createElement("path",_p({},rt(t,!0),{className:b,d:Cp(c,u,i,o,p),ref:n})))})):r().createElement("path",_p({},rt(t,!0),{className:b,d:Cp(u,s,l,f,p)}))};Np.defaultProps={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"};var Dp=["points","className","baseLinePoints","connectNulls"];function Rp(){return Rp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Rp.apply(this,arguments)}function Lp(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function Bp(t){return function(t){if(Array.isArray(t))return zp(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return zp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zp(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zp(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Fp=function(t){return t&&t.x===+t.x&&t.y===+t.y},Up=function(t,e){var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach((function(t){Fp(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])})),Fp(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e}(t);e&&(r=[r.reduce((function(t,e){return[].concat(Bp(t),Bp(e))}),[])]);var n=r.map((function(t){return t.reduce((function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)}),"")})).join("");return 1===r.length?"".concat(n,"Z"):n},Wp=function(t){var e=t.points,n=t.className,o=t.baseLinePoints,a=t.connectNulls,c=Lp(t,Dp);if(!e||!e.length)return null;var u=i()("recharts-polygon",n);if(o&&o.length){var s=c.stroke&&"none"!==c.stroke,l=function(t,e,r){var n=Up(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(Up(e.reverse(),r).slice(1))}(e,o,a);return r().createElement("g",{className:u},r().createElement("path",Rp({},rt(c,!0),{fill:"Z"===l.slice(-1)?c.fill:"none",stroke:"none",d:l})),s?r().createElement("path",Rp({},rt(c,!0),{fill:"none",d:Up(e,a)})):null,s?r().createElement("path",Rp({},rt(c,!0),{fill:"none",d:Up(o,a)})):null)}var f=Up(e,a);return r().createElement("path",Rp({},rt(c,!0),{fill:"Z"===f.slice(-1)?c.fill:"none",className:u,d:f}))};function $p(){return $p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},$p.apply(this,arguments)}var Vp=function(t){var e=t.cx,n=t.cy,o=t.r,a=t.className,c=i()("recharts-dot",a);return e===+e&&n===+n&&o===+o?r().createElement("circle",$p({},rt(t),F(t),{className:c,cx:e,cy:n,r:o})):null};function Hp(){return Hp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Hp.apply(this,arguments)}var qp=function(t,e,r,n,i,o){return"M".concat(t,",").concat(i,"v").concat(n,"M").concat(o,",").concat(e,"h").concat(r)},Gp=function(t){var e=t.x,n=t.y,o=t.width,a=t.height,c=t.top,u=t.left,s=t.className;return E(e)&&E(n)&&E(o)&&E(a)&&E(c)&&E(u)?r().createElement("path",Hp({},rt(t,!0),{className:i()("recharts-cross",s),d:qp(e,n,o,a,c,u)})):null};function Xp(t){return Xp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xp(t)}function Kp(){return Kp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Kp.apply(this,arguments)}function Yp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Jp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Yp(Object(r),!0).forEach((function(e){Zp(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Yp(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Zp(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Xp(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Xp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Xp(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}Gp.defaultProps={x:0,y:0,top:0,left:0,width:0,height:0};var Qp=function(t,e,r,n){var i="";return n.forEach((function(n,o){var a=lf(e,r,t,n);i+=o?"L ".concat(a.x,",").concat(a.y):"M ".concat(a.x,",").concat(a.y)})),i+="Z"},th=function(t){var e=t.cx,n=t.cy,i=t.innerRadius,o=t.outerRadius,a=t.polarAngles,c=t.radialLines;if(!a||!a.length||!c)return null;var u=Jp({stroke:"#ccc"},rt(t));return r().createElement("g",{className:"recharts-polar-grid-angle"},a.map((function(t,a){var c=lf(e,n,i,t),s=lf(e,n,o,t);return r().createElement("line",Kp({},u,{key:"line-".concat(a),x1:c.x,y1:c.y,x2:s.x,y2:s.y}))})))},eh=function(t){var e=t.cx,n=t.cy,i=t.radius,o=t.index,a=Jp(Jp({stroke:"#ccc"},rt(t)),{},{fill:"none"});return r().createElement("circle",Kp({},a,{className:"recharts-polar-grid-concentric-circle",key:"circle-".concat(o),cx:e,cy:n,r:i}))},rh=function(t){var e=t.radius,n=t.index,i=Jp(Jp({stroke:"#ccc"},rt(t)),{},{fill:"none"});return r().createElement("path",Kp({},i,{className:"recharts-polar-grid-concentric-polygon",key:"path-".concat(n),d:Qp(e,t.cx,t.cy,t.polarAngles)}))},nh=function(t){var e=t.polarRadius,n=t.gridType;return e&&e.length?r().createElement("g",{className:"recharts-polar-grid-concentric"},e.map((function(e,i){var o=i;return"circle"===n?r().createElement(eh,Kp({key:o},t,{radius:e,index:i})):r().createElement(rh,Kp({key:o},t,{radius:e,index:i}))}))):null},ih=function(t){return t.outerRadius<=0?null:r().createElement("g",{className:"recharts-polar-grid"},r().createElement(th,t),r().createElement(nh,t))};ih.displayName="PolarGrid",ih.defaultProps={cx:0,cy:0,innerRadius:0,outerRadius:0,gridType:"polygon",radialLines:!0};var oh=o(2762),ah=o.n(oh),ch=o(4753),uh=o.n(ch),sh=["cx","cy","angle","ticks","axisLine"],lh=["ticks","tick","angle","tickFormatter","stroke"];function fh(t){return fh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fh(t)}function ph(){return ph=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ph.apply(this,arguments)}function hh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function dh(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hh(Object(r),!0).forEach((function(e){Oh(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hh(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function yh(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function vh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function mh(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Sh(n.key),n)}}function gh(t,e){return gh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},gh(t,e)}function bh(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=wh(t);if(e){var i=wh(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return xh(this,r)}}function xh(t,e){if(e&&("object"===fh(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function wh(t){return wh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},wh(t)}function Oh(t,e,r){return(e=Sh(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Sh(t){var e=function(t,e){if("object"!==fh(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==fh(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===fh(e)?e:String(e)}var Ah=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&gh(t,e)}(a,t);var e,n,i,o=bh(a);function a(){return vh(this,a),o.apply(this,arguments)}return e=a,n=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle,i=r.cx,o=r.cy;return lf(i,o,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,i=t.ticks,o=uh()(i,(function(t){return t.coordinate||0}));return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:ah()(i,(function(t){return t.coordinate||0})).coordinate||0,outerRadius:o.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,n=t.cy,i=t.angle,o=t.ticks,a=t.axisLine,c=yh(t,sh),u=o.reduce((function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]}),[1/0,-1/0]),s=lf(e,n,u[0],i),l=lf(e,n,u[1],i),f=dh(dh(dh({},rt(c)),{},{fill:"none"},rt(a)),{},{x1:s.x,y1:s.y,x2:l.x,y2:l.y});return r().createElement("line",ph({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var t=this,e=this.props,n=e.ticks,i=e.tick,o=e.angle,c=e.tickFormatter,u=e.stroke,s=yh(e,lh),l=this.getTickTextAnchor(),f=rt(s),p=rt(i),h=n.map((function(e,n){var s=t.getTickValueCoord(e),h=dh(dh(dh(dh({textAnchor:l,transform:"rotate(".concat(90-o,", ").concat(s.x,", ").concat(s.y,")")},f),{},{stroke:"none",fill:u},p),{},{index:n},s),{},{payload:e});return r().createElement(ht,ph({className:"recharts-polar-radius-axis-tick",key:"tick-".concat(n)},U(t.props,e,n)),a.renderTickItem(i,h,c?c(e.value,n):e.value))}));return r().createElement(ht,{className:"recharts-polar-radius-axis-ticks"},h)}},{key:"render",value:function(){var t=this.props,e=t.ticks,n=t.axisLine,i=t.tick;return e&&e.length?r().createElement(ht,{className:"recharts-polar-radius-axis"},n&&this.renderAxisLine(),i&&this.renderTicks(),Af.renderCallByParent(this.props,this.getViewBox())):null}}],i=[{key:"renderTickItem",value:function(t,e,n){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement(pi,ph({},e,{className:"recharts-polar-radius-axis-tick-value"}),n)}}],n&&mh(e.prototype,n),i&&mh(e,i),Object.defineProperty(e,"prototype",{writable:!1}),a}(e.PureComponent);function jh(t){return jh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jh(t)}function Eh(){return Eh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Eh.apply(this,arguments)}function kh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ph(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?kh(Object(r),!0).forEach((function(e){Dh(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):kh(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function _h(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Mh(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Rh(n.key),n)}}function Th(t,e){return Th=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Th(t,e)}function Ch(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Nh(t);if(e){var i=Nh(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return Ih(this,r)}}function Ih(t,e){if(e&&("object"===jh(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function Nh(t){return Nh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Nh(t)}function Dh(t,e,r){return(e=Rh(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Rh(t){var e=function(t,e){if("object"!==jh(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==jh(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===jh(e)?e:String(e)}Oh(Ah,"displayName","PolarRadiusAxis"),Oh(Ah,"axisType","radiusAxis"),Oh(Ah,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var Lh=Math.PI/180,Bh=1e-5,zh=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Th(t,e)}(a,t);var e,n,i,o=Ch(a);function a(){return _h(this,a),o.apply(this,arguments)}return e=a,n=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,i=e.radius,o=e.orientation,a=e.tickSize||8,c=lf(r,n,i,t.coordinate),u=lf(r,n,i+("inner"===o?-1:1)*a,t.coordinate);return{x1:c.x,y1:c.y,x2:u.x,y2:u.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*Lh);return r>Bh?"outer"===e?"start":"end":r<-Bh?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,n=t.cy,i=t.radius,o=t.axisLine,a=t.axisLineType,c=Ph(Ph({},rt(this.props)),{},{fill:"none"},rt(o));if("circle"===a)return r().createElement(Vp,Eh({className:"recharts-polar-angle-axis-line"},c,{cx:e,cy:n,r:i}));var u=this.props.ticks.map((function(t){return lf(e,n,i,t.coordinate)}));return r().createElement(Wp,Eh({className:"recharts-polar-angle-axis-line"},c,{points:u}))}},{key:"renderTicks",value:function(){var t=this,e=this.props,n=e.ticks,i=e.tick,o=e.tickLine,c=e.tickFormatter,u=e.stroke,s=rt(this.props),l=rt(i),f=Ph(Ph({},s),{},{fill:"none"},rt(o)),p=n.map((function(e,n){var p=t.getTickLineCoord(e),h=Ph(Ph(Ph({textAnchor:t.getTickTextAnchor(e)},s),{},{stroke:"none",fill:u},l),{},{index:n,payload:e,x:p.x2,y:p.y2});return r().createElement(ht,Eh({className:"recharts-polar-angle-axis-tick",key:"tick-".concat(n)},U(t.props,e,n)),o&&r().createElement("line",Eh({className:"recharts-polar-angle-axis-tick-line"},f,p)),i&&a.renderTickItem(i,h,c?c(e.value,n):e.value))}));return r().createElement(ht,{className:"recharts-polar-angle-axis-ticks"},p)}},{key:"render",value:function(){var t=this.props,e=t.ticks,n=t.radius,i=t.axisLine;return n<=0||!e||!e.length?null:r().createElement(ht,{className:"recharts-polar-angle-axis"},i&&this.renderAxisLine(),this.renderTicks())}}],i=[{key:"renderTickItem",value:function(t,e,n){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement(pi,Eh({},e,{className:"recharts-polar-angle-axis-tick-value"}),n)}}],n&&Mh(e.prototype,n),i&&Mh(e,i),Object.defineProperty(e,"prototype",{writable:!1}),a}(e.PureComponent);Dh(zh,"displayName","PolarAngleAxis"),Dh(zh,"axisType","angleAxis"),Dh(zh,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var Fh=o(8630),Uh=o.n(Fh);function Wh(t){return Wh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wh(t)}function $h(){return $h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},$h.apply(this,arguments)}function Vh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Hh(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Vh(Object(r),!0).forEach((function(e){Zh(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Vh(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function qh(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Qh(n.key),n)}}function Gh(t,e){return Gh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Gh(t,e)}function Xh(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Jh(t);if(e){var i=Jh(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return Kh(this,r)}}function Kh(t,e){if(e&&("object"===Wh(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Yh(t)}function Yh(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Jh(t){return Jh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Jh(t)}function Zh(t,e,r){return(e=Qh(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Qh(t){var e=function(t,e){if("object"!==Wh(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Wh(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Wh(e)?e:String(e)}var td=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Gh(t,e)}(c,t);var e,n,o,a=Xh(c);function c(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,c),Zh(Yh(e=a.call(this,t)),"pieRef",null),Zh(Yh(e),"sectorRefs",[]),Zh(Yh(e),"id",_("recharts-pie-")),Zh(Yh(e),"handleAnimationEnd",(function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),l()(t)&&t()})),Zh(Yh(e),"handleAnimationStart",(function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),l()(t)&&t()})),e.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},e}return e=c,o=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement(Pp,$h({},e,{type:"linear",className:"recharts-pie-label-line"}))}},{key:"renderLabelItem",value:function(t,e,n){if(r().isValidElement(t))return r().cloneElement(t,e);var i=n;return l()(t)&&(i=t(e),r().isValidElement(i))?i:r().createElement(pi,$h({},e,{alignmentBaseline:"middle",className:"recharts-pie-label-text"}),i)}},{key:"renderSectorItem",value:function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):Uh()(t)?r().createElement(Xf,$h({tabIndex:-1},e,t)):r().createElement(Xf,$h({tabIndex:-1},e))}}],(n=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var e=this.props,n=e.label,i=e.labelLine,o=e.dataKey,a=e.valueKey,u=rt(this.props),s=rt(n),l=rt(i),f=n&&n.offsetRadius||20,p=t.map((function(t,e){var p=(t.startAngle+t.endAngle)/2,h=lf(t.cx,t.cy,t.outerRadius+f,p),d=Hh(Hh(Hh(Hh({},u),t),{},{stroke:"none"},s),{},{index:e,textAnchor:c.getTextAnchor(h.x,t.cx)},h),y=Hh(Hh(Hh(Hh({},u),t),{},{fill:"none",stroke:t.fill},l),{},{index:e,points:[lf(t.cx,t.cy,t.outerRadius,p),h],key:"line"}),m=o;return v()(o)&&v()(a)?m="value":v()(o)&&(m=a),r().createElement(ht,{key:"label-".concat(e)},i&&c.renderLabelLineItem(i,y),c.renderLabelItem(n,d,kl(t,m)))}));return r().createElement(ht,{className:"recharts-pie-labels"},p)}},{key:"renderSectorsStatically",value:function(t){var e=this,n=this.props,i=n.activeShape,o=n.blendStroke,a=n.inactiveShape;return t.map((function(t,n){var u=a&&e.hasActiveIndex()?a:null,s=e.isActiveIndex(n)?i:u,l=Hh(Hh({},t),{},{stroke:o?t.fill:t.stroke});return r().createElement(ht,$h({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},U(e.props,t,n),{key:"sector-".concat(n)}),c.renderSectorItem(s,l))}))}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,n=e.sectors,i=e.isAnimationActive,o=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,s=this.state,l=s.prevSectors,f=s.prevIsAnimationActive;return r().createElement(Qr,{begin:o,duration:a,isActive:i,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(u,"-").concat(f),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var i=e.t,o=[],a=(n&&n[0]).startAngle;return n.forEach((function(t,e){var r=l&&l[e],n=e>0?d()(t,"paddingAngle",0):0;if(r){var c=C(r.endAngle-r.startAngle,t.endAngle-t.startAngle),u=Hh(Hh({},t),{},{startAngle:a+n,endAngle:a+c(i)+n});o.push(u),a=u.endAngle}else{var s=t.endAngle,f=t.startAngle,p=C(0,s-f)(i),h=Hh(Hh({},t),{},{startAngle:a+n,endAngle:a+p+n});o.push(h),a=h.endAngle}})),r().createElement(ht,null,t.renderSectorsStatically(o))}))}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return!(r&&e&&e.length)||n&&di()(n,e)?this.renderSectorsStatically(e):this.renderSectorsWithAnimation()}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,n=e.hide,o=e.sectors,a=e.className,c=e.label,u=e.cx,s=e.cy,l=e.innerRadius,f=e.outerRadius,p=e.isAnimationActive,h=this.state.isAnimationFinished;if(n||!o||!o.length||!E(u)||!E(s)||!E(l)||!E(f))return null;var d=i()("recharts-pie",a);return r().createElement(ht,{tabIndex:0,className:d,ref:function(e){t.pieRef=e}},this.renderSectors(),c&&this.renderLabels(o),Af.renderCallByParent(this.props,null,!1),(!p||h)&&zf.renderCallByParent(this.props,o,!1))}}])&&qh(e.prototype,n),o&&qh(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.PureComponent);Zh(td,"displayName","Pie"),Zh(td,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!fn.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1}),Zh(td,"parseDeltaAngle",(function(t,e){return A(e-t)*Math.min(Math.abs(e-t),360)})),Zh(td,"getRealPieData",(function(t){var e=t.props,r=e.data,n=e.children,i=rt(t.props),o=Y(n,Un);return r&&r.length?r.map((function(t,e){return Hh(Hh(Hh({payload:t},i),t),o&&o[e]&&o[e].props)})):o&&o.length?o.map((function(t){return Hh(Hh({},i),t.props)})):[]})),Zh(td,"parseCoordinateOfPie",(function(t,e){var r=e.top,n=e.left,i=e.width,o=e.height,a=ff(i,o);return{cx:n+M(t.props.cx,i,i/2),cy:r+M(t.props.cy,o,o/2),innerRadius:M(t.props.innerRadius,a,0),outerRadius:M(t.props.outerRadius,a,.8*a),maxRadius:t.props.maxRadius||Math.sqrt(i*i+o*o)/2}})),Zh(td,"getComposedData",(function(t){var e=t.item,r=t.offset,n=td.getRealPieData(e);if(!n||!n.length)return null;var i=e.props,o=i.cornerRadius,a=i.startAngle,c=i.endAngle,u=i.paddingAngle,s=i.dataKey,l=i.nameKey,f=i.valueKey,p=i.tooltipType,h=Math.abs(e.props.minAngle),d=td.parseCoordinateOfPie(e,r),y=td.parseDeltaAngle(a,c),m=Math.abs(y),g=s;v()(s)&&v()(f)?(Rn(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g="value"):v()(s)&&(Rn(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g=f);var b,x,w=n.filter((function(t){return 0!==kl(t,g,0)})).length,O=m-w*h-(m>=360?w:w-1)*u,S=n.reduce((function(t,e){var r=kl(e,g,0);return t+(E(r)?r:0)}),0);S>0&&(b=n.map((function(t,e){var r,n=kl(t,g,0),i=kl(t,l,e),c=(E(n)?n:0)/S,s=(r=e?x.endAngle+A(y)*u*(0!==n?1:0):a)+A(y)*((0!==n?h:0)+c*O),f=(r+s)/2,v=(d.innerRadius+d.outerRadius)/2,m=[{name:i,value:n,payload:t,dataKey:g,type:p}],b=lf(d.cx,d.cy,v,f);return x=Hh(Hh(Hh({percent:c,cornerRadius:o,name:i,tooltipPayload:m,midAngle:f,middleRadius:v,tooltipPosition:b},t),d),{},{value:kl(t,g),startAngle:r,endAngle:s,payload:t,paddingAngle:A(y)*u})})));return Hh(Hh({},d),{},{sectors:b,data:n})}));var ed=o(8804),rd=o.n(ed);function nd(t){return nd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},nd(t)}function id(){return id=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},id.apply(this,arguments)}function od(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ad(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?od(Object(r),!0).forEach((function(e){dd(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):od(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function cd(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ud(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yd(n.key),n)}}function sd(t,e){return sd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},sd(t,e)}function ld(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=hd(t);if(e){var i=hd(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fd(this,r)}}function fd(t,e){if(e&&("object"===nd(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return pd(t)}function pd(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function hd(t){return hd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},hd(t)}function dd(t,e,r){return(e=yd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yd(t){var e=function(t,e){if("object"!==nd(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==nd(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===nd(e)?e:String(e)}var vd=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&sd(t,e)}(c,t);var e,n,o,a=ld(c);function c(){var t;cd(this,c);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return dd(pd(t=a.call.apply(a,[this].concat(r))),"state",{isAnimationFinished:!1}),dd(pd(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),l()(e)&&e()})),dd(pd(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),l()(e)&&e()})),dd(pd(t),"handleMouseEnter",(function(e){var r=t.props.onMouseEnter;r&&r(t.props,e)})),dd(pd(t),"handleMouseLeave",(function(e){var r=t.props.onMouseLeave;r&&r(t.props,e)})),t}return e=c,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"renderDotItem",value:function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement(Vp,id({},e,{className:"recharts-radar-dot"}))}}],(n=[{key:"renderDots",value:function(t){var e=this.props,n=e.dot,i=e.dataKey,o=rt(this.props),a=rt(n),u=t.map((function(t,e){var r=ad(ad(ad({key:"dot-".concat(e),r:3},o),a),{},{dataKey:i,cx:t.x,cy:t.y,index:e,payload:t});return c.renderDotItem(n,r)}));return r().createElement(ht,{className:"recharts-radar-dots"},u)}},{key:"renderPolygonStatically",value:function(t){var e,n=this.props,i=n.shape,o=n.dot,a=n.isRange,c=n.baseLinePoints,u=n.connectNulls;return e=r().isValidElement(i)?r().cloneElement(i,ad(ad({},this.props),{},{points:t})):l()(i)?i(ad(ad({},this.props),{},{points:t})):r().createElement(Wp,id({},rt(this.props,!0),{onMouseEnter:this.handleMouseEnter,onMouseLeave:this.handleMouseLeave,points:t,baseLinePoints:a?c:null,connectNulls:u})),r().createElement(ht,{className:"recharts-radar-polygon"},e,o?this.renderDots(t):null)}},{key:"renderPolygonWithAnimation",value:function(){var t=this,e=this.props,n=e.points,i=e.isAnimationActive,o=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,s=this.state.prevPoints;return r().createElement(Qr,{begin:o,duration:a,isActive:i,easing:c,from:{t:0},to:{t:1},key:"radar-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var r=e.t,i=s&&s.length/n.length,o=n.map((function(t,e){var n=s&&s[Math.floor(e*i)];if(n){var o=C(n.x,t.x),a=C(n.y,t.y);return ad(ad({},t),{},{x:o(r),y:a(r)})}var c=C(t.cx,t.x),u=C(t.cy,t.y);return ad(ad({},t),{},{x:c(r),y:u(r)})}));return t.renderPolygonStatically(o)}))}},{key:"renderPolygon",value:function(){var t=this.props,e=t.points,r=t.isAnimationActive,n=t.isRange,i=this.state.prevPoints;return!(r&&e&&e.length)||n||i&&di()(i,e)?this.renderPolygonStatically(e):this.renderPolygonWithAnimation()}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.className,o=t.points,a=t.isAnimationActive;if(e||!o||!o.length)return null;var c=this.state.isAnimationFinished,u=i()("recharts-radar",n);return r().createElement(ht,{className:u},this.renderPolygon(),(!a||c)&&zf.renderCallByParent(this.props,o))}}])&&ud(e.prototype,n),o&&ud(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.PureComponent);dd(vd,"displayName","Radar"),dd(vd,"defaultProps",{angleAxisId:0,radiusAxisId:0,hide:!1,activeDot:!0,dot:!1,legendType:"rect",isAnimationActive:!fn.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),dd(vd,"getComposedData",(function(t){var e=t.radiusAxis,r=t.angleAxis,n=t.displayedData,i=t.dataKey,o=t.bandSize,a=r.cx,c=r.cy,u=!1,s=[];n.forEach((function(t,n){var l=kl(t,r.dataKey,n),f=kl(t,i),p=r.scale(l)+(o||0),h=g()(f)?Pf()(f):f,d=v()(h)?void 0:e.scale(h);g()(f)&&f.length>=2&&(u=!0),s.push(ad(ad({},lf(a,c,d,p)),{},{name:l,value:f,cx:a,cy:c,radius:d,angle:p,payload:t}))}));var l=[];return u&&s.forEach((function(t){if(g()(t.value)){var r=rd()(t.value),n=v()(r)?void 0:e.scale(r);l.push(ad(ad({},t),{},{radius:n},lf(a,c,n,t.angle)))}else l.push(t)})),{points:s,isRange:u,baseLinePoints:l}}));var md=["shape","activeShape","activeIndex","cornerRadius"],gd=["value","background"];function bd(t){return bd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bd(t)}function xd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function wd(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xd(Object(r),!0).forEach((function(e){Md(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xd(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Od(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function Sd(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ad(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Td(n.key),n)}}function jd(t,e){return jd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},jd(t,e)}function Ed(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=_d(t);if(e){var i=_d(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return kd(this,r)}}function kd(t,e){if(e&&("object"===bd(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Pd(t)}function Pd(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function _d(t){return _d=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},_d(t)}function Md(t,e,r){return(e=Td(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Td(t){var e=function(t,e){if("object"!==bd(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==bd(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===bd(e)?e:String(e)}var Cd=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&jd(t,e)}(c,t);var e,n,o,a=Ed(c);function c(){var t;Sd(this,c);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return Md(Pd(t=a.call.apply(a,[this].concat(r))),"state",{isAnimationFinished:!1}),Md(Pd(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),l()(e)&&e()})),Md(Pd(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),l()(e)&&e()})),t}return e=c,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}},{key:"renderSectorShape",value:function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement(Xf,e)}}],(n=[{key:"getDeltaAngle",value:function(){var t=this.props,e=t.startAngle,r=t.endAngle;return A(r-e)*Math.min(Math.abs(r-e),360)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,n=r.shape,i=r.activeShape,o=r.activeIndex,a=r.cornerRadius,u=Od(r,md),s=rt(u);return t.map((function(t,r){var l=wd(wd(wd(wd({},s),{},{cornerRadius:a},t),U(e.props,t,r)),{},{key:"sector-".concat(r),className:"recharts-radial-bar-sector",forceCornerRadius:u.forceCornerRadius,cornerIsExternal:u.cornerIsExternal});return c.renderSectorShape(r===o?i:n,l)}))}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,n=e.data,i=e.isAnimationActive,o=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,s=this.state.prevData;return r().createElement(Qr,{begin:o,duration:a,isActive:i,easing:c,from:{t:0},to:{t:1},key:"radialBar-".concat(u),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var i=e.t,o=n.map((function(t,e){var r=s&&s[e];if(r){var n=C(r.startAngle,t.startAngle),o=C(r.endAngle,t.endAngle);return wd(wd({},t),{},{startAngle:n(i),endAngle:o(i)})}var a=t.endAngle,c=t.startAngle,u=C(c,a);return wd(wd({},t),{},{endAngle:u(i)})}));return r().createElement(ht,null,t.renderSectorsStatically(o))}))}},{key:"renderSectors",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&di()(n,e)?this.renderSectorsStatically(e):this.renderSectorsWithAnimation()}},{key:"renderBackground",value:function(t){var e=this,r=this.props.cornerRadius,n=rt(this.props.background);return t.map((function(t,i){t.value;var o=t.background,a=Od(t,gd);if(!o)return null;var u=wd(wd(wd(wd(wd({cornerRadius:r},a),{},{fill:"#eee"},o),n),U(e.props,t,i)),{},{index:i,key:"sector-".concat(i),className:"recharts-radial-bar-background-sector"});return c.renderSectorShape(o,u)}))}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.data,o=t.className,a=t.background,c=t.isAnimationActive;if(e||!n||!n.length)return null;var u=this.state.isAnimationFinished,s=i()("recharts-area",o);return r().createElement(ht,{className:s},a&&r().createElement(ht,{className:"recharts-radial-bar-background"},this.renderBackground(n)),r().createElement(ht,{className:"recharts-radial-bar-sectors"},this.renderSectors()),(!c||u)&&zf.renderCallByParent(wd({},this.props),n))}}])&&Ad(e.prototype,n),o&&Ad(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.PureComponent);Md(Cd,"displayName","RadialBar"),Md(Cd,"defaultProps",{angleAxisId:0,radiusAxisId:0,minPointSize:0,hide:!1,legendType:"rect",data:[],isAnimationActive:!fn.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease",forceCornerRadius:!1,cornerIsExternal:!1}),Md(Cd,"getComposedData",(function(t){var e=t.item,r=t.props,n=t.radiusAxis,i=t.radiusAxisTicks,o=t.angleAxis,a=t.angleAxisTicks,c=t.displayedData,u=t.dataKey,s=t.stackedData,l=t.barPosition,f=t.bandSize,p=t.dataStartIndex,h=Fl(l,e);if(!h)return null;var d=o.cx,y=o.cy,v=r.layout,m=e.props,b=m.children,x=m.minPointSize,w="radial"===v?o:n,O=s?w.scale.domain():null,S=Gl({numericAxis:w}),j=Y(b,Un),E=c.map((function(t,c){var l,m,b,w,E,k;if(s?l=Ul(s[p+c],O):(l=kl(t,u),g()(l)||(l=[S,l])),"radial"===v){m=ql({axis:n,ticks:i,bandSize:f,offset:h.offset,entry:t,index:c}),E=o.scale(l[1]),w=o.scale(l[0]),b=m+h.size;var P=E-w;if(Math.abs(x)>0&&Math.abs(P)<Math.abs(x))E+=A(P||x)*(Math.abs(x)-Math.abs(P));k={background:{cx:d,cy:y,innerRadius:m,outerRadius:b,startAngle:r.startAngle,endAngle:r.endAngle}}}else{m=n.scale(l[0]),b=n.scale(l[1]),E=(w=ql({axis:o,ticks:a,bandSize:f,offset:h.offset,entry:t,index:c}))+h.size;var _=b-m;if(Math.abs(x)>0&&Math.abs(_)<Math.abs(x))b+=A(_||x)*(Math.abs(x)-Math.abs(_))}return wd(wd(wd(wd({},t),k),{},{payload:t,value:s?l:l[1],cx:d,cy:y,innerRadius:m,outerRadius:b,startAngle:w,endAngle:E},j&&j[c]&&j[c].props),{},{tooltipPayload:[tf(e,t)],tooltipPosition:lf(d,y,(m+b)/2,(w+E)/2)})}));return{data:E,layout:v}}));var Id=o(6026),Nd=o.n(Id);function Dd(t){return Dd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dd(t)}function Rd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ld(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Rd(Object(r),!0).forEach((function(e){Bd(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Rd(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Bd(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Dd(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Dd(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Dd(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var zd=["Webkit","Moz","O","ms"];function Fd(t){return Fd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fd(t)}function Ud(){return Ud=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ud.apply(this,arguments)}function Wd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function $d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Wd(Object(r),!0).forEach((function(e){Yd(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Wd(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Vd(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Jd(n.key),n)}}function Hd(t,e){return Hd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Hd(t,e)}function qd(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Kd(t);if(e){var i=Kd(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return Gd(this,r)}}function Gd(t,e){if(e&&("object"===Fd(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Xd(t)}function Xd(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Kd(t){return Kd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Kd(t)}function Yd(t,e,r){return(e=Jd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Jd(t){var e=function(t,e){if("object"!==Fd(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Fd(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Fd(e)?e:String(e)}var Zd=function(t){return t.changedTouches&&!!t.changedTouches.length},Qd=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Hd(t,e)}(u,t);var n,o,a,c=qd(u);function u(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),Yd(Xd(e=c.call(this,t)),"handleDrag",(function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)})),Yd(Xd(e),"handleTouchMove",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])})),Yd(Xd(e),"handleDragEnd",(function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1}),e.detachDragEndListener()})),Yd(Xd(e),"handleLeaveWrapper",(function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))})),Yd(Xd(e),"handleEnterSlideOrTraveller",(function(){e.setState({isTextActive:!0})})),Yd(Xd(e),"handleLeaveSlideOrTraveller",(function(){e.setState({isTextActive:!1})})),Yd(Xd(e),"handleSlideDragStart",(function(t){var r=Zd(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()})),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(Xd(e),"startX"),endX:e.handleTravellerDragStart.bind(Xd(e),"endX")},e.state={},e}return n=u,o=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,n=this.state.scaleValues,i=this.props,o=i.gap,a=i.data.length-1,c=Math.min(e,r),s=Math.max(e,r),l=u.getIndexInRange(n,c),f=u.getIndexInRange(n,s);return{startIndex:l-l%o,endIndex:f===a?a:f-f%o}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,i=e.dataKey,o=kl(r[t],i,t);return l()(n)?n(o,t):o}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,i=e.endX,o=this.props,a=o.x,c=o.width,u=o.travellerWidth,s=o.startIndex,l=o.endIndex,f=o.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-i,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-i));var h=this.getIndex({startX:n+p,endX:i+p});h.startIndex===s&&h.endIndex===l||!f||f(h),this.setState({startX:n+p,endX:i+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=Zd(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e,r=this.state,n=r.brushMoveStartX,i=r.movingTravellerId,o=r.endX,a=r.startX,c=this.state[i],u=this.props,s=u.x,l=u.width,f=u.travellerWidth,p=u.onChange,h=u.gap,d=u.data,y={startX:this.state.startX,endX:this.state.endX},v=t.pageX-n;v>0?v=Math.min(v,s+l-f-c):v<0&&(v=Math.max(v,s-c)),y[i]=c+v;var m=this.getIndex(y),g=m.startIndex,b=m.endIndex;this.setState((Yd(e={},i,c+v),Yd(e,"brushMoveStartX",t.pageX),e),(function(){var t;p&&(t=d.length-1,("startX"===i&&(o>a?g%h==0:b%h==0)||o<a&&b===t||"endX"===i&&(o>a?b%h==0:g%h==0)||o>a&&b===t)&&p(m))}))}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,n=t.y,i=t.width,o=t.height,a=t.fill,c=t.stroke;return r().createElement("rect",{stroke:c,fill:a,x:e,y:n,width:i,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,n=t.x,i=t.y,o=t.width,a=t.height,c=t.data,u=t.children,s=t.padding,l=e.Children.only(u);return l?r().cloneElement(l,{x:n,y:i,width:o,height:a,margin:s,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var n=this.props,i=n.y,o=n.travellerWidth,a=n.height,c=n.traveller,s=Math.max(t,this.props.x),l=$d($d({},rt(this.props)),{},{x:s,y:i,width:o,height:a});return r().createElement(ht,{className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],style:{cursor:"col-resize"}},u.renderTraveller(c,l))}},{key:"renderSlide",value:function(t,e){var n=this.props,i=n.y,o=n.height,a=n.stroke,c=n.travellerWidth,u=Math.min(t,e)+c,s=Math.max(Math.abs(e-t)-c,0);return r().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:u,y:i,width:s,height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,n=t.endIndex,i=t.y,o=t.height,a=t.travellerWidth,c=t.stroke,u=this.state,s=u.startX,l=u.endX,f={pointerEvents:"none",fill:c};return r().createElement(ht,{className:"recharts-brush-texts"},r().createElement(pi,Ud({textAnchor:"end",verticalAnchor:"middle",x:Math.min(s,l)-5,y:i+o/2},f),this.getTextOfTick(e)),r().createElement(pi,Ud({textAnchor:"start",verticalAnchor:"middle",x:Math.max(s,l)+a+5,y:i+o/2},f),this.getTextOfTick(n)))}},{key:"render",value:function(){var t=this.props,e=t.data,n=t.className,o=t.children,a=t.x,c=t.y,u=t.width,s=t.height,l=t.alwaysShowText,f=this.state,p=f.startX,h=f.endX,d=f.isTextActive,y=f.isSlideMoving,v=f.isTravellerMoving;if(!e||!e.length||!E(a)||!E(c)||!E(u)||!E(s)||u<=0||s<=0)return null;var m=i()("recharts-brush",n),g=1===r().Children.count(o),b=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,(function(t){return t.toUpperCase()})),n=zd.reduce((function(t,n){return Ld(Ld({},t),{},Bd({},n+r,e))}),{});return n[t]=e,n}("userSelect","none");return r().createElement(ht,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:b},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(p,h),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(h,"endX"),(d||y||v||l)&&this.renderText())}}],a=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,n=t.y,i=t.width,o=t.height,a=t.stroke,c=Math.floor(n+o/2)-1;return r().createElement(r().Fragment,null,r().createElement("rect",{x:e,y:n,width:i,height:o,fill:a,stroke:"none"}),r().createElement("line",{x1:e+1,y1:c,x2:e+i-1,y2:c,fill:"none",stroke:"#fff"}),r().createElement("line",{x1:e+1,y1:c+2,x2:e+i-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):u.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.x,o=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return $d({prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n},r&&r.length?function(t){var e=t.data,r=t.startIndex,n=t.endIndex,i=t.x,o=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=Ni().domain(Nd()(0,c)).range([i,i+o-a]),s=u.domain().map((function(t){return u(t)}));return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,startX:u(r),endX:u(n),scale:u,scaleValues:s}}({data:r,width:n,x:i,travellerWidth:o,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||i!==e.prevX||o!==e.prevTravellerWidth)){e.scale.range([i,i+n-o]);var s=e.scale.domain().map((function(t){return e.scale(t)}));return{prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:s}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=0,n=t.length-1;n-r>1;){var i=Math.floor((r+n)/2);t[i]>e?n=i:r=i}return e>=t[n]?n:r}}],o&&Vd(n.prototype,o),a&&Vd(n,a),Object.defineProperty(n,"prototype",{writable:!1}),u}(e.PureComponent);Yd(Qd,"displayName","Brush"),Yd(Qd,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var ty=o(9704),ey=o.n(ty),ry=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},ny=o(711),iy=o.n(ny),oy=o(6604),ay=o.n(oy),cy=["value","background"];function uy(t){return uy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},uy(t)}function sy(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function ly(){return ly=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ly.apply(this,arguments)}function fy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function py(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fy(Object(r),!0).forEach((function(e){xy(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fy(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function hy(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function dy(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,wy(n.key),n)}}function yy(t,e){return yy=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},yy(t,e)}function vy(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=by(t);if(e){var i=by(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return my(this,r)}}function my(t,e){if(e&&("object"===uy(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return gy(t)}function gy(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function by(t){return by=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},by(t)}function xy(t,e,r){return(e=wy(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function wy(t){var e=function(t,e){if("object"!==uy(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==uy(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===uy(e)?e:String(e)}var Oy=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&yy(t,e)}(c,t);var e,n,o,a=vy(c);function c(){var t;hy(this,c);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return xy(gy(t=a.call.apply(a,[this].concat(r))),"state",{isAnimationFinished:!1}),xy(gy(t),"id",_("recharts-bar-")),xy(gy(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()})),xy(gy(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()})),t}return e=c,n=[{key:"renderRectanglesStatically",value:function(t){var e=this,n=this.props.shape,i=rt(this.props);return t&&t.map((function(t,o){var a=py(py(py({},i),t),{},{index:o});return r().createElement(ht,ly({className:"recharts-bar-rectangle"},U(e.props,t,o),{key:"rectangle-".concat(o)}),c.renderRectangle(n,a))}))}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,n=e.data,i=e.layout,o=e.isAnimationActive,a=e.animationBegin,c=e.animationDuration,u=e.animationEasing,s=e.animationId,l=this.state.prevData;return r().createElement(Qr,{begin:a,duration:c,isActive:o,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var o=e.t,a=n.map((function(t,e){var r=l&&l[e];if(r){var n=C(r.x,t.x),a=C(r.y,t.y),c=C(r.width,t.width),u=C(r.height,t.height);return py(py({},t),{},{x:n(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===i){var s=C(0,t.height)(o);return py(py({},t),{},{y:t.y+t.height-s,height:s})}var f=C(0,t.width)(o);return py(py({},t),{},{width:f})}));return r().createElement(ht,null,t.renderRectanglesStatically(a))}))}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&di()(n,e)?this.renderRectanglesStatically(e):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var t=this,e=this.props.data,r=rt(this.props.background);return e.map((function(e,n){e.value;var i=e.background,o=sy(e,cy);if(!i)return null;var a=py(py(py(py(py({},o),{},{fill:"#eee"},i),r),U(t.props,e,n)),{},{index:n,key:"background-bar-".concat(n),className:"recharts-bar-background-rectangle"});return c.renderRectangle(t.props.background,a)}))}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,i=n.data,o=n.xAxis,a=n.yAxis,c=n.layout,u=Y(n.children,xl);if(!u)return null;var s="vertical"===c?i[0].height/2:i[0].width/2,l=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:kl(t,e)}},f={clipPath:t?"url(#clipPath-".concat(e,")"):null};return r().createElement(ht,f,u.map((function(t,e){return r().cloneElement(t,{key:"error-bar-".concat(e),data:i,xAxis:o,yAxis:a,layout:c,offset:s,dataPointFormatter:l})})))}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.data,o=t.className,a=t.xAxis,c=t.yAxis,u=t.left,s=t.top,l=t.width,f=t.height,p=t.isAnimationActive,h=t.background,d=t.id;if(e||!n||!n.length)return null;var y=this.state.isAnimationFinished,m=i()("recharts-bar",o),g=a&&a.allowDataOverflow,b=c&&c.allowDataOverflow,x=g||b,w=v()(d)?this.id:d;return r().createElement(ht,{className:m},g||b?r().createElement("defs",null,r().createElement("clipPath",{id:"clipPath-".concat(w)},r().createElement("rect",{x:g?u:u-l/2,y:b?s:s-f/2,width:g?l:2*l,height:b?f:2*f}))):null,r().createElement(ht,{className:"recharts-bar-rectangles",clipPath:x?"url(#clipPath-".concat(w,")"):null},h?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(x,w),(!p||y)&&zf.renderCallByParent(this.props,n))}}],o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}},{key:"renderRectangle",value:function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement(Np,e)}}],n&&dy(e.prototype,n),o&&dy(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.PureComponent);function Sy(t){return Sy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sy(t)}function Ay(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Py(n.key),n)}}function jy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ey(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?jy(Object(r),!0).forEach((function(e){ky(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):jy(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ky(t,e,r){return(e=Py(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Py(t){var e=function(t,e){if("object"!==Sy(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Sy(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Sy(e)?e:String(e)}xy(Oy,"displayName","Bar"),xy(Oy,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",isAnimationActive:!fn.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),xy(Oy,"getComposedData",(function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,o=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,s=t.stackedData,l=t.dataStartIndex,f=t.displayedData,p=t.offset,h=Fl(n,r);if(!h)return null;var d=e.layout,y=r.props,v=y.dataKey,m=y.children,b=y.minPointSize,x="horizontal"===d?a:o,w=s?x.scale.domain():null,O=Gl({numericAxis:x}),S=Y(m,Un),j=f.map((function(t,e){var n,f,p,y,m,x;if(s?n=Ul(s[l+e],w):(n=kl(t,v),g()(n)||(n=[O,n])),"horizontal"===d){var j,E=[a.scale(n[0]),a.scale(n[1])],k=E[0],P=E[1];f=ql({axis:o,ticks:c,bandSize:i,offset:h.offset,entry:t,index:e}),p=null!==(j=null!=P?P:k)&&void 0!==j?j:void 0,y=h.size;var _=k-P;if(m=Number.isNaN(_)?0:_,x={x:f,y:a.y,width:y,height:a.height},Math.abs(b)>0&&Math.abs(m)<Math.abs(b)){var M=A(m||b)*(Math.abs(b)-Math.abs(m));p-=M,m+=M}}else{var T=[o.scale(n[0]),o.scale(n[1])],C=T[0],I=T[1];if(f=C,p=ql({axis:a,ticks:u,bandSize:i,offset:h.offset,entry:t,index:e}),y=I-C,m=h.size,x={x:o.x,y:p,width:o.width,height:m},Math.abs(b)>0&&Math.abs(y)<Math.abs(b))y+=A(y||b)*(Math.abs(b)-Math.abs(y))}return py(py(py({},t),{},{x:f,y:p,width:y,height:m,value:s?n:n[1],payload:t,background:x},S&&S[e]&&S[e].props),{},{tooltipPayload:[tf(r,t)],tooltipPosition:{x:f+y/2,y:p+m/2}})}));return py({data:j,layout:d},p)}));var _y=function(t,e,r,n,i){var o=t.width,a=t.height,c=t.layout,u=t.children,s=Object.keys(e),l={left:r.left,leftMirror:r.left,right:o-r.right,rightMirror:o-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!J(u,Oy);return s.reduce((function(o,a){var u,s,p,h,d,y=e[a],v=y.orientation,m=y.domain,g=y.padding,b=void 0===g?{}:g,x=y.mirror,w=y.reversed,O="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var S=m[1]-m[0],A=1/0,j=y.categoricalDomain.sort();j.forEach((function(t,e){e>0&&(A=Math.min((t||0)-(j[e-1]||0),A))}));var E=A/S,k="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=E*k/2),"no-gap"===y.padding){var P=M(t.barCategoryGap,E*k),_=E*k/2;u=_-P-(_-P)/k*P}}s="xAxis"===n?[r.left+(b.left||0)+(u||0),r.left+r.width-(b.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(b.bottom||0),r.top+(b.top||0)]:[r.top+(b.top||0)+(u||0),r.top+r.height-(b.bottom||0)-(u||0)]:y.range,w&&(s=[s[1],s[0]]);var T=Ll(y,i,f),C=T.scale,I=T.realScaleType;C.domain(m).range(s),zl(C);var N=Vl(C,Ey(Ey({},y),{},{realScaleType:I}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=l[O]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=l[O]-d*y.width,h=r.top);var D=Ey(Ey(Ey({},y),N),{},{realScaleType:I,x:p,y:h,scale:C,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return D.bandSize=Zl(D,N),y.hide||"xAxis"!==n?y.hide||(l[O]+=(d?-1:1)*D.width):l[O]+=(d?-1:1)*D.height,Ey(Ey({},o),{},ky({},a,D))}),{})},My=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return{x:Math.min(r,i),y:Math.min(n,o),width:Math.abs(i-r),height:Math.abs(o-n)}},Ty=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scale=e}var e,r,n;return e=t,r=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(t)+o}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],n=[{key:"create",value:function(e){return new t(e)}}],r&&Ay(e.prototype,r),n&&Ay(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();ky(Ty,"EPS",1e-4);var Cy=function(t){var e=Object.keys(t).reduce((function(e,r){return Ey(Ey({},e),{},ky({},r,Ty.create(t[r])))}),{});return Ey(Ey({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return ay()(t,(function(t,r){return e[r].apply(t,{bandAware:n,position:i})}))},isInRange:function(t){return iy()(t,(function(t,r){return e[r].isInRange(t)}))}})};function Iy(t){return(t%180+180)%180}function Ny(t){return Ny="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ny(t)}function Dy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ry(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Dy(Object(r),!0).forEach((function(e){Ly(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Dy(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ly(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Ny(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Ny(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Ny(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function By(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return zy(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zy(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zy(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Fy(){return Fy=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Fy.apply(this,arguments)}function Uy(t){var e=t.x,n=t.y,o=t.segment,a=t.xAxis,c=t.yAxis,u=t.shape,s=t.className,f=t.alwaysShow,p=t.clipPathId;Rn(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var h=function(t,e,r,n,i){var o=i.viewBox,a=o.x,c=o.y,u=o.width,s=o.height,l=i.position;if(r){var f=i.y,p=i.yAxis.orientation,h=t.y.apply(f,{position:l});if(ry(i,"discard")&&!t.y.isInRange(h))return null;var d=[{x:a+u,y:h},{x:a,y:h}];return"left"===p?d.reverse():d}if(e){var y=i.x,v=i.xAxis.orientation,m=t.x.apply(y,{position:l});if(ry(i,"discard")&&!t.x.isInRange(m))return null;var g=[{x:m,y:c+s},{x:m,y:c}];return"top"===v?g.reverse():g}if(n){var b=i.segment.map((function(e){return t.apply(e,{position:l})}));return ry(i,"discard")&&ey()(b,(function(e){return!t.isInRange(e)}))?null:b}return null}(Cy({x:a.scale,y:c.scale}),k(e),k(n),o&&2===o.length,t);if(!h)return null;var d=By(h,2),y=d[0],v=y.x,m=y.y,g=d[1],b=g.x,x=g.y,w=Ry(Ry({clipPath:ry(t,"hidden")?"url(#".concat(p,")"):void 0},rt(t,!0)),{},{x1:v,y1:m,x2:b,y2:x});return r().createElement(ht,{className:i()("recharts-reference-line",s)},function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement("line",Fy({},e,{className:"recharts-reference-line-line"}))}(u,w),Af.renderCallByParent(t,function(t){var e=t.x1,r=t.y1,n=t.x2,i=t.y2;return My({x:e,y:r},{x:n,y:i})}({x1:v,y1:m,x2:b,y2:x})))}function Wy(t){return Wy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wy(t)}function $y(){return $y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},$y.apply(this,arguments)}function Vy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Hy(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Vy(Object(r),!0).forEach((function(e){qy(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Vy(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function qy(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Wy(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Wy(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Wy(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}Uy.displayName="ReferenceLine",Uy.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"};function Gy(t){var e=t.x,n=t.y,o=t.r,a=t.alwaysShow,c=t.clipPathId,u=k(e),s=k(n);if(Rn(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!s)return null;var l=function(t){var e=t.x,r=t.y,n=t.xAxis,i=t.yAxis,o=Cy({x:n.scale,y:i.scale}),a=o.apply({x:e,y:r},{bandAware:!0});return ry(t,"discard")&&!o.isInRange(a)?null:a}(t);if(!l)return null;var f=l.x,p=l.y,h=t.shape,d=t.className,y=Hy(Hy({clipPath:ry(t,"hidden")?"url(#".concat(c,")"):void 0},rt(t,!0)),{},{cx:f,cy:p});return r().createElement(ht,{className:i()("recharts-reference-dot",d)},Gy.renderDot(h,y),Af.renderCallByParent(t,{x:f-o,y:p-o,width:2*o,height:2*o}))}function Xy(t){return Xy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xy(t)}function Ky(){return Ky=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ky.apply(this,arguments)}function Yy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Jy(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Yy(Object(r),!0).forEach((function(e){Zy(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Yy(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Zy(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Xy(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Xy(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Xy(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}Gy.displayName="ReferenceDot",Gy.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1},Gy.renderDot=function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement(Vp,$y({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))};function Qy(t){var e=t.x1,n=t.x2,o=t.y1,a=t.y2,c=t.className,u=t.alwaysShow,s=t.clipPathId;Rn(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=k(e),f=k(n),p=k(o),h=k(a),d=t.shape;if(!(l||f||p||h||d))return null;var y=function(t,e,r,n,i){var o=i.x1,a=i.x2,c=i.y1,u=i.y2,s=i.xAxis,l=i.yAxis;if(!s||!l)return null;var f=Cy({x:s.scale,y:l.scale}),p={x:t?f.x.apply(o,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!ry(i,"discard")||f.isInRange(p)&&f.isInRange(h)?My(p,h):null}(l,f,p,h,t);if(!y&&!d)return null;var v=ry(t,"hidden")?"url(#".concat(s,")"):void 0;return r().createElement(ht,{className:i()("recharts-reference-area",c)},Qy.renderRect(d,Jy(Jy({clipPath:v},rt(t,!0)),y)),Af.renderCallByParent(t,y))}function tv(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e){if(void 0!==r&&!0!==r(t[i]))return;n.push(t[i])}return n}function ev(t){return ev="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ev(t)}function rv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function nv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rv(Object(r),!0).forEach((function(e){iv(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rv(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function iv(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==ev(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ev(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===ev(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ov(t,e,r){return function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=Iy(n),o=i*Math.PI/180,a=Math.atan(r/e),c=o>a&&o<Math.PI-a?r/Math.sin(o):e/Math.cos(o);return Math.abs(c)}({width:t.width+e.width,height:t.height+e.height},r)}function av(t,e){var r,n,i=t.angle,o=t.ticks,a=t.tickFormatter,c=t.viewBox,u=t.orientation,s=t.minTickGap,f=t.unit,p=t.fontSize,h=t.letterSpacing,d=c.x,y=c.y,v=c.width,m=c.height,g="top"===u||"bottom"===u?"width":"height",b=(o||[]).slice(),x=f&&"width"===g?ei(f,{fontSize:p,letterSpacing:h}):{width:0,height:0},w=b.length,O=w>=2?A(b[1].coordinate-b[0].coordinate):1;if(1===O?(r="width"===g?d:y,n="width"===g?d+v:y+m):(r="width"===g?d+v:y+m,n="width"===g?d:y),e){var S=o[w-1],j=l()(a)?a(S.value,w-1):S.value,E="width"===g?ov(ei(j,{fontSize:p,letterSpacing:h}),x,i):ei(j,{fontSize:p,letterSpacing:h})[g],k=O*(S.coordinate+O*E/2-n);b[w-1]=S=nv(nv({},S),{},{tickCoord:k>0?S.coordinate-k*O:S.coordinate}),O*(S.tickCoord-O*E/2-r)>=0&&O*(S.tickCoord+O*E/2-n)<=0&&(n=S.tickCoord-O*(E/2+s),b[w-1]=nv(nv({},S),{},{isShow:!0}))}for(var P=e?w-1:w,_=0;_<P;_++){var M=b[_],T=l()(a)?a(M.value,_):M.value,C="width"===g?ov(ei(T,{fontSize:p,letterSpacing:h}),x,i):ei(T,{fontSize:p,letterSpacing:h})[g];if(0===_){var I=O*(M.coordinate-O*C/2-r);b[_]=M=nv(nv({},M),{},{tickCoord:I<0?M.coordinate-I*O:M.coordinate})}else b[_]=M=nv(nv({},M),{},{tickCoord:M.coordinate});O*(M.tickCoord-O*C/2-r)>=0&&O*(M.tickCoord+O*C/2-n)<=0&&(r=M.tickCoord+O*(C/2+s),b[_]=nv(nv({},M),{},{isShow:!0}))}return b}function cv(t,e,r){var n=t.tick,i=t.ticks,o=t.viewBox,a=t.minTickGap,c=t.orientation,u=t.interval,s=t.tickFormatter,f=t.unit,p=t.angle;if(!i||!i.length||!n)return[];if(E(u)||fn.isSsr)return function(t,e){return tv(t,e+1)}(i,"number"==typeof u&&E(u)?u:0);var h=[];return"equidistantPreserveStart"===u?function(t){for(var e=1,r=tv(t,e,(function(t){return t.isShow}));e<=t.length;){if(void 0!==r)return r;r=tv(t,++e,(function(t){return t.isShow}))}return t.slice(0,1)}(h=av({angle:p,ticks:i,tickFormatter:s,viewBox:o,orientation:c,minTickGap:a,unit:f,fontSize:e,letterSpacing:r})):(h="preserveStart"===u||"preserveStartEnd"===u?av({angle:p,ticks:i,tickFormatter:s,viewBox:o,orientation:c,minTickGap:a,unit:f,fontSize:e,letterSpacing:r},"preserveStartEnd"===u):function(t){var e,r,n=t.angle,i=t.ticks,o=t.tickFormatter,a=t.viewBox,c=t.orientation,u=t.minTickGap,s=t.unit,f=t.fontSize,p=t.letterSpacing,h=a.x,d=a.y,y=a.width,v=a.height,m="top"===c||"bottom"===c?"width":"height",g=s&&"width"===m?ei(s,{fontSize:f,letterSpacing:p}):{width:0,height:0},b=(i||[]).slice(),x=b.length,w=x>=2?A(b[1].coordinate-b[0].coordinate):1;1===w?(e="width"===m?h:d,r="width"===m?h+y:d+v):(e="width"===m?h+y:d+v,r="width"===m?h:d);for(var O=x-1;O>=0;O--){var S=b[O],j=l()(o)?o(S.value,x-O-1):S.value,E="width"===m?ov(ei(j,{fontSize:f,letterSpacing:p}),g,n):ei(j,{fontSize:f,letterSpacing:p})[m];if(O===x-1){var k=w*(S.coordinate+w*E/2-r);b[O]=S=nv(nv({},S),{},{tickCoord:k>0?S.coordinate-k*w:S.coordinate})}else b[O]=S=nv(nv({},S),{},{tickCoord:S.coordinate});w*(S.tickCoord-w*E/2-e)>=0&&w*(S.tickCoord+w*E/2-r)<=0&&(r=S.tickCoord-w*(E/2+u),b[O]=nv(nv({},S),{},{isShow:!0}))}return b}({angle:p,ticks:i,tickFormatter:s,viewBox:o,orientation:c,minTickGap:a,unit:f,fontSize:e,letterSpacing:r}),h.filter((function(t){return t.isShow})))}Qy.displayName="ReferenceArea",Qy.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1},Qy.renderRect=function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement(Np,Ky({},e,{className:"recharts-reference-area-rect"}))};var uv=["viewBox"],sv=["viewBox"],lv=["ticks"];function fv(t){return fv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fv(t)}function pv(){return pv=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},pv.apply(this,arguments)}function hv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function dv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hv(Object(r),!0).forEach((function(e){wv(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hv(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function yv(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function vv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ov(n.key),n)}}function mv(t,e){return mv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},mv(t,e)}function gv(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=xv(t);if(e){var i=xv(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return bv(this,r)}}function bv(t,e){if(e&&("object"===fv(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function xv(t){return xv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},xv(t)}function wv(t,e,r){return(e=Ov(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ov(t){var e=function(t,e){if("object"!==fv(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==fv(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===fv(e)?e:String(e)}var Sv=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&mv(t,e)}(c,t);var e,n,o,a=gv(c);function c(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,c),(e=a.call(this,t)).state={fontSize:"",letterSpacing:""},e}return e=c,n=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=yv(t,uv),i=this.props,o=i.viewBox,a=yv(i,sv);return!N(r,o)||!N(n,a)||!N(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,i,o,a,c=this.props,u=c.x,s=c.y,l=c.width,f=c.height,p=c.orientation,h=c.tickSize,d=c.mirror,y=c.tickMargin,v=d?-1:1,m=t.tickSize||h,g=E(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(i=s+ +!d*f)-v*m)-v*y,o=g;break;case"left":n=i=t.coordinate,o=(e=(r=u+ +!d*l)-v*m)-v*y,a=g;break;case"right":n=i=t.coordinate,o=(e=(r=u+ +d*l)+v*m)+v*y,a=g;break;default:e=r=t.coordinate,a=(n=(i=s+ +d*f)+v*m)+v*y,o=g}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,n=t.y,o=t.width,a=t.height,c=t.orientation,u=t.mirror,s=t.axisLine,l=dv(dv(dv({},rt(this.props)),rt(s)),{},{fill:"none"});if("top"===c||"bottom"===c){var f=+("top"===c&&!u||"bottom"===c&&u);l=dv(dv({},l),{},{x1:e,y1:n+f*a,x2:e+o,y2:n+f*a})}else{var p=+("left"===c&&!u||"right"===c&&u);l=dv(dv({},l),{},{x1:e+p*o,y1:n,x2:e+p*o,y2:n+a})}return r().createElement("line",pv({},l,{className:i()("recharts-cartesian-axis-line",d()(s,"className"))}))}},{key:"renderTicks",value:function(t,e,n){var o=this,a=this.props,u=a.tickLine,s=a.stroke,f=a.tick,p=a.tickFormatter,h=a.unit,y=cv(dv(dv({},this.props),{},{ticks:t}),e,n),v=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),g=rt(this.props),b=rt(f),x=dv(dv({},g),{},{fill:"none"},rt(u)),w=y.map((function(t,e){var n=o.getTickLineCoord(t),a=n.line,w=n.tick,O=dv(dv(dv(dv({textAnchor:v,verticalAnchor:m},g),{},{stroke:"none",fill:s},b),w),{},{index:e,payload:t,visibleTicksCount:y.length,tickFormatter:p});return r().createElement(ht,pv({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e)},U(o.props,t,e)),u&&r().createElement("line",pv({},x,a,{className:i()("recharts-cartesian-axis-tick-line",d()(u,"className"))})),f&&c.renderTickItem(f,O,"".concat(l()(p)?p(t.value,e):t.value).concat(h||"")))}));return r().createElement("g",{className:"recharts-cartesian-axis-ticks"},w)}},{key:"render",value:function(){var t=this,e=this.props,n=e.axisLine,o=e.width,a=e.height,c=e.ticksGenerator,u=e.className;if(e.hide)return null;var s=this.props,f=s.ticks,p=yv(s,lv),h=f;return l()(c)&&(h=f&&f.length>0?c(this.props):c(p)),o<=0||a<=0||!h||!h.length?null:r().createElement(ht,{className:i()("recharts-cartesian-axis",u),ref:function(e){t.layerReference=e}},n&&this.renderAxisLine(),this.renderTicks(h,this.state.fontSize,this.state.letterSpacing),Af.renderCallByParent(this.props))}}],o=[{key:"renderTickItem",value:function(t,e,n){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement(pi,pv({},e,{className:"recharts-cartesian-axis-tick-value"}),n)}}],n&&vv(e.prototype,n),o&&vv(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.Component);wv(Sv,"displayName","CartesianAxis"),wv(Sv,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var Av=["x1","y1","x2","y2","key"];function jv(t){return jv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jv(t)}function Ev(){return Ev=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ev.apply(this,arguments)}function kv(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function Pv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function _v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Pv(Object(r),!0).forEach((function(e){Rv(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Pv(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Mv(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Tv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Lv(n.key),n)}}function Cv(t,e){return Cv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Cv(t,e)}function Iv(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Dv(t);if(e){var i=Dv(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return Nv(this,r)}}function Nv(t,e){if(e&&("object"===jv(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function Dv(t){return Dv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Dv(t)}function Rv(t,e,r){return(e=Lv(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Lv(t){var e=function(t,e){if("object"!==jv(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==jv(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===jv(e)?e:String(e)}var Bv=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Cv(t,e)}(a,t);var e,n,i,o=Iv(a);function a(){return Mv(this,a),o.apply(this,arguments)}return e=a,n=[{key:"renderHorizontal",value:function(t){var e=this,n=this.props,i=n.x,o=n.width,c=n.horizontal;if(!t||!t.length)return null;var u=t.map((function(t,r){var n=_v(_v({},e.props),{},{x1:i,y1:t,x2:i+o,y2:t,key:"line-".concat(r),index:r});return a.renderLineItem(c,n)}));return r().createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}},{key:"renderVertical",value:function(t){var e=this,n=this.props,i=n.y,o=n.height,c=n.vertical;if(!t||!t.length)return null;var u=t.map((function(t,r){var n=_v(_v({},e.props),{},{x1:t,y1:i,x2:t,y2:i+o,key:"line-".concat(r),index:r});return a.renderLineItem(c,n)}));return r().createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}},{key:"renderVerticalStripes",value:function(t){var e=this.props.verticalFill;if(!e||!e.length)return null;var n=this.props,i=n.fillOpacity,o=n.x,a=n.y,c=n.width,u=n.height,s=t.map((function(t){return Math.round(t+o-o)})).sort((function(t,e){return t-e}));o!==s[0]&&s.unshift(0);var l=s.map((function(t,n){var l=s[n+1]?s[n+1]-t:o+c-t;if(l<=0)return null;var f=n%e.length;return r().createElement("rect",{key:"react-".concat(n),x:t,y:a,width:l,height:u,stroke:"none",fill:e[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})}));return r().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}},{key:"renderHorizontalStripes",value:function(t){var e=this.props.horizontalFill;if(!e||!e.length)return null;var n=this.props,i=n.fillOpacity,o=n.x,a=n.y,c=n.width,u=n.height,s=t.map((function(t){return Math.round(t+a-a)})).sort((function(t,e){return t-e}));a!==s[0]&&s.unshift(0);var l=s.map((function(t,n){var l=s[n+1]?s[n+1]-t:a+u-t;if(l<=0)return null;var f=n%e.length;return r().createElement("rect",{key:"react-".concat(n),y:t,x:o,height:l,width:c,stroke:"none",fill:e[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})}));return r().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}},{key:"renderBackground",value:function(){var t=this.props.fill;if(!t||"none"===t)return null;var e=this.props,n=e.fillOpacity,i=e.x,o=e.y,a=e.width,c=e.height;return r().createElement("rect",{x:i,y:o,width:a,height:c,stroke:"none",fill:t,fillOpacity:n,className:"recharts-cartesian-grid-bg"})}},{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,i=t.width,o=t.height,a=t.horizontal,c=t.vertical,u=t.horizontalCoordinatesGenerator,s=t.verticalCoordinatesGenerator,f=t.xAxis,p=t.yAxis,h=t.offset,d=t.chartWidth,y=t.chartHeight;if(!E(i)||i<=0||!E(o)||o<=0||!E(e)||e!==+e||!E(n)||n!==+n)return null;var v=this.props,m=v.horizontalPoints,g=v.verticalPoints;return m&&m.length||!l()(u)||(m=u({yAxis:p,width:d,height:y,offset:h})),g&&g.length||!l()(s)||(g=s({xAxis:f,width:d,height:y,offset:h})),r().createElement("g",{className:"recharts-cartesian-grid"},this.renderBackground(),a&&this.renderHorizontal(m),c&&this.renderVertical(g),a&&this.renderHorizontalStripes(m),c&&this.renderVerticalStripes(g))}}],i=[{key:"renderLineItem",value:function(t,e){var n;if(r().isValidElement(t))n=r().cloneElement(t,e);else if(l()(t))n=t(e);else{var i=e.x1,o=e.y1,a=e.x2,c=e.y2,u=e.key,s=kv(e,Av);n=r().createElement("line",Ev({},rt(s),{x1:i,y1:o,x2:a,y2:c,fill:"none",key:u}))}return n}}],n&&Tv(e.prototype,n),i&&Tv(e,i),Object.defineProperty(e,"prototype",{writable:!1}),a}(e.PureComponent);Rv(Bv,"displayName","CartesianGrid"),Rv(Bv,"defaultProps",{horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]});var zv=["type","layout","connectNulls","ref"];function Fv(t){return Fv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fv(t)}function Uv(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function Wv(){return Wv=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Wv.apply(this,arguments)}function $v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Vv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?$v(Object(r),!0).forEach((function(e){tm(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):$v(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Hv(t){return function(t){if(Array.isArray(t))return qv(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return qv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return qv(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qv(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Gv(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Xv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,em(n.key),n)}}function Kv(t,e){return Kv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Kv(t,e)}function Yv(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Qv(t);if(e){var i=Qv(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return Jv(this,r)}}function Jv(t,e){if(e&&("object"===Fv(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Zv(t)}function Zv(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Qv(t){return Qv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Qv(t)}function tm(t,e,r){return(e=em(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function em(t){var e=function(t,e){if("object"!==Fv(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Fv(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Fv(e)?e:String(e)}var rm=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Kv(t,e)}(c,t);var e,n,o,a=Yv(c);function c(){var t;Gv(this,c);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return tm(Zv(t=a.call.apply(a,[this].concat(r))),"state",{isAnimationFinished:!0,totalLength:0}),tm(Zv(t),"getStrokeDasharray",(function(t,e,r){for(var n=r.reduce((function(t,e){return t+e})),i=Math.floor(t/n),o=t%n,a=e-t,u=[],s=0,l=0;;l+=r[s],++s)if(l+r[s]>o){u=[].concat(Hv(r.slice(0,s)),[o-l]);break}var f=u.length%2==0?[0,a]:[a];return[].concat(Hv(c.repeat(r,i)),Hv(u),f).map((function(t){return"".concat(t,"px")})).join(", ")})),tm(Zv(t),"id",_("recharts-line-")),tm(Zv(t),"pathRef",(function(e){t.mainCurve=e})),tm(Zv(t),"handleAnimationEnd",(function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()})),tm(Zv(t),"handleAnimationStart",(function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()})),t}return e=c,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(Hv(t),[0]):t,n=[],i=0;i<e;++i)n=[].concat(Hv(n),Hv(r));return n}},{key:"renderDotItem",value:function(t,e){var n;if(r().isValidElement(t))n=r().cloneElement(t,e);else if(l()(t))n=t(e);else{var o=i()("recharts-line-dot",t?t.className:"");n=r().createElement(Vp,Wv({},e,{className:o}))}return n}}],(n=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,i=n.points,o=n.xAxis,a=n.yAxis,c=n.layout,u=Y(n.children,xl);if(!u)return null;var s=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:kl(t.payload,e)}},l={clipPath:t?"url(#clipPath-".concat(e,")"):null};return r().createElement(ht,l,u.map((function(t,e){return r().cloneElement(t,{key:"bar-".concat(e),data:i,xAxis:o,yAxis:a,layout:c,dataPointFormatter:s})})))}},{key:"renderDots",value:function(t,e,n){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,o=i.dot,a=i.points,u=i.dataKey,s=rt(this.props),l=rt(o,!0),f=a.map((function(t,e){var r=Vv(Vv(Vv({key:"dot-".concat(e),r:3},s),l),{},{value:t.value,dataKey:u,cx:t.x,cy:t.y,index:e,payload:t.payload});return c.renderDotItem(o,r)})),p={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(n,")"):null};return r().createElement(ht,Wv({className:"recharts-line-dots",key:"dots"},p,{role:"img"}),f)}},{key:"renderCurveStatically",value:function(t,e,n,i){var o=this.props,a=o.type,c=o.layout,u=o.connectNulls,s=(o.ref,Uv(o,zv)),l=Vv(Vv(Vv({},rt(s,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(n,")"):null,points:t},i),{},{type:a,layout:c,connectNulls:u});return r().createElement(Pp,Wv({},l,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var n=this,i=this.props,o=i.points,a=i.strokeDasharray,c=i.isAnimationActive,u=i.animationBegin,s=i.animationDuration,l=i.animationEasing,f=i.animationId,p=i.animateNewValues,h=i.width,d=i.height,y=this.state,v=y.prevPoints,m=y.totalLength;return r().createElement(Qr,{begin:u,duration:s,isActive:c,easing:l,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(r){var i=r.t;if(v){var c=v.length/o.length,u=o.map((function(t,e){var r=Math.floor(e*c);if(v[r]){var n=v[r],o=C(n.x,t.x),a=C(n.y,t.y);return Vv(Vv({},t),{},{x:o(i),y:a(i)})}if(p){var u=C(2*h,t.x),s=C(d/2,t.y);return Vv(Vv({},t),{},{x:u(i),y:s(i)})}return Vv(Vv({},t),{},{x:t.x,y:t.y})}));return n.renderCurveStatically(u,t,e)}var s,l=C(0,m)(i);if(a){var f="".concat(a).split(/[,\s]+/gim).map((function(t){return parseFloat(t)}));s=n.getStrokeDasharray(l,m,f)}else s="".concat(l,"px ").concat(m-l,"px");return n.renderCurveStatically(o,t,e,{strokeDasharray:s})}))}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,i=r.isAnimationActive,o=this.state,a=o.prevPoints,c=o.totalLength;return i&&n&&n.length&&(!a&&c>0||!di()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.dot,o=t.points,a=t.className,c=t.xAxis,u=t.yAxis,s=t.top,l=t.left,f=t.width,p=t.height,h=t.isAnimationActive,d=t.id;if(e||!o||!o.length)return null;var y=this.state.isAnimationFinished,m=1===o.length,g=i()("recharts-line",a),b=c&&c.allowDataOverflow,x=u&&u.allowDataOverflow,w=b||x,O=v()(d)?this.id:d,S=rt(n)||{r:3,strokeWidth:2},A=S.r,j=S.strokeWidth,E=n.clipDot,k=void 0===E||E,P=2*A+j;return r().createElement(ht,{className:g},b||x?r().createElement("defs",null,r().createElement("clipPath",{id:"clipPath-".concat(O)},r().createElement("rect",{x:b?l:l-f/2,y:x?s:s-p/2,width:b?f:2*f,height:x?p:2*p})),!k&&r().createElement("clipPath",{id:"clipPath-dots-".concat(O)},r().createElement("rect",{x:l-P/2,y:s-P/2,width:f+P,height:p+P}))):null,!m&&this.renderCurve(w,O),this.renderErrorBar(w,O),(m||n)&&this.renderDots(w,k,O),(!h||y)&&zf.renderCallByParent(this.props,o))}}])&&Xv(e.prototype,n),o&&Xv(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.PureComponent);tm(rm,"displayName","Line"),tm(rm,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!fn.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),tm(rm,"getComposedData",(function(t){var e=t.props,r=t.xAxis,n=t.yAxis,i=t.xAxisTicks,o=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,s=t.offset,l=e.layout,f=u.map((function(t,e){var u=kl(t,a);return"horizontal"===l?{x:Hl({axis:r,ticks:i,bandSize:c,entry:t,index:e}),y:v()(u)?null:n.scale(u),value:u,payload:t}:{x:v()(u)?null:r.scale(u),y:Hl({axis:n,ticks:o,bandSize:c,entry:t,index:e}),value:u,payload:t}}));return Vv({points:f,layout:l},s)}));var nm=["layout","type","stroke","connectNulls","isRange","ref"];function im(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function om(){return om=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},om.apply(this,arguments)}function am(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function cm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?am(Object(r),!0).forEach((function(e){ym(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):am(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function um(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function sm(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,vm(n.key),n)}}function lm(t,e){return lm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},lm(t,e)}function fm(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=dm(t);if(e){var i=dm(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return pm(this,r)}}function pm(t,e){if(e&&("object"===mm(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return hm(t)}function hm(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function dm(t){return dm=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},dm(t)}function ym(t,e,r){return(e=vm(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function vm(t){var e=function(t,e){if("object"!==mm(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==mm(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===mm(e)?e:String(e)}function mm(t){return mm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mm(t)}var gm=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&lm(t,e)}(c,t);var e,n,o,a=fm(c);function c(){var t;um(this,c);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return ym(hm(t=a.call.apply(a,[this].concat(r))),"state",{isAnimationFinished:!0}),ym(hm(t),"id",_("recharts-area-")),ym(hm(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),l()(e)&&e()})),ym(hm(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),l()(e)&&e()})),t}return e=c,n=[{key:"renderDots",value:function(t,e,n){var i=this.props.isAnimationActive,o=this.state.isAnimationFinished;if(i&&!o)return null;var a=this.props,u=a.dot,s=a.points,l=a.dataKey,f=rt(this.props),p=rt(u,!0),h=s.map((function(t,e){var r=cm(cm(cm({key:"dot-".concat(e),r:3},f),p),{},{dataKey:l,cx:t.x,cy:t.y,index:e,value:t.value,payload:t.payload});return c.renderDotItem(u,r)})),d={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(n,")"):null};return r().createElement(ht,om({className:"recharts-area-dots"},d),h)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,n=e.baseLine,i=e.points,o=e.strokeWidth,a=i[0].x,c=i[i.length-1].x,u=t*Math.abs(a-c),s=vi()(i.map((function(t){return t.y||0})));return E(n)&&"number"==typeof n?s=Math.max(n,s):n&&g()(n)&&n.length&&(s=Math.max(vi()(n.map((function(t){return t.y||0}))),s)),E(s)?r().createElement("rect",{x:a<c?a:a-u,y:0,width:u,height:Math.floor(s+(o?parseInt("".concat(o),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,n=e.baseLine,i=e.points,o=e.strokeWidth,a=i[0].y,c=i[i.length-1].y,u=t*Math.abs(a-c),s=vi()(i.map((function(t){return t.x||0})));return E(n)&&"number"==typeof n?s=Math.max(n,s):n&&g()(n)&&n.length&&(s=Math.max(vi()(n.map((function(t){return t.x||0}))),s)),E(s)?r().createElement("rect",{x:0,y:a<c?a:a-u,width:s+(o?parseInt("".concat(o),10):1),height:Math.floor(u)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,n,i){var o=this.props,a=o.layout,c=o.type,u=o.stroke,s=o.connectNulls,l=o.isRange,f=(o.ref,im(o,nm));return r().createElement(ht,{clipPath:n?"url(#clipPath-".concat(i,")"):null},r().createElement(Pp,om({},rt(f,!0),{points:t,connectNulls:s,type:c,baseLine:e,layout:a,stroke:"none",className:"recharts-area-area"})),"none"!==u&&r().createElement(Pp,om({},rt(this.props),{className:"recharts-area-curve",layout:a,type:c,connectNulls:s,fill:"none",points:t})),"none"!==u&&l&&r().createElement(Pp,om({},rt(this.props),{className:"recharts-area-curve",layout:a,type:c,connectNulls:s,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var n=this,i=this.props,o=i.points,a=i.baseLine,c=i.isAnimationActive,u=i.animationBegin,s=i.animationDuration,l=i.animationEasing,f=i.animationId,p=this.state,h=p.prevPoints,d=p.prevBaseLine;return r().createElement(Qr,{begin:u,duration:s,isActive:c,easing:l,from:{t:0},to:{t:1},key:"area-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(i){var c=i.t;if(h){var u,s=h.length/o.length,l=o.map((function(t,e){var r=Math.floor(e*s);if(h[r]){var n=h[r],i=C(n.x,t.x),o=C(n.y,t.y);return cm(cm({},t),{},{x:i(c),y:o(c)})}return t}));return u=E(a)&&"number"==typeof a?C(d,a)(c):v()(a)||w()(a)?C(d,0)(c):a.map((function(t,e){var r=Math.floor(e*s);if(d[r]){var n=d[r],i=C(n.x,t.x),o=C(n.y,t.y);return cm(cm({},t),{},{x:i(c),y:o(c)})}return t})),n.renderAreaStatically(l,u,t,e)}return r().createElement(ht,null,r().createElement("defs",null,r().createElement("clipPath",{id:"animationClipPath-".concat(e)},n.renderClipRect(c))),r().createElement(ht,{clipPath:"url(#animationClipPath-".concat(e,")")},n.renderAreaStatically(o,a,t,e)))}))}},{key:"renderArea",value:function(t,e){var r=this.props,n=r.points,i=r.baseLine,o=r.isAnimationActive,a=this.state,c=a.prevPoints,u=a.prevBaseLine,s=a.totalLength;return o&&n&&n.length&&(!c&&s>0||!di()(c,n)||!di()(u,i))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(n,i,t,e)}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.dot,o=t.points,a=t.className,c=t.top,u=t.left,s=t.xAxis,l=t.yAxis,f=t.width,p=t.height,h=t.isAnimationActive,d=t.id;if(e||!o||!o.length)return null;var y=this.state.isAnimationFinished,m=1===o.length,g=i()("recharts-area",a),b=s&&s.allowDataOverflow,x=l&&l.allowDataOverflow,w=b||x,O=v()(d)?this.id:d,S=rt(n)||{r:3,strokeWidth:2},A=S.r,j=S.strokeWidth,E=(function(t){return"object"===mm(t)&&"cx"in t&&"cy"in t&&"r"in t}(n)?n:{}).clipDot,k=void 0===E||E,P=2*A+j;return r().createElement(ht,{className:g},b||x?r().createElement("defs",null,r().createElement("clipPath",{id:"clipPath-".concat(O)},r().createElement("rect",{x:b?u:u-f/2,y:x?c:c-p/2,width:b?f:2*f,height:x?p:2*p})),!k&&r().createElement("clipPath",{id:"clipPath-dots-".concat(O)},r().createElement("rect",{x:u-P/2,y:c-P/2,width:f+P,height:p+P}))):null,m?null:this.renderArea(w,O),(n||m)&&this.renderDots(w,k,O),(!h||y)&&zf.renderCallByParent(this.props,o))}}],o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],n&&sm(e.prototype,n),o&&sm(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.PureComponent);ym(gm,"displayName","Area"),ym(gm,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!fn.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),ym(gm,"getBaseValue",(function(t,e,r,n){var i=t.layout,o=t.baseValue,a=e.props.baseValue,c=null!=a?a:o;if(E(c)&&"number"==typeof c)return c;var u="horizontal"===i?n:r,s=u.scale.domain();if("number"===u.type){var l=Math.max(s[0],s[1]),f=Math.min(s[0],s[1]);return"dataMin"===c?f:"dataMax"===c||l<0?l:Math.max(Math.min(s[0],s[1]),0)}return"dataMin"===c?s[0]:"dataMax"===c?s[1]:s[0]})),ym(gm,"getComposedData",(function(t){var e,r=t.props,n=t.item,i=t.xAxis,o=t.yAxis,a=t.xAxisTicks,c=t.yAxisTicks,u=t.bandSize,s=t.dataKey,l=t.stackedData,f=t.dataStartIndex,p=t.displayedData,h=t.offset,y=r.layout,m=l&&l.length,b=gm.getBaseValue(r,n,i,o),x=!1,w=p.map((function(t,e){var r,n=kl(t,s);m?r=l[f+e]:(r=n,g()(r)?x=!0:r=[b,r]);var p=v()(r[1])||m&&v()(n);return"horizontal"===y?{x:Hl({axis:i,ticks:a,bandSize:u,entry:t,index:e}),y:p?null:o.scale(r[1]),value:r,payload:t}:{x:p?null:i.scale(r[1]),y:Hl({axis:o,ticks:c,bandSize:u,entry:t,index:e}),value:r,payload:t}}));return e=m||x?w.map((function(t){return"horizontal"===y?{x:t.x,y:v()(d()(t,"value[0]"))||v()(d()(t,"y"))?null:o.scale(d()(t,"value[0]"))}:{x:v()(d()(t,"value[0]"))?null:i.scale(d()(t,"value[0]")),y:t.y}})):"horizontal"===y?o.scale(b):i.scale(b),cm({points:w,baseLine:e,layout:y,isRange:x},h)})),ym(gm,"renderDotItem",(function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement(Vp,om({},e,{className:"recharts-area-dot"}))}));var bm=function(){return null};function xm(t){return xm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xm(t)}function wm(){return wm=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},wm.apply(this,arguments)}function Om(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Sm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Om(Object(r),!0).forEach((function(e){Tm(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Om(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Am(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function jm(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Cm(n.key),n)}}function Em(t,e){return Em=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Em(t,e)}function km(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Mm(t);if(e){var i=Mm(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return Pm(this,r)}}function Pm(t,e){if(e&&("object"===xm(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return _m(t)}function _m(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Mm(t){return Mm=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Mm(t)}function Tm(t,e,r){return(e=Cm(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Cm(t){var e=function(t,e){if("object"!==xm(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==xm(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===xm(e)?e:String(e)}bm.displayName="ZAxis",bm.defaultProps={zAxisId:0,range:[64,64],scale:"auto",type:"number"};var Im=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Em(t,e)}(c,t);var e,n,o,a=km(c);function c(){var t;Am(this,c);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return Tm(_m(t=a.call.apply(a,[this].concat(r))),"state",{isAnimationFinished:!1}),Tm(_m(t),"handleAnimationEnd",(function(){t.setState({isAnimationFinished:!0})})),Tm(_m(t),"handleAnimationStart",(function(){t.setState({isAnimationFinished:!1})})),Tm(_m(t),"id",_("recharts-scatter-")),t}return e=c,n=[{key:"renderSymbolsStatically",value:function(t){var e=this,n=this.props,i=n.shape,o=n.activeShape,a=n.activeIndex,u=rt(this.props);return t.map((function(t,n){var s=Sm(Sm({key:"symbol-".concat(n)},u),t);return r().createElement(ht,wm({className:"recharts-scatter-symbol"},U(e.props,t,n),{key:"symbol-".concat(n),role:"img"}),c.renderSymbolItem(a===n?o:i,s))}))}},{key:"renderSymbolsWithAnimation",value:function(){var t=this,e=this.props,n=e.points,i=e.isAnimationActive,o=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,s=this.state.prevPoints;return r().createElement(Qr,{begin:o,duration:a,isActive:i,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var i=e.t,o=n.map((function(t,e){var r=s&&s[e];if(r){var n=C(r.cx,t.cx),o=C(r.cy,t.cy),a=C(r.size,t.size);return Sm(Sm({},t),{},{cx:n(i),cy:o(i),size:a(i)})}var c=C(0,t.size);return Sm(Sm({},t),{},{size:c(i)})}));return r().createElement(ht,null,t.renderSymbolsStatically(o))}))}},{key:"renderSymbols",value:function(){var t=this.props,e=t.points,r=t.isAnimationActive,n=this.state.prevPoints;return!(r&&e&&e.length)||n&&di()(n,e)?this.renderSymbolsStatically(e):this.renderSymbolsWithAnimation()}},{key:"renderErrorBar",value:function(){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,e=t.points,n=t.xAxis,i=t.yAxis,o=Y(t.children,xl);return o?o.map((function(t,o){var a=t.props.direction;return r().cloneElement(t,{key:o,data:e,xAxis:n,yAxis:i,layout:"x"===a?"vertical":"horizontal",dataPointFormatter:function(t,e){return{x:t.cx,y:t.cy,value:"x"===a?+t.node.x:+t.node.y,errorVal:kl(t,e)}}})})):null}},{key:"renderLine",value:function(){var t,e,n=this.props,i=n.points,o=n.line,a=n.lineType,c=n.lineJointType,u=rt(this.props),s=rt(o);if("joint"===a)t=i.map((function(t){return{x:t.cx,y:t.cy}}));else if("fitting"===a){var f=function(t){if(!t||!t.length)return null;for(var e=t.length,r=0,n=0,i=0,o=0,a=1/0,c=-1/0,u=0,s=0,l=0;l<e;l++)r+=u=t[l].cx||0,n+=s=t[l].cy||0,i+=u*s,o+=u*u,a=Math.min(a,u),c=Math.max(c,u);var f=e*o!=r*r?(e*i-r*n)/(e*o-r*r):0;return{xmin:a,xmax:c,a:f,b:(n-f*r)/e}}(i),p=f.xmin,h=f.xmax,d=f.a,y=f.b,v=function(t){return d*t+y};t=[{x:p,y:v(p)},{x:h,y:v(h)}]}var m=Sm(Sm(Sm({},u),{},{fill:"none",stroke:u&&u.fill},s),{},{points:t});return e=r().isValidElement(o)?r().cloneElement(o,m):l()(o)?o(m):r().createElement(Pp,wm({},m,{type:c})),r().createElement(ht,{className:"recharts-scatter-line",key:"recharts-scatter-line"},e)}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.points,o=t.line,a=t.className,c=t.xAxis,u=t.yAxis,s=t.left,l=t.top,f=t.width,p=t.height,h=t.id,d=t.isAnimationActive;if(e||!n||!n.length)return null;var y=this.state.isAnimationFinished,m=i()("recharts-scatter",a),g=c&&c.allowDataOverflow,b=u&&u.allowDataOverflow,x=g||b,w=v()(h)?this.id:h;return r().createElement(ht,{className:m,clipPath:x?"url(#clipPath-".concat(w,")"):null},g||b?r().createElement("defs",null,r().createElement("clipPath",{id:"clipPath-".concat(w)},r().createElement("rect",{x:g?s:s-f/2,y:b?l:l-p/2,width:g?f:2*f,height:b?p:2*p}))):null,o&&this.renderLine(),this.renderErrorBar(),r().createElement(ht,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!d||y)&&zf.renderCallByParent(this.props,n))}}],o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"renderSymbolItem",value:function(t,e){var n;return r().isValidElement(t)?n=r().cloneElement(t,e):l()(t)?n=t(e):"string"==typeof t&&(n=r().createElement(Jt,wm({},e,{type:t}))),n}}],n&&jm(e.prototype,n),o&&jm(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.PureComponent);Tm(Im,"displayName","Scatter"),Tm(Im,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!fn.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"}),Tm(Im,"getComposedData",(function(t){var e=t.xAxis,r=t.yAxis,n=t.zAxis,i=t.item,o=t.displayedData,a=t.xAxisTicks,c=t.yAxisTicks,u=t.offset,s=i.props.tooltipType,l=Y(i.props.children,Un),f=v()(e.dataKey)?i.props.dataKey:e.dataKey,p=v()(r.dataKey)?i.props.dataKey:r.dataKey,h=n&&n.dataKey,d=n?n.range:bm.defaultProps.range,y=d&&d[0],m=e.scale.bandwidth?e.scale.bandwidth():0,g=r.scale.bandwidth?r.scale.bandwidth():0,b=o.map((function(t,o){var u=kl(t,f),d=kl(t,p),b=!v()(h)&&kl(t,h)||"-",x=[{name:v()(e.dataKey)?i.props.name:e.name||e.dataKey,unit:e.unit||"",value:u,payload:t,dataKey:f,type:s},{name:v()(r.dataKey)?i.props.name:r.name||r.dataKey,unit:r.unit||"",value:d,payload:t,dataKey:p,type:s}];"-"!==b&&x.push({name:n.name||n.dataKey,unit:n.unit||"",value:b,payload:t,dataKey:h,type:s});var w=Hl({axis:e,ticks:a,bandSize:m,entry:t,index:o,dataKey:f}),O=Hl({axis:r,ticks:c,bandSize:g,entry:t,index:o,dataKey:p}),S="-"!==b?n.scale(b):y,A=Math.sqrt(Math.max(S,0)/Math.PI);return Sm(Sm({},t),{},{cx:w,cy:O,x:w-A,y:O-A,xAxis:e,yAxis:r,zAxis:n,width:2*A,height:2*A,size:S,node:{x:u,y:d,z:b},tooltipPayload:x,tooltipPosition:{x:w,y:O},payload:t},l&&l[o]&&l[o].props)}));return Sm({points:b},u)}));var Nm=function(){return null};Nm.displayName="XAxis",Nm.defaultProps={allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0};var Dm=function(){return null};Dm.displayName="YAxis",Dm.defaultProps={allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1};var Rm=o(3311),Lm=o.n(Rm),Bm=o(1584),zm=o.n(Bm);function Fm(t){return function(t){if(Array.isArray(t))return Um(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Um(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Um(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Um(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Wm=function(t,e,r,n,i){var o=Y(t,Uy),a=Y(t,Gy),c=[].concat(Fm(o),Fm(a)),u=Y(t,Qy),s="".concat(n,"Id"),l=n[0],f=e;if(c.length&&(f=c.reduce((function(t,e){if(e.props[s]===r&&ry(e.props,"extendDomain")&&E(e.props[l])){var n=e.props[l];return[Math.min(t[0],n),Math.max(t[1],n)]}return t}),f)),u.length){var p="".concat(l,"1"),h="".concat(l,"2");f=u.reduce((function(t,e){if(e.props[s]===r&&ry(e.props,"extendDomain")&&E(e.props[p])&&E(e.props[h])){var n=e.props[p],i=e.props[h];return[Math.min(t[0],n,i),Math.max(t[1],n,i)]}return t}),f)}return i&&i.length&&(f=i.reduce((function(t,e){return E(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t}),f)),f},$m=o(6729),Vm=new(o.n($m)());Vm.setMaxListeners&&Vm.setMaxListeners(10);var Hm="recharts.syncMouseEvents";function qm(t){return qm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qm(t)}function Gm(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Km(n.key),n)}}function Xm(t,e,r){return(e=Km(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Km(t){var e=function(t,e){if("object"!==qm(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==qm(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===qm(e)?e:String(e)}var Ym=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Xm(this,"activeIndex",0),Xm(this,"coordinateList",[]),Xm(this,"layout","horizontal")}var e,r,n;return e=t,(r=[{key:"setDetails",value:function(t){var e=t.coordinateList,r=void 0===e?[]:e,n=t.container,i=void 0===n?null:n,o=t.layout,a=void 0===o?null:o,c=t.offset,u=void 0===c?null:c,s=t.mouseHandlerCallback,l=void 0===s?null:s;this.coordinateList=null!=r?r:this.coordinateList,this.container=null!=i?i:this.container,this.layout=null!=a?a:this.layout,this.offset=null!=u?u:this.offset,this.mouseHandlerCallback=null!=l?l:this.mouseHandlerCallback,this.activeIndex>=this.coordinateList.length&&(this.activeIndex=this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout){var t=this.container.getBoundingClientRect(),e=t.x,r=t.y,n=e+this.coordinateList[this.activeIndex].coordinate,i=r+this.offset.top;this.mouseHandlerCallback({pageX:n,pageY:i})}}}])&&Gm(e.prototype,r),n&&Gm(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),Jm=["item"],Zm=["children","className","width","height","style","compact","title","desc"];function Qm(t){return Qm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qm(t)}function tg(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||lg(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eg(){return eg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},eg.apply(this,arguments)}function rg(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function ng(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yg(n.key),n)}}function ig(t,e){return ig=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ig(t,e)}function og(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=ug(t);if(e){var i=ug(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return ag(this,r)}}function ag(t,e){if(e&&("object"===Qm(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return cg(t)}function cg(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ug(t){return ug=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ug(t)}function sg(t){return function(t){if(Array.isArray(t))return fg(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||lg(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lg(t,e){if(t){if("string"==typeof t)return fg(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?fg(t,e):void 0}}function fg(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function pg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function hg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pg(Object(r),!0).forEach((function(e){dg(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pg(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function dg(t,e,r){return(e=yg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yg(t){var e=function(t,e){if("object"!==Qm(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Qm(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Qm(e)?e:String(e)}var vg={xAxis:["bottom","top"],yAxis:["left","right"]},mg={x:0,y:0},gg=Number.isFinite?Number.isFinite:isFinite,bg="function"==typeof requestAnimationFrame?requestAnimationFrame:"function"==typeof setImmediate?setImmediate:setTimeout,xg="function"==typeof cancelAnimationFrame?cancelAnimationFrame:"function"==typeof clearImmediate?clearImmediate:clearTimeout,wg=function(t,e,r){var n=e.graphicalItems,i=e.dataStartIndex,o=e.dataEndIndex,a=(n||[]).reduce((function(t,e){var r=e.props.data;return r&&r.length?[].concat(sg(t),sg(r)):t}),[]);return a&&a.length>0?a:r&&r.props&&r.props.data&&r.props.data.length>0?r.props.data:t&&t.length&&E(i)&&E(o)?t.slice(i,o+1):[]};function Og(t){return"number"===t?[0,"auto"]:void 0}var Sg=function(t,e,r,n){var i=t.graphicalItems,o=t.tooltipAxis,a=wg(e,t);return r<0||!i||!i.length||r>=a.length?null:i.reduce((function(t,e){if(e.props.hide)return t;var i,c=e.props.data;o.dataKey&&!o.allowDuplicatedCategory?i=I(void 0===c?a:c,o.dataKey,n):i=c&&c[r]||a[r];return i?[].concat(sg(t),[tf(e,i)]):t}),[])},Ag=function(t,e,r,n){var i=n||{x:t.chartX,y:t.chartY},o=function(t,e){return"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius}(i,r),a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,s=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(i&&"angleAxis"===i.axisType&&Math.abs(Math.abs(i.range[1]-i.range[0])-360)<=1e-6)for(var c=i.range,u=0;u<a;u++){var s=u>0?n[u-1].coordinate:n[a-1].coordinate,l=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(A(l-s)!==A(f-l)){var h=[];if(A(f-l)===A(c[1]-c[0])){p=f;var d=l+c[1]-c[0];h[0]=Math.min(d,(d+s)/2),h[1]=Math.max(d,(d+s)/2)}else{p=s;var y=f+c[1]-c[0];h[0]=Math.min(l,(y+l)/2),h[1]=Math.max(l,(y+l)/2)}var v=[Math.min(l,(p+l)/2),Math.max(l,(p+l)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){o=n[u].index;break}}else{var m=Math.min(s,f),g=Math.max(s,f);if(t>(m+l)/2&&t<=(g+l)/2){o=n[u].index;break}}}else for(var b=0;b<a;b++)if(0===b&&t<=(r[b].coordinate+r[b+1].coordinate)/2||b>0&&b<a-1&&t>(r[b].coordinate+r[b-1].coordinate)/2&&t<=(r[b].coordinate+r[b+1].coordinate)/2||b===a-1&&t>(r[b].coordinate+r[b-1].coordinate)/2){o=r[b].index;break}return o}(o,a,u,c);if(s>=0&&u){var l=u[s]&&u[s].value,f=Sg(t,e,s,l),p=function(t,e,r,n){var i=e.find((function(t){return t&&t.index===r}));if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var o=i.coordinate,a=n.radius;return hg(hg(hg({},n),lf(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var c=i.coordinate,u=n.angle;return hg(hg(hg({},n),lf(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return mg}(r,a,s,i);return{activeTooltipIndex:s,activeLabel:l,activePayload:f,activeCoordinate:p}}return null},jg=function(t,e){var r=e.axes,n=e.graphicalItems,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.layout,l=t.children,f=t.stackOffset,p=Il(s,i),h=r.reduce((function(e,r){var h,d=r.props,y=d.type,m=d.dataKey,b=d.allowDataOverflow,x=d.allowDuplicatedCategory,w=d.scale,O=d.ticks,S=d.includeHidden,A=r.props[o];if(e[A])return e;var j,k,P,_=wg(t.data,{graphicalItems:n.filter((function(t){return t.props[o]===A})),dataStartIndex:c,dataEndIndex:u}),M=_.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],i=null==t?void 0:t[1];if(n&&i&&E(n)&&E(i))return!0}return!1})(r.props.domain,b,y)&&(j=Jl(r.props.domain,null,b),!p||"number"!==y&&"auto"===w||(P=Pl(_,m,"category")));var T=Og(y);if(!j||0===j.length){var C,I=null!==(C=r.props.domain)&&void 0!==C?C:T;if(m){if(j=Pl(_,m,y),"category"===y&&p){var N=function(t){if(!g()(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1}(j);x&&N?(k=j,j=Nd()(0,M)):x||(j=Ql(I,j,r).reduce((function(t,e){return t.indexOf(e)>=0?t:[].concat(sg(t),[e])}),[]))}else if("category"===y)j=x?j.filter((function(t){return""!==t&&!v()(t)})):Ql(I,j,r).reduce((function(t,e){return t.indexOf(e)>=0||""===e||v()(e)?t:[].concat(sg(t),[e])}),[]);else if("number"===y){var D=function(t,e,r,n,i){var o=e.map((function(e){return Tl(t,e,r,i,n)})).filter((function(t){return!v()(t)}));return o&&o.length?o.reduce((function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}),[1/0,-1/0]):null}(_,n.filter((function(t){return t.props[o]===A&&(S||!t.props.hide)})),m,i,s);D&&(j=D)}!p||"number"!==y&&"auto"===w||(P=Pl(_,m,"category"))}else j=p?Nd()(0,M):a&&a[A]&&a[A].hasStack&&"number"===y?"expand"===f?[0,1]:Xl(a[A].stackGroups,c,u):Cl(_,n.filter((function(t){return t.props[o]===A&&(S||!t.props.hide)})),y,s,!0);if("number"===y)j=Wm(l,j,A,i,O),I&&(j=Jl(I,j,b));else if("category"===y&&I){var R=I;j.every((function(t){return R.indexOf(t)>=0}))&&(j=R)}}return hg(hg({},e),{},dg({},A,hg(hg({},r.props),{},{axisType:i,domain:j,categoricalDomain:P,duplicateDomain:k,originalDomain:null!==(h=r.props.domain)&&void 0!==h?h:T,isCategorical:p,layout:s})))}),{});return h},Eg=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,i=e.AxisComp,o=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.children,l="".concat(n,"Id"),f=Y(s,i),p={};return f&&f.length?p=jg(t,{axes:f,graphicalItems:o,axisType:n,axisIdKey:l,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):o&&o.length&&(p=function(t,e){var r=e.graphicalItems,n=e.Axis,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.layout,l=t.children,f=wg(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=Il(s,i),y=-1;return r.reduce((function(t,e){var v,m=e.props[o],g=Og("number");return t[m]?t:(y++,h?v=Nd()(0,p):a&&a[m]&&a[m].hasStack?(v=Xl(a[m].stackGroups,c,u),v=Wm(l,v,m,i)):(v=Jl(g,Cl(f,r.filter((function(t){return t.props[o]===m&&!t.props.hide})),"number",s),n.defaultProps.allowDataOverflow),v=Wm(l,v,m,i)),hg(hg({},t),{},dg({},m,hg(hg({axisType:i},n.defaultProps),{},{hide:!0,orientation:d()(vg,"".concat(i,".").concat(y%2),null),domain:v,originalDomain:g,isCategorical:h,layout:s}))))}),{})}(t,{Axis:i,graphicalItems:o,axisType:n,axisIdKey:l,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},kg=function(t){var e,r,n=t.children,i=t.defaultShowTooltip,o=J(n,Qd);return{chartX:0,chartY:0,dataStartIndex:o&&o.props&&o.props.startIndex||0,dataEndIndex:void 0!==(null==o||null===(e=o.props)||void 0===e?void 0:e.endIndex)?null==o||null===(r=o.props)||void 0===r?void 0:r.endIndex:t.data&&t.data.length-1||0,activeTooltipIndex:-1,isTooltipActive:!v()(i)&&i}},Pg=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},_g=function(t,e){var r=t.props,n=(t.graphicalItems,t.xAxisMap),i=void 0===n?{}:n,o=t.yAxisMap,a=void 0===o?{}:o,c=r.width,u=r.height,s=r.children,l=r.margin||{},f=J(s,Qd),p=J(s,ke),h=Object.keys(a).reduce((function(t,e){var r=a[e],n=r.orientation;return r.mirror||r.hide?t:hg(hg({},t),{},dg({},n,t[n]+r.width))}),{left:l.left||0,right:l.right||0}),y=Object.keys(i).reduce((function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:hg(hg({},t),{},dg({},n,d()(t,"".concat(n))+r.height))}),{top:l.top||0,bottom:l.bottom||0}),v=hg(hg({},y),h),m=v.bottom;return f&&(v.bottom+=f.props.height||Qd.defaultProps.height),p&&e&&(v=function(t,e,r,n){var i=r.children,o=r.width,a=r.margin,c=o-(a.left||0)-(a.right||0),u=Ml({children:i,legendWidth:c}),s=t;if(u){var l=n||{},f=u.align,p=u.verticalAlign,h=u.layout;("vertical"===h||"horizontal"===h&&"middle"===p)&&E(t[f])&&(s=jl(jl({},t),{},El({},f,s[f]+(l.width||0)))),("horizontal"===h||"vertical"===h&&"center"===f)&&E(t[p])&&(s=jl(jl({},t),{},El({},p,s[p]+(l.height||0))))}return s}(v,0,r,e)),hg(hg({brushBottom:m},v),{},{width:c-v.left-v.right,height:u-v.top-v.bottom})},Mg=function(t){var n,o=t.chartName,a=t.GraphicalChild,c=t.defaultTooltipEventType,u=void 0===c?"axis":c,s=t.validateTooltipEventTypes,f=void 0===s?["axis"]:s,p=t.axisComponents,h=t.legendContent,y=t.formatAxisMap,m=t.defaultProps,b=function(t,e){var r=e.graphicalItems,n=e.stackGroups,i=e.offset,o=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,s=t.layout,l=t.barGap,f=t.barCategoryGap,h=t.maxBarSize,d=Pg(s),y=d.numericAxisName,m=d.cateAxisName,g=function(t){return!(!t||!t.length)&&t.some((function(t){var e=q(t&&t.type);return e&&e.indexOf("Bar")>=0}))}(r),b=g&&function(t){var e=t.barSize,r=t.stackGroups,n=void 0===r?{}:r;if(!n)return{};for(var i={},o=Object.keys(n),a=0,c=o.length;a<c;a++)for(var u=n[o[a]].stackGroups,s=Object.keys(u),l=0,f=s.length;l<f;l++){var p=u[s[l]],h=p.items,d=p.cateAxisId,y=h.filter((function(t){return q(t.type).indexOf("Bar")>=0}));if(y&&y.length){var m=y[0].props.barSize,g=y[0].props[d];i[g]||(i[g]=[]),i[g].push({item:y[0],stackList:y.slice(1),barSize:v()(m)?e:m})}}return i}({barSize:u,stackGroups:n}),x=[];return r.forEach((function(r,u){var d=wg(t.data,{dataStartIndex:a,dataEndIndex:c},r),g=r.props,w=g.dataKey,O=g.maxBarSize,S=r.props["".concat(y,"Id")],A=r.props["".concat(m,"Id")],j=p.reduce((function(t,n){var i,o=e["".concat(n.axisType,"Map")],a=r.props["".concat(n.axisType,"Id")],c=o&&o[a];return hg(hg({},t),{},(dg(i={},n.axisType,c),dg(i,"".concat(n.axisType,"Ticks"),Dl(c)),i))}),{}),E=j[m],P=j["".concat(m,"Ticks")],_=n&&n[S]&&n[S].hasStack&&function(t,e){var r=t.props.stackId;if(k(r)){var n=e[r];if(n&&n.items.length){for(var i=-1,o=0,a=n.items.length;o<a;o++)if(n.items[o]===t){i=o;break}return i>=0?n.stackedData[i]:null}}return null}(r,n[S].stackGroups),T=q(r.type).indexOf("Bar")>=0,C=Zl(E,P),I=[];if(T){var N,D,R=v()(O)?h:O,L=null!==(N=null!==(D=Zl(E,P,!0))&&void 0!==D?D:R)&&void 0!==N?N:0;I=function(t){var e=t.barGap,r=t.barCategoryGap,n=t.bandSize,i=t.sizeList,o=void 0===i?[]:i,a=t.maxBarSize,c=o.length;if(c<1)return null;var u,s=M(e,n,0,!0);if(o[0].barSize===+o[0].barSize){var l=!1,f=n/c,p=o.reduce((function(t,e){return t+e.barSize||0}),0);(p+=(c-1)*s)>=n&&(p-=(c-1)*s,s=0),p>=n&&f>0&&(l=!0,p=c*(f*=.9));var h={offset:((n-p)/2>>0)-s,size:0};u=o.reduce((function(t,e){var r=[].concat(Ol(t),[{item:e.item,position:{offset:h.offset+h.size+s,size:l?f:e.barSize}}]);return h=r[r.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach((function(t){r.push({item:t,position:h})})),r}),[])}else{var d=M(r,n,0,!0);n-2*d-(c-1)*s<=0&&(s=0);var y=(n-2*d-(c-1)*s)/c;y>1&&(y>>=0);var v=a===+a?Math.min(y,a):y;u=o.reduce((function(t,e,r){var n=[].concat(Ol(t),[{item:e.item,position:{offset:d+(y+s)*r+(y-v)/2,size:v}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach((function(t){n.push({item:t,position:n[n.length-1].position})})),n}),[])}return u}({barGap:l,barCategoryGap:f,bandSize:L!==C?L:C,sizeList:b[A],maxBarSize:R}),L!==C&&(I=I.map((function(t){return hg(hg({},t),{},{position:hg(hg({},t.position),{},{offset:t.position.offset-L/2})})})))}var B,z,F,U=r&&r.type&&r.type.getComposedData;U&&x.push({props:hg(hg({},U(hg(hg({},j),{},{displayedData:d,props:t,dataKey:w,item:r,bandSize:C,barPosition:I,offset:i,stackedData:_,layout:s,dataStartIndex:a,dataEndIndex:c}))),{},(B={key:r.key||"item-".concat(u)},dg(B,y,j[y]),dg(B,m,j[m]),dg(B,"animationId",o),B)),childIndex:(z=r,F=t.children,K(F).indexOf(z)),item:r})})),x},x=function(t,e){var r=t.props,n=t.dataStartIndex,i=t.dataEndIndex,c=t.updateId;if(!Z({props:r}))return null;var u=r.children,s=r.layout,l=r.stackOffset,f=r.data,h=r.reverseStackOrder,d=Pg(s),v=d.numericAxisName,m=d.cateAxisName,g=Y(u,a),x=function(t,e,r,n,i,o){if(!t)return null;var a=(o?e.reverse():e).reduce((function(t,e){var i=e.props,o=i.stackId;if(i.hide)return t;var a=e.props[r],c=t[a]||{hasStack:!1,stackGroups:{}};if(k(o)){var u=c.stackGroups[o]||{numericAxisId:r,cateAxisId:n,items:[]};u.items.push(e),c.hasStack=!0,c.stackGroups[o]=u}else c.stackGroups[_("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return jl(jl({},t),{},El({},a,c))}),{});return Object.keys(a).reduce((function(e,o){var c=a[o];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce((function(e,o){var a=c.stackGroups[o];return jl(jl({},e),{},El({},o,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:$l(t,a.items,i)}))}),{})),jl(jl({},e),{},El({},o,c))}),{})}(f,g,"".concat(v,"Id"),"".concat(m,"Id"),l,h),w=p.reduce((function(t,e){var o="".concat(e.axisType,"Map");return hg(hg({},t),{},dg({},o,Eg(r,hg(hg({},e),{},{graphicalItems:g,stackGroups:e.axisType===v&&x,dataStartIndex:n,dataEndIndex:i}))))}),{}),O=_g(hg(hg({},w),{},{props:r,graphicalItems:g}),null==e?void 0:e.legendBBox);Object.keys(w).forEach((function(t){w[t]=y(r,w[t],O,t.replace("Map",""),o)}));var S,A,j=w["".concat(m,"Map")],E=(S=T(j),{tooltipTicks:A=Dl(S,!1,!0),orderedTooltipTicks:en()(A,(function(t){return t.coordinate})),tooltipAxis:S,tooltipAxisBandSize:Zl(S,A)}),P=b(r,hg(hg({},w),{},{dataStartIndex:n,dataEndIndex:i,updateId:c,graphicalItems:g,stackGroups:x,offset:O}));return hg(hg({formattedGraphicalItems:P,graphicalItems:g,offset:O,stackGroups:x},E),w)};return n=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ig(t,e)}(p,t);var n,a,c,s=og(p);function p(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,p),dg(cg(r=s.call(this,t)),"accessibilityManager",new Ym),dg(cg(r),"clearDeferId",(function(){!v()(r.deferId)&&xg&&xg(r.deferId),r.deferId=null})),dg(cg(r),"handleLegendBBoxUpdate",(function(t){if(t){var e=r.state,n=e.dataStartIndex,i=e.dataEndIndex,o=e.updateId;r.setState(hg({legendBBox:t},x({props:r.props,dataStartIndex:n,dataEndIndex:i,updateId:o},hg(hg({},r.state),{},{legendBBox:t}))))}})),dg(cg(r),"handleReceiveSyncEvent",(function(t,e,n){r.props.syncId===t&&e!==r.uniqueChartId&&(r.clearDeferId(),r.deferId=bg&&bg(r.applySyncEvent.bind(cg(r),n)))})),dg(cg(r),"handleBrushChange",(function(t){var e=t.startIndex,n=t.endIndex;if(e!==r.state.dataStartIndex||n!==r.state.dataEndIndex){var i=r.state.updateId;r.setState((function(){return hg({dataStartIndex:e,dataEndIndex:n},x({props:r.props,dataStartIndex:e,dataEndIndex:n,updateId:i},r.state))})),r.triggerSyncEvent({dataStartIndex:e,dataEndIndex:n})}})),dg(cg(r),"handleMouseEnter",(function(t){var e=r.props.onMouseEnter,n=r.getMouseInfo(t);if(n){var i=hg(hg({},n),{},{isTooltipActive:!0});r.setState(i),r.triggerSyncEvent(i),l()(e)&&e(i,t)}})),dg(cg(r),"triggeredAfterMouseMove",(function(t){var e=r.props.onMouseMove,n=r.getMouseInfo(t),i=n?hg(hg({},n),{},{isTooltipActive:!0}):{isTooltipActive:!1};r.setState(i),r.triggerSyncEvent(i),l()(e)&&e(i,t)})),dg(cg(r),"handleItemMouseEnter",(function(t){r.setState((function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}}))})),dg(cg(r),"handleItemMouseLeave",(function(){r.setState((function(){return{isTooltipActive:!1}}))})),dg(cg(r),"handleMouseMove",(function(t){t&&l()(t.persist)&&t.persist(),r.triggeredAfterMouseMove(t)})),dg(cg(r),"handleMouseLeave",(function(t){var e=r.props.onMouseLeave,n={isTooltipActive:!1};r.setState(n),r.triggerSyncEvent(n),l()(e)&&e(n,t),r.cancelThrottledTriggerAfterMouseMove()})),dg(cg(r),"handleOuterEvent",(function(t){var e=function(t){var e=t&&t.type;return e&&H[e]?H[e]:null}(t),n=d()(r.props,"".concat(e));e&&l()(n)&&n(/.*touch.*/i.test(e)?r.getMouseInfo(t.changedTouches[0]):r.getMouseInfo(t),t)})),dg(cg(r),"handleClick",(function(t){var e=r.props.onClick,n=r.getMouseInfo(t);if(n){var i=hg(hg({},n),{},{isTooltipActive:!0});r.setState(i),r.triggerSyncEvent(i),l()(e)&&e(i,t)}})),dg(cg(r),"handleMouseDown",(function(t){var e=r.props.onMouseDown;l()(e)&&e(r.getMouseInfo(t),t)})),dg(cg(r),"handleMouseUp",(function(t){var e=r.props.onMouseUp;l()(e)&&e(r.getMouseInfo(t),t)})),dg(cg(r),"handleTouchMove",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&r.handleMouseMove(t.changedTouches[0])})),dg(cg(r),"handleTouchStart",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&r.handleMouseDown(t.changedTouches[0])})),dg(cg(r),"handleTouchEnd",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&r.handleMouseUp(t.changedTouches[0])})),dg(cg(r),"verticalCoordinatesGenerator",(function(t){var e=t.xAxis,r=t.width,n=t.height,i=t.offset;return Nl(cv(hg(hg(hg({},Sv.defaultProps),e),{},{ticks:Dl(e,!0),viewBox:{x:0,y:0,width:r,height:n}})),i.left,i.left+i.width)})),dg(cg(r),"horizontalCoordinatesGenerator",(function(t){var e=t.yAxis,r=t.width,n=t.height,i=t.offset;return Nl(cv(hg(hg(hg({},Sv.defaultProps),e),{},{ticks:Dl(e,!0),viewBox:{x:0,y:0,width:r,height:n}})),i.top,i.top+i.height)})),dg(cg(r),"axesTicksGenerator",(function(t){return Dl(t,!0)})),dg(cg(r),"renderCursor",(function(t){var n=r.state,i=n.isTooltipActive,a=n.activeCoordinate,c=n.activePayload,u=n.offset,s=n.activeTooltipIndex,l=r.getTooltipEventType();if(!t||!t.props.cursor||!i||!a||"ScatterChart"!==o&&"axis"!==l)return null;var f,p=r.props.layout,h=Pp;if("ScatterChart"===o)f=a,h=Gp;else if("BarChart"===o)f=r.getCursorRectangle(),h=Np;else if("radial"===p){var d=r.getCursorPoints(),y=d.cx,v=d.cy,m=d.radius;f={cx:y,cy:v,startAngle:d.startAngle,endAngle:d.endAngle,innerRadius:m,outerRadius:m},h=Xf}else f={points:r.getCursorPoints()},h=Pp;var g=t.key||"_recharts-cursor",b=hg(hg(hg(hg({stroke:"#ccc",pointerEvents:"none"},u),f),rt(t.props.cursor)),{},{payload:c,payloadIndex:s,key:g,className:"recharts-tooltip-cursor"});return(0,e.isValidElement)(t.props.cursor)?(0,e.cloneElement)(t.props.cursor,b):(0,e.createElement)(h,b)})),dg(cg(r),"renderPolarAxis",(function(t,n,i){var o=d()(t,"type.axisType"),a=d()(r.state,"".concat(o,"Map")),c=a&&a[t.props["".concat(o,"Id")]];return(0,e.cloneElement)(t,hg(hg({},c),{},{className:o,key:t.key||"".concat(n,"-").concat(i),ticks:Dl(c,!0)}))})),dg(cg(r),"renderXAxis",(function(t,e,n){var i=r.state.xAxisMap[t.props.xAxisId];return r.renderAxis(i,t,e,n)})),dg(cg(r),"renderYAxis",(function(t,e,n){var i=r.state.yAxisMap[t.props.yAxisId];return r.renderAxis(i,t,e,n)})),dg(cg(r),"renderGrid",(function(t){var n=r.state,i=n.xAxisMap,o=n.yAxisMap,a=n.offset,c=r.props,u=c.width,s=c.height,l=T(i),f=Lm()(o,(function(t){return iy()(t.domain,gg)}))||T(o),p=t.props||{};return(0,e.cloneElement)(t,{key:t.key||"grid",x:E(p.x)?p.x:a.left,y:E(p.y)?p.y:a.top,width:E(p.width)?p.width:a.width,height:E(p.height)?p.height:a.height,xAxis:l,yAxis:f,offset:a,chartWidth:u,chartHeight:s,verticalCoordinatesGenerator:p.verticalCoordinatesGenerator||r.verticalCoordinatesGenerator,horizontalCoordinatesGenerator:p.horizontalCoordinatesGenerator||r.horizontalCoordinatesGenerator})})),dg(cg(r),"renderPolarGrid",(function(t){var n=t.props,i=n.radialLines,o=n.polarAngles,a=n.polarRadius,c=r.state,u=c.radiusAxisMap,s=c.angleAxisMap,l=T(u),f=T(s),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,e.cloneElement)(t,{polarAngles:g()(o)?o:Dl(f,!0).map((function(t){return t.coordinate})),polarRadius:g()(a)?a:Dl(l,!0).map((function(t){return t.coordinate})),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:i})})),dg(cg(r),"renderLegend",(function(){var t=r.state.formattedGraphicalItems,n=r.props,i=n.children,o=n.width,a=n.height,c=r.props.margin||{},u=o-(c.left||0)-(c.right||0),s=Ml({children:i,formattedGraphicalItems:t,legendWidth:u,legendContent:h});if(!s)return null;var l=s.item,f=rg(s,Jm);return(0,e.cloneElement)(l,hg(hg({},f),{},{chartWidth:o,chartHeight:a,margin:c,ref:function(t){r.legendInstance=t},onBBoxUpdate:r.handleLegendBBoxUpdate}))})),dg(cg(r),"renderTooltip",(function(){var t=J(r.props.children,wn);if(!t)return null;var n=r.state,i=n.isTooltipActive,o=n.activeCoordinate,a=n.activePayload,c=n.activeLabel,u=n.offset;return(0,e.cloneElement)(t,{viewBox:hg(hg({},u),{},{x:u.left,y:u.top}),active:i,label:c,payload:i?a:[],coordinate:o})})),dg(cg(r),"renderBrush",(function(t){var n=r.props,i=n.margin,o=n.data,a=r.state,c=a.offset,u=a.dataStartIndex,s=a.dataEndIndex,l=a.updateId;return(0,e.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:Rl(r.handleBrushChange,null,t.props.onChange),data:o,x:E(t.props.x)?t.props.x:c.left,y:E(t.props.y)?t.props.y:c.top+c.height+c.brushBottom-(i.bottom||0),width:E(t.props.width)?t.props.width:c.width,startIndex:u,endIndex:s,updateId:"brush-".concat(l)})})),dg(cg(r),"renderReferenceElement",(function(t,n,i){if(!t)return null;var o=cg(r).clipPathId,a=r.state,c=a.xAxisMap,u=a.yAxisMap,s=a.offset,l=t.props,f=l.xAxisId,p=l.yAxisId;return(0,e.cloneElement)(t,{key:t.key||"".concat(n,"-").concat(i),xAxis:c[f],yAxis:u[p],viewBox:{x:s.left,y:s.top,width:s.width,height:s.height},clipPathId:o})})),dg(cg(r),"renderActivePoints",(function(t){var e=t.item,r=t.activePoint,n=t.basePoint,i=t.childIndex,o=t.isRange,a=[],c=e.props.key,u=e.item.props,s=u.activeDot,l=hg(hg({index:i,dataKey:u.dataKey,cx:r.x,cy:r.y,r:4,fill:_l(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value,key:"".concat(c,"-activePoint-").concat(i)},rt(s)),F(s));return a.push(p.renderActiveDot(s,l)),n?a.push(p.renderActiveDot(s,hg(hg({},l),{},{cx:n.x,cy:n.y,key:"".concat(c,"-basePoint-").concat(i)}))):o&&a.push(null),a})),dg(cg(r),"renderGraphicChild",(function(t,n,i){var o=r.filterFormatItem(t,n,i);if(!o)return null;var a=r.getTooltipEventType(),c=r.state,u=c.isTooltipActive,s=c.tooltipAxis,l=c.activeTooltipIndex,f=c.activeLabel,p=J(r.props.children,wn),h=o.props,d=h.points,y=h.isRange,m=h.baseLine,g=o.item.props,b=g.activeDot,x=!g.hide&&u&&p&&b&&l>=0,w={};"axis"!==a&&p&&"click"===p.props.trigger?w={onClick:Rl(r.handleItemMouseEnter,null,t.props.onCLick)}:"axis"!==a&&(w={onMouseLeave:Rl(r.handleItemMouseLeave,null,t.props.onMouseLeave),onMouseEnter:Rl(r.handleItemMouseEnter,null,t.props.onMouseEnter)});var O=(0,e.cloneElement)(t,hg(hg({},o.props),w));if(x){var S,A;if(s.dataKey&&!s.allowDuplicatedCategory){var j="function"==typeof s.dataKey?function(t){return"function"==typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());S=I(d,j,f),A=y&&m&&I(m,j,f)}else S=d[l],A=y&&m&&m[l];if(!v()(S))return[O].concat(sg(r.renderActivePoints({item:o,activePoint:S,basePoint:A,childIndex:l,isRange:y})))}return y?[O,null,null]:[O,null]})),dg(cg(r),"renderCustomized",(function(t,n,i){return(0,e.cloneElement)(t,hg(hg({key:"recharts-customized-".concat(i)},r.props),r.state))})),r.uniqueChartId=v()(t.id)?_("recharts"):t.id,r.clipPathId="".concat(r.uniqueChartId,"-clip"),t.throttleDelay&&(r.triggeredAfterMouseMove=En()(r.triggeredAfterMouseMove,t.throttleDelay)),r.state={},r}return n=p,a=[{key:"componentDidMount",value:function(){var t,e;v()(this.props.syncId)||this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.handleMouseMove,layout:this.props.layout})}},{key:"getSnapshotBeforeUpdate",value:function(t,e){return this.props.accessibilityLayer?(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin&&this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}}),null):null;var r,n}},{key:"componentDidUpdate",value:function(t){v()(t.syncId)&&!v()(this.props.syncId)&&this.addListener(),!v()(t.syncId)&&v()(this.props.syncId)&&this.removeListener()}},{key:"componentWillUnmount",value:function(){this.clearDeferId(),v()(this.props.syncId)||this.removeListener(),this.cancelThrottledTriggerAfterMouseMove()}},{key:"cancelThrottledTriggerAfterMouseMove",value:function(){"function"==typeof this.triggeredAfterMouseMove.cancel&&this.triggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=J(this.props.children,wn);if(t&&zm()(t.props.shared)){var e=t.props.shared?"axis":"item";return f.indexOf(e)>=0?e:u}return u}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e,r,n,i=function(t,e){return{chartX:Math.round(t.pageX-e.left),chartY:Math.round(t.pageY-e.top)}}(t,(e=this.container,r=e.ownerDocument.documentElement,n={top:0,left:0},void 0!==e.getBoundingClientRect&&(n=e.getBoundingClientRect()),{top:n.top+window.pageYOffset-r.clientTop,left:n.left+window.pageXOffset-r.clientLeft})),o=this.inRange(i.chartX,i.chartY);if(!o)return null;var a=this.state,c=a.xAxisMap,u=a.yAxisMap;if("axis"!==this.getTooltipEventType()&&c&&u){var s=T(c).scale,l=T(u).scale,f=s&&s.invert?s.invert(i.chartX):null,p=l&&l.invert?l.invert(i.chartY):null;return hg(hg({},i),{},{xValue:f,yValue:p})}var h=Ag(this.state,this.props.data,this.props.layout,o);return h?hg(hg({},i),h):null}},{key:"getCursorRectangle",value:function(){var t=this.props.layout,e=this.state,r=e.activeCoordinate,n=e.offset,i=e.tooltipAxisBandSize,o=i/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===t?r.x-o:n.left+.5,y:"horizontal"===t?n.top+.5:r.y-o,width:"horizontal"===t?i:n.width-1,height:"horizontal"===t?n.height-1:i}}},{key:"getCursorPoints",value:function(){var t,e,r,n,i=this.props.layout,o=this.state,a=o.activeCoordinate,c=o.offset;if("horizontal"===i)r=t=a.x,e=c.top,n=c.top+c.height;else if("vertical"===i)n=e=a.y,t=c.left,r=c.left+c.width;else if(!v()(a.cx)||!v()(a.cy)){if("centric"!==i){var u=a.cx,s=a.cy,l=a.radius,f=a.startAngle,p=a.endAngle;return{points:[lf(u,s,l,f),lf(u,s,l,p)],cx:u,cy:s,radius:l,startAngle:f,endAngle:p}}var h=a.cx,d=a.cy,y=a.innerRadius,m=a.outerRadius,g=a.angle,b=lf(h,d,y,g),x=lf(h,d,m,g);t=b.x,e=b.y,r=x.x,n=x.y}return[{x:t,y:e},{x:r,y:n}]}},{key:"inRange",value:function(t,e){var r=this.props.layout;if("horizontal"===r||"vertical"===r){var n=this.state.offset;return t>=n.left&&t<=n.left+n.width&&e>=n.top&&e<=n.top+n.height?{x:t,y:e}:null}var i=this.state,o=i.angleAxisMap,a=i.radiusAxisMap;if(o&&a){var c=T(o);return yf({x:t,y:e},c)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=J(t,wn),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd}),hg(hg({},F(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){Vm.on(Hm,this.handleReceiveSyncEvent),Vm.setMaxListeners&&Vm._maxListeners&&Vm.setMaxListeners(Vm._maxListeners+1)}},{key:"removeListener",value:function(){Vm.removeListener(Hm,this.handleReceiveSyncEvent),Vm.setMaxListeners&&Vm._maxListeners&&Vm.setMaxListeners(Vm._maxListeners-1)}},{key:"triggerSyncEvent",value:function(t){var e=this.props.syncId;v()(e)||Vm.emit(Hm,e,this.uniqueChartId,t)}},{key:"applySyncEvent",value:function(t){var e=this.props,r=e.layout,n=e.syncMethod,i=this.state.updateId,o=t.dataStartIndex,a=t.dataEndIndex;if(v()(t.dataStartIndex)&&v()(t.dataEndIndex))if(v()(t.activeTooltipIndex))this.setState(t);else{var c=t.chartX,u=t.chartY,s=t.activeTooltipIndex,l=this.state,f=l.offset,p=l.tooltipTicks;if(!f)return;if("function"==typeof n)s=n(p,t);else if("value"===n){s=-1;for(var h=0;h<p.length;h++)if(p[h].value===t.activeLabel){s=h;break}}var d=hg(hg({},f),{},{x:f.left,y:f.top}),y=Math.min(c,d.x+d.width),m=Math.min(u,d.y+d.height),g=p[s]&&p[s].value,b=Sg(this.state,this.props.data,s),w=p[s]?{x:"horizontal"===r?p[s].coordinate:y,y:"horizontal"===r?m:p[s].coordinate}:mg;this.setState(hg(hg({},t),{},{activeLabel:g,activeCoordinate:w,activePayload:b,activeTooltipIndex:s}))}else this.setState(hg({dataStartIndex:o,dataEndIndex:a},x({props:this.props,dataStartIndex:o,dataEndIndex:a,updateId:i},this.state)))}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,i=0,o=n.length;i<o;i++){var a=n[i];if(a.item===t||a.props.key===t.key||e===q(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderAxis",value:function(t,e,n,o){var a=this.props,c=a.width,u=a.height;return r().createElement(Sv,eg({},t,{className:i()("recharts-".concat(t.axisType," ").concat(t.axisType),t.className),key:e.key||"".concat(n,"-").concat(o),viewBox:{x:0,y:0,width:c,height:u},ticksGenerator:this.axesTicksGenerator}))}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,n=e.left,i=e.top,o=e.height,a=e.width;return r().createElement("defs",null,r().createElement("clipPath",{id:t},r().createElement("rect",{x:n,y:i,height:o,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce((function(t,e){var r=tg(e,2),n=r[0],i=r[1];return hg(hg({},t),{},dg({},n,i.scale))}),{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce((function(t,e){var r=tg(e,2),n=r[0],i=r[1];return hg(hg({},t),{},dg({},n,i.scale))}),{}):null}},{key:"getXScaleByAxisId",value:function(t){var e,r;return null===(e=this.state.xAxisMap)||void 0===e||null===(r=e[t])||void 0===r?void 0:r.scale}},{key:"getYScaleByAxisId",value:function(t){var e,r;return null===(e=this.state.yAxisMap)||void 0===e||null===(r=e[t])||void 0===r?void 0:r.scale}},{key:"getItemByXY",value:function(t){var e=this.state.formattedGraphicalItems;if(e&&e.length)for(var r=0,n=e.length;r<n;r++){var i=e[r],o=i.props,a=i.item,c=q(a.type);if("Bar"===c){var u=(o.data||[]).find((function(e){return Ip(t,e)}));if(u)return{graphicalItem:i,payload:u}}else if("RadialBar"===c){var s=(o.data||[]).find((function(e){return yf(t,e)}));if(s)return{graphicalItem:i,payload:s}}}return null}},{key:"render",value:function(){var t=this;if(!Z(this))return null;var e=this.props,n=e.children,o=e.className,a=e.width,c=e.height,u=e.style,s=e.compact,l=e.title,f=e.desc,p=rg(e,Zm),h=rt(p),d={CartesianGrid:{handler:this.renderGrid,once:!0},ReferenceArea:{handler:this.renderReferenceElement},ReferenceLine:{handler:this.renderReferenceElement},ReferenceDot:{handler:this.renderReferenceElement},XAxis:{handler:this.renderXAxis},YAxis:{handler:this.renderYAxis},Brush:{handler:this.renderBrush,once:!0},Bar:{handler:this.renderGraphicChild},Line:{handler:this.renderGraphicChild},Area:{handler:this.renderGraphicChild},Radar:{handler:this.renderGraphicChild},RadialBar:{handler:this.renderGraphicChild},Scatter:{handler:this.renderGraphicChild},Pie:{handler:this.renderGraphicChild},Funnel:{handler:this.renderGraphicChild},Tooltip:{handler:this.renderCursor,once:!0},PolarGrid:{handler:this.renderPolarGrid,once:!0},PolarAngleAxis:{handler:this.renderPolarAxis},PolarRadiusAxis:{handler:this.renderPolarAxis},Customized:{handler:this.renderCustomized}};if(s)return r().createElement(st,eg({},h,{width:a,height:c,title:l,desc:f}),this.renderClipPath(),ot(n,d));this.props.accessibilityLayer&&(h.tabIndex=0,h.role="img",h.onKeyDown=function(e){t.accessibilityManager.keyboardEvent(e)},h.onFocus=function(){t.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return r().createElement("div",eg({className:i()("recharts-wrapper",o),style:hg({position:"relative",cursor:"default",width:a,height:c},u)},y,{ref:function(e){t.container=e},role:"region"}),r().createElement(st,eg({},h,{width:a,height:c,title:l,desc:f}),this.renderClipPath(),ot(n,d)),this.renderLegend(),this.renderTooltip())}}],a&&ng(n.prototype,a),c&&ng(n,c),Object.defineProperty(n,"prototype",{writable:!1}),p}(e.Component),dg(n,"displayName",o),dg(n,"defaultProps",hg({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},m)),dg(n,"getDerivedStateFromProps",(function(t,e){var r=t.data,n=t.children,i=t.width,o=t.height,a=t.layout,c=t.stackOffset,u=t.margin;if(v()(e.updateId)){var s=kg(t);return hg(hg(hg({},s),{},{updateId:0},x(hg(hg({props:t},s),{},{updateId:0}),e)),{},{prevData:r,prevWidth:i,prevHeight:o,prevLayout:a,prevStackOffset:c,prevMargin:u,prevChildren:n})}if(r!==e.prevData||i!==e.prevWidth||o!==e.prevHeight||a!==e.prevLayout||c!==e.prevStackOffset||!N(u,e.prevMargin)){var l=kg(t),f={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},p=hg(hg({},Ag(e,r,a)),{},{updateId:e.updateId+1}),h=hg(hg(hg({},l),f),p);return hg(hg(hg({},h),x(hg({props:t},h),e)),{},{prevData:r,prevWidth:i,prevHeight:o,prevLayout:a,prevStackOffset:c,prevMargin:u,prevChildren:n})}if(!nt(n,e.prevChildren)){var d=!v()(r)?e.updateId:e.updateId+1;return hg(hg({updateId:d},x(hg(hg({props:t},e),{},{updateId:d}),e)),{},{prevChildren:n})}return null})),dg(n,"renderActiveDot",(function(t,n){var i;return i=(0,e.isValidElement)(t)?(0,e.cloneElement)(t,n):l()(t)?t(n):r().createElement(Vp,n),r().createElement(ht,{className:"recharts-active-dot",key:n.key},i)})),n},Tg=Mg({chartName:"LineChart",GraphicalChild:rm,axisComponents:[{axisType:"xAxis",AxisComp:Nm},{axisType:"yAxis",AxisComp:Dm}],formatAxisMap:_y}),Cg=Mg({chartName:"BarChart",GraphicalChild:Oy,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:Nm},{axisType:"yAxis",AxisComp:Dm}],formatAxisMap:_y}),Ig=Mg({chartName:"PieChart",GraphicalChild:td,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:zh},{axisType:"radiusAxis",AxisComp:Ah}],formatAxisMap:pf,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),Ng=o(7557),Dg=o.n(Ng),Rg=["#1890FF","#66B5FF","#41D9C7","#2FC25B","#6EDB8F","#9AE65C","#FACC14","#E6965C","#57AD71","#223273","#738AE6","#7564CC","#8543E0","#A877ED","#5C8EE6","#13C2C2","#70E0E0","#5CA3E6","#3436C7","#8082FF","#DD81E6","#F04864","#FA7D92","#D598D9"],Lg=["width","height","className","style","children","type"];function Bg(t){return Bg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bg(t)}function zg(){return zg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},zg.apply(this,arguments)}function Fg(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function Ug(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Wg(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Jg(n.key),n)}}function $g(t,e){return $g=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},$g(t,e)}function Vg(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Gg(t);if(e){var i=Gg(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return Hg(this,r)}}function Hg(t,e){if(e&&("object"===Bg(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return qg(t)}function qg(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Gg(t){return Gg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Gg(t)}function Xg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Kg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Xg(Object(r),!0).forEach((function(e){Yg(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Xg(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Yg(t,e,r){return(e=Jg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Jg(t){var e=function(t,e){if("object"!==Bg(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Bg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Bg(e)?e:String(e)}var Zg="value",Qg=function t(e){var r,n,i=e.depth,o=e.node,a=e.index,c=e.valueKey,u=o.children,s=i+1,l=u&&u.length?u.map((function(e,r){return t({depth:s,node:e,index:r,valueKey:c})})):null;return n=u&&u.length?l.reduce((function(t,e){return t+e[Zg]}),0):w()(o[c])||o[c]<=0?0:o[c],Kg(Kg({},o),{},(Yg(r={children:l},Zg,n),Yg(r,"depth",i),Yg(r,"index",a),r))},tb=function(t,e,r){var n=e*e,i=t.area*t.area,o=t.reduce((function(t,e){return{min:Math.min(t.min,e.area),max:Math.max(t.max,e.area)}}),{min:1/0,max:0}),a=o.min,c=o.max;return i?Math.max(n*c*r/i,i/(n*a*r)):1/0},eb=function(t,e,r,n){return e===r.width?function(t,e,r,n){var i=e?Math.round(t.area/e):0;(n||i>r.height)&&(i=r.height);for(var o,a=r.x,c=0,u=t.length;c<u;c++)(o=t[c]).x=a,o.y=r.y,o.height=i,o.width=Math.min(i?Math.round(o.area/i):0,r.x+r.width-a),a+=o.width;return o.width+=r.x+r.width-a,Kg(Kg({},r),{},{y:r.y+i,height:r.height-i})}(t,e,r,n):function(t,e,r,n){var i=e?Math.round(t.area/e):0;(n||i>r.width)&&(i=r.width);for(var o,a=r.y,c=0,u=t.length;c<u;c++)(o=t[c]).x=r.x,o.y=a,o.width=i,o.height=Math.min(i?Math.round(o.area/i):0,r.y+r.height-a),a+=o.height;return o&&(o.height+=r.y+r.height-a),Kg(Kg({},r),{},{x:r.x+i,width:r.width-i})}(t,e,r,n)},rb=function t(e,r){var n=e.children;if(n&&n.length){var i,o,a=function(t){return{x:t.x,y:t.y,width:t.width,height:t.height}}(e),c=[],u=1/0,s=Math.min(a.width,a.height),l=function(t,e){var r=e<0?0:e;return t.map((function(t){var e=t[Zg]*r;return Kg(Kg({},t),{},{area:w()(e)||e<=0?0:e})}))}(n,a.width*a.height/e[Zg]),f=l.slice();for(c.area=0;f.length>0;)c.push(i=f[0]),c.area+=i.area,(o=tb(c,s,r))<=u?(f.shift(),u=o):(c.area-=c.pop().area,a=eb(c,s,a,!1),s=Math.min(a.width,a.height),c.length=c.area=0,u=1/0);return c.length&&(a=eb(c,s,a,!0),c.length=c.area=0),Kg(Kg({},e),{},{children:l.map((function(e){return t(e,r)}))})}return e},nb={isTooltipActive:!1,isAnimationFinished:!1,activeNode:null,formatRoot:null,currentRoot:null,nestIndex:[]},ib=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&$g(t,e)}(c,t);var e,n,o,a=Vg(c);function c(){var t;Ug(this,c);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return Yg(qg(t=a.call.apply(a,[this].concat(r))),"state",Kg({},nb)),Yg(qg(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),l()(e)&&e()})),Yg(qg(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),l()(e)&&e()})),t}return e=c,o=[{key:"getDerivedStateFromProps",value:function(t,e){if(t.data!==e.prevData||t.type!==e.prevType||t.width!==e.prevWidth||t.height!==e.prevHeight||t.dataKey!==e.prevDataKey||t.aspectRatio!==e.prevAspectRatio){var r=Qg({depth:0,node:{children:t.data,x:0,y:0,width:t.width,height:t.height},index:0,valueKey:t.dataKey}),n=rb(r,t.aspectRatio);return Kg(Kg({},e),{},{formatRoot:n,currentRoot:r,nestIndex:[r],prevAspectRatio:t.aspectRatio,prevData:t.data,prevWidth:t.width,prevHeight:t.height,prevDataKey:t.dataKey,prevType:t.type})}return null}},{key:"renderContentItem",value:function(t,e,n,i){if(r().isValidElement(t))return r().cloneElement(t,e);if(l()(t))return t(e);var o=e.x,a=e.y,c=e.width,u=e.height,s=e.index,f=null;c>10&&u>10&&e.children&&"nest"===n&&(f=r().createElement(Wp,{points:[{x:o+2,y:a+u/2},{x:o+6,y:a+u/2+3},{x:o+2,y:a+u/2+6}]}));var p=null,h=ei(e.name);c>20&&u>20&&h.width<c&&h.height<u&&(p=r().createElement("text",{x:o+8,y:a+u/2+7,fontSize:14},e.name));var d=i||Rg;return r().createElement("g",null,r().createElement(Np,zg({fill:e.depth<2?d[s%d.length]:"rgba(255,255,255,0)",stroke:"#fff"},Dg()(e,"children"),{role:"img"})),f,p)}}],(n=[{key:"handleMouseEnter",value:function(t,e){e.persist();var r=this.props,n=r.onMouseEnter;J(r.children,wn)?this.setState({isTooltipActive:!0,activeNode:t},(function(){n&&n(t,e)})):n&&n(t,e)}},{key:"handleMouseLeave",value:function(t,e){e.persist();var r=this.props,n=r.onMouseLeave;J(r.children,wn)?this.setState({isTooltipActive:!1,activeNode:null},(function(){n&&n(t,e)})):n&&n(t,e)}},{key:"handleClick",value:function(t){var e=this.props,r=e.onClick;if("nest"===e.type&&t.children){var n=this.props,i=n.width,o=n.height,a=n.dataKey,c=n.aspectRatio,u=Qg({depth:0,node:Kg(Kg({},t),{},{x:0,y:0,width:i,height:o}),index:0,valueKey:a}),s=rb(u,c),l=this.state.nestIndex;l.push(t),this.setState({formatRoot:s,currentRoot:u,nestIndex:l})}r&&r(t)}},{key:"handleNestIndex",value:function(t,e){var r=this.state.nestIndex,n=this.props,i=n.width,o=n.height,a=n.dataKey,c=n.aspectRatio,u=Qg({depth:0,node:Kg(Kg({},t),{},{x:0,y:0,width:i,height:o}),index:0,valueKey:a}),s=rb(u,c);r=r.slice(0,e+1),this.setState({formatRoot:s,currentRoot:t,nestIndex:r})}},{key:"renderItem",value:function(t,e,n){var i=this,o=this.props,a=o.isAnimationActive,c=o.animationBegin,u=o.animationDuration,s=o.animationEasing,l=o.isUpdateAnimationActive,f=o.type,p=o.animationId,h=o.colorPanel,d=this.state.isAnimationFinished,y=e.width,v=e.height,m=e.x,g=e.y,b=e.depth,x=parseInt("".concat((2*Math.random()-1)*y),10),w={};return(n||"nest"===f)&&(w={onMouseEnter:this.handleMouseEnter.bind(this,e),onMouseLeave:this.handleMouseLeave.bind(this,e),onClick:this.handleClick.bind(this,e)}),a?r().createElement(Qr,{begin:c,duration:u,isActive:a,easing:s,key:"treemap-".concat(p),from:{x:m,y:g,width:y,height:v},to:{x:m,y:g,width:y,height:v},onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(n){var o=n.x,p=n.y,y=n.width,v=n.height;return r().createElement(Qr,{from:"translate(".concat(x,"px, ").concat(x,"px)"),to:"translate(0, 0)",attributeName:"transform",begin:c,easing:s,isActive:a,duration:u},r().createElement(ht,w,b>2&&!d?null:i.constructor.renderContentItem(t,Kg(Kg({},e),{},{isAnimationActive:a,isUpdateAnimationActive:!l,width:y,height:v,x:o,y:p}),f,h)))})):r().createElement(ht,w,this.constructor.renderContentItem(t,Kg(Kg({},e),{},{isAnimationActive:!1,isUpdateAnimationActive:!1,width:y,height:v,x:m,y:g}),f,h))}},{key:"renderNode",value:function(t,e,n){var i=this,o=this.props,a=o.content,c=o.type,u=Kg(Kg(Kg({},rt(this.props)),e),{},{root:t}),s=!e.children||!e.children.length;return!(this.state.currentRoot.children||[]).filter((function(t){return t.depth===e.depth&&t.name===e.name})).length&&t.depth&&"nest"===c?null:r().createElement(ht,{key:"recharts-treemap-node-".concat(n),className:"recharts-treemap-depth-".concat(e.depth)},this.renderItem(a,u,s),e.children&&e.children.length?e.children.map((function(t,r){return i.renderNode(e,t,r)})):null)}},{key:"renderAllNodes",value:function(){var t=this.state.formatRoot;return t?this.renderNode(t,t,0):null}},{key:"renderTooltip",value:function(){var t=this.props,e=t.children,n=t.nameKey,i=J(e,wn);if(!i)return null;var o=this.props,a=o.width,c=o.height,u=this.state,s=u.isTooltipActive,l=u.activeNode,f={x:0,y:0,width:a,height:c},p=l?{x:l.x+l.width/2,y:l.y+l.height/2}:null,h=s&&l?[{payload:l,name:kl(l,n,""),value:kl(l,Zg)}]:[];return r().cloneElement(i,{viewBox:f,active:s,coordinate:p,label:"",payload:h})}},{key:"renderNestIndex",value:function(){var t=this,e=this.props,n=e.nameKey,i=e.nestIndexContent,o=this.state.nestIndex;return r().createElement("div",{className:"recharts-treemap-nest-index-wrapper",style:{marginTop:"8px",textAlign:"center"}},o.map((function(e,o){var a=d()(e,n,"root"),c=null;return r().isValidElement(i)&&(c=r().cloneElement(i,e,o)),c=l()(i)?i(e,o):a,r().createElement("div",{onClick:t.handleNestIndex.bind(t,e,o),key:"nest-index-".concat(_()),className:"recharts-treemap-nest-index-box",style:{cursor:"pointer",display:"inline-block",padding:"0 7px",background:"#000",color:"#fff",marginRight:"3px"}},c)})))}},{key:"render",value:function(){if(!Z(this))return null;var t=this.props,e=t.width,n=t.height,o=t.className,a=t.style,c=t.children,u=t.type,s=Fg(t,Lg),l=rt(s);return r().createElement("div",{className:i()("recharts-wrapper",o),style:Kg(Kg({},a),{},{position:"relative",cursor:"default",width:e,height:n}),role:"region"},r().createElement(st,zg({},l,{width:e,height:"nest"===u?n-30:n}),this.renderAllNodes(),et(c)),this.renderTooltip(),"nest"===u&&this.renderNestIndex())}}])&&Wg(e.prototype,n),o&&Wg(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.PureComponent);Yg(ib,"displayName","Treemap"),Yg(ib,"defaultProps",{aspectRatio:.5*(1+Math.sqrt(5)),dataKey:"value",type:"flat",isAnimationActive:!fn.isSsr,isUpdateAnimationActive:!fn.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"linear"});var ob=o(3303),ab=o.n(ob),cb=["width","height","className","style","children"],ub=["sourceX","sourceY","sourceControlX","targetX","targetY","targetControlX","linkWidth"];function sb(t){return sb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},sb(t)}function lb(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function fb(){return fb=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},fb.apply(this,arguments)}function pb(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function hb(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ob(n.key),n)}}function db(t,e){return db=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},db(t,e)}function yb(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=gb(t);if(e){var i=gb(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return vb(this,r)}}function vb(t,e){if(e&&("object"===sb(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return mb(t)}function mb(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function gb(t){return gb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},gb(t)}function bb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function xb(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?bb(Object(r),!0).forEach((function(e){wb(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):bb(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function wb(t,e,r){return(e=Ob(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ob(t){var e=function(t,e){if("object"!==sb(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==sb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===sb(e)?e:String(e)}var Sb={x:0,y:0},Ab=function(t){return t.y+t.dy/2},jb=function(t){return t&&t.value||0},Eb=function(t,e){return e.reduce((function(e,r){return e+jb(t[r])}),0)},kb=function(t,e,r){return r.reduce((function(r,n){var i=e[n],o=t[i.source];return r+Ab(o)*jb(e[n])}),0)},Pb=function(t,e,r){return r.reduce((function(r,n){var i=e[n],o=t[i.target];return r+Ab(o)*jb(e[n])}),0)},_b=function(t,e){return t.y-e.y},Mb=function t(e,r){for(var n=r.targetNodes,i=0,o=n.length;i<o;i++){var a=e[n[i]];a&&(a.depth=Math.max(r.depth+1,a.depth),t(e,a))}},Tb=function(t,e,r){for(var n=0,i=t.length;n<i;n++){var o=t[n],a=o.length;o.sort(_b);for(var c=0,u=0;u<a;u++){var s=o[u],l=c-s.y;l>0&&(s.y+=l),c=s.y+s.dy+r}c=e+r;for(var f=a-1;f>=0;f--){var p=o[f],h=p.y+p.dy+r-c;if(!(h>0))break;p.y-=h,c=p.y}}},Cb=function(t,e,r,n){for(var i=0,o=e.length;i<o;i++)for(var a=e[i],c=0,u=a.length;c<u;c++){var s=a[c];if(s.sourceLinks.length){var l=Eb(r,s.sourceLinks),f=kb(t,r,s.sourceLinks)/l;s.y+=(f-Ab(s))*n}}},Ib=function(t,e,r,n){for(var i=e.length-1;i>=0;i--)for(var o=e[i],a=0,c=o.length;a<c;a++){var u=o[a];if(u.targetLinks.length){var s=Eb(r,u.targetLinks),l=Pb(t,r,u.targetLinks)/s;u.y+=(l-Ab(u))*n}}},Nb=function(t){var e=t.data,r=t.width,n=t.height,i=t.iterations,o=t.nodeWidth,a=t.nodePadding,c=e.links,u=function(t,e,r){for(var n=t.nodes,i=t.links,o=n.map((function(t,e){var r=function(t,e){for(var r=[],n=[],i=[],o=[],a=0,c=t.length;a<c;a++){var u=t[a];u.source===e&&(i.push(u.target),o.push(a)),u.target===e&&(r.push(u.source),n.push(a))}return{sourceNodes:r,sourceLinks:n,targetLinks:o,targetNodes:i}}(i,e);return xb(xb(xb({},t),r),{},{value:Math.max(Eb(i,r.sourceLinks),Eb(i,r.targetLinks)),depth:0})})),a=0,c=o.length;a<c;a++){var u=o[a];u.sourceNodes.length||Mb(o,u)}var s=uh()(o,(function(t){return t.depth})).depth;if(s>=1)for(var l=(e-r)/s,f=0,p=o.length;f<p;f++){var h=o[f];h.targetNodes.length||(h.depth=s),h.x=h.depth*l,h.dx=r}return{tree:o,maxDepth:s}}(e,r,o),s=u.tree,l=function(t){for(var e=[],r=0,n=t.length;r<n;r++){var i=t[r];e[i.depth]||(e[i.depth]=[]),e[i.depth].push(i)}return e}(s),f=function(t,e,r,n){for(var i=gi()(t.map((function(t){return(e-(t.length-1)*r)/ab()(t,jb)}))),o=0,a=t.length;o<a;o++)for(var c=0,u=t[o].length;c<u;c++){var s=t[o][c];s.y=c,s.dy=s.value*i}return n.map((function(t){return xb(xb({},t),{},{dy:jb(t)*i})}))}(l,n,a,c);Tb(l,n,a);for(var p=1,h=1;h<=i;h++)Ib(s,l,f,p*=.99),Tb(l,n,a),Cb(s,l,f,p),Tb(l,n,a);return function(t,e){for(var r=0,n=t.length;r<n;r++){var i=t[r],o=0,a=0;i.targetLinks.sort((function(r,n){return t[e[r].target].y-t[e[n].target].y})),i.sourceLinks.sort((function(r,n){return t[e[r].source].y-t[e[n].source].y}));for(var c=0,u=i.targetLinks.length;c<u;c++){var s=e[i.targetLinks[c]];s&&(s.sy=o,o+=s.dy)}for(var l=0,f=i.sourceLinks.length;l<f;l++){var p=e[i.sourceLinks[l]];p&&(p.ty=a,a+=p.dy)}}}(s,f),{nodes:s,links:f}},Db=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&db(t,e)}(c,t);var e,n,o,a=yb(c);function c(){var t;pb(this,c);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return wb(mb(t=a.call.apply(a,[this].concat(r))),"state",{activeElement:null,activeElementType:null,isTooltipActive:!1,nodes:[],links:[]}),t}return e=c,n=[{key:"handleMouseEnter",value:function(t,e,r){var n=this.props,i=n.onMouseEnter,o=J(n.children,wn);o?this.setState((function(r){return"hover"===o.props.trigger?xb(xb({},r),{},{activeElement:t,activeElementType:e,isTooltipActive:!0}):r}),(function(){i&&i(t,e,r)})):i&&i(t,e,r)}},{key:"handleMouseLeave",value:function(t,e,r){var n=this.props,i=n.onMouseLeave,o=J(n.children,wn);o?this.setState((function(t){return"hover"===o.props.trigger?xb(xb({},t),{},{activeElement:void 0,activeElementType:void 0,isTooltipActive:!1}):t}),(function(){i&&i(t,e,r)})):i&&i(t,e,r)}},{key:"handleClick",value:function(t,e,r){var n=this.props,i=n.onClick,o=J(n.children,wn);o&&"click"===o.props.trigger&&(this.state.isTooltipActive?this.setState((function(t){return xb(xb({},t),{},{activeElement:void 0,activeElementType:void 0,isTooltipActive:!1})})):this.setState((function(r){return xb(xb({},r),{},{activeElement:t,activeElementType:e,isTooltipActive:!0})}))),i&&i(t,e,r)}},{key:"renderLinks",value:function(t,e){var n=this,i=this.props,o=i.linkCurvature,a=i.link,c=i.margin,u=d()(c,"top")||0,s=d()(c,"left")||0;return r().createElement(ht,{className:"recharts-sankey-links",key:"recharts-sankey-links"},t.map((function(t,i){var c=t.sy,l=t.ty,f=t.dy,p=e[t.source],h=e[t.target],d=p.x+p.dx+s,y=h.x+s,v=function(t,e){var r=+t,n=e-r;return function(t){return r+n*t}}(d,y),m=v(o),g=v(1-o),b=xb({sourceX:d,targetX:y,sourceY:p.y+c+f/2+u,targetY:h.y+l+f/2+u,sourceControlX:m,targetControlX:g,sourceRelativeY:c,targetRelativeY:l,linkWidth:f,index:i,payload:xb(xb({},t),{},{source:p,target:h})},rt(a)),x={onMouseEnter:n.handleMouseEnter.bind(n,b,"link"),onMouseLeave:n.handleMouseLeave.bind(n,b,"link"),onClick:n.handleClick.bind(n,b,"link")};return r().createElement(ht,fb({key:"link".concat(i)},x),n.constructor.renderLinkItem(a,b))})))}},{key:"renderNodes",value:function(t){var e=this,n=this.props,i=n.node,o=n.margin,a=d()(o,"top")||0,c=d()(o,"left")||0;return r().createElement(ht,{className:"recharts-sankey-nodes",key:"recharts-sankey-nodes"},t.map((function(t,n){var o=t.x,u=t.y,s=t.dx,l=t.dy,f=xb(xb({},rt(i)),{},{x:o+c,y:u+a,width:s,height:l,index:n,payload:t}),p={onMouseEnter:e.handleMouseEnter.bind(e,f,"node"),onMouseLeave:e.handleMouseLeave.bind(e,f,"node"),onClick:e.handleClick.bind(e,f,"node")};return r().createElement(ht,fb({key:"node".concat(n)},p),e.constructor.renderNodeItem(i,f))})))}},{key:"renderTooltip",value:function(){var t=this.props,e=t.children,n=t.width,i=t.height,o=t.nameKey,a=J(e,wn);if(!a)return null;var c,u=this.state,s=u.isTooltipActive,l=u.activeElement,f=u.activeElementType,p={x:0,y:0,width:n,height:i},h=l?(c=l,"node"===f?{x:c.x+c.width/2,y:c.y+c.height/2}:{x:(c.sourceX+c.targetX)/2,y:(c.sourceY+c.targetY)/2}):Sb,d=l?function(t,e,r){var n=t.payload;if("node"===e)return[{payload:t,name:kl(n,r,""),value:kl(n,"value")}];if(n.source&&n.target){var i=kl(n.source,r,""),o=kl(n.target,r,"");return[{payload:t,name:"".concat(i," - ").concat(o),value:kl(n,"value")}]}return[]}(l,f,o):[];return r().cloneElement(a,{viewBox:p,active:s,coordinate:h,label:"",payload:d})}},{key:"render",value:function(){if(!Z(this))return null;var t=this.props,e=t.width,n=t.height,o=t.className,a=t.style,c=t.children,u=lb(t,cb),s=this.state,l=s.links,f=s.nodes,p=rt(u);return r().createElement("div",{className:i()("recharts-wrapper",o),style:xb(xb({},a),{},{position:"relative",cursor:"default",width:e,height:n}),role:"region"},r().createElement(st,fb({},p,{width:e,height:n}),et(c),this.renderLinks(l,f),this.renderNodes(f)),this.renderTooltip())}}],o=[{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.height,o=t.margin,a=t.iterations,c=t.nodeWidth,u=t.nodePadding;if(r!==e.prevData||n!==e.prevWidth||i!==e.prevHeight||!N(o,e.prevMargin)||a!==e.prevIterations||c!==e.prevNodeWidth||u!==e.prevNodePadding){var s=n-(o&&o.left||0)-(o&&o.right||0),l=i-(o&&o.top||0)-(o&&o.bottom||0),f=Nb({data:r,width:s,height:l,iterations:a,nodeWidth:c,nodePadding:u}),p=f.links,h=f.nodes;return xb(xb({},e),{},{nodes:h,links:p,prevData:r,prevWidth:a,prevHeight:i,prevMargin:o,prevNodePadding:u,prevNodeWidth:c,prevIterations:a})}return null}},{key:"renderLinkItem",value:function(t,e){if(r().isValidElement(t))return r().cloneElement(t,e);if(l()(t))return t(e);var n=e.sourceX,i=e.sourceY,o=e.sourceControlX,a=e.targetX,c=e.targetY,u=e.targetControlX,s=e.linkWidth,f=lb(e,ub);return r().createElement("path",fb({className:"recharts-sankey-link",d:"\n          M".concat(n,",").concat(i,"\n          C").concat(o,",").concat(i," ").concat(u,",").concat(c," ").concat(a,",").concat(c,"\n        "),fill:"none",stroke:"#333",strokeWidth:s,strokeOpacity:"0.2"},rt(f)))}},{key:"renderNodeItem",value:function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):r().createElement(Np,fb({className:"recharts-sankey-node",fill:"#0088fe",fillOpacity:"0.8"},rt(e),{role:"img"}))}}],n&&hb(e.prototype,n),o&&hb(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.PureComponent);wb(Db,"displayName","Sankey"),wb(Db,"defaultProps",{nameKey:"name",dataKey:"value",nodePadding:10,nodeWidth:10,linkCurvature:.5,iterations:32,margin:{top:5,right:5,bottom:5,left:5}});var Rb=Mg({chartName:"RadarChart",GraphicalChild:vd,axisComponents:[{axisType:"angleAxis",AxisComp:zh},{axisType:"radiusAxis",AxisComp:Ah}],formatAxisMap:pf,defaultProps:{layout:"centric",startAngle:90,endAngle:-270,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),Lb=Mg({chartName:"ScatterChart",GraphicalChild:Im,defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],axisComponents:[{axisType:"xAxis",AxisComp:Nm},{axisType:"yAxis",AxisComp:Dm},{axisType:"zAxis",AxisComp:bm}],formatAxisMap:_y}),Bb=Mg({chartName:"AreaChart",GraphicalChild:gm,axisComponents:[{axisType:"xAxis",AxisComp:Nm},{axisType:"yAxis",AxisComp:Dm}],formatAxisMap:_y}),zb=Mg({chartName:"RadialBarChart",GraphicalChild:Cd,legendContent:"children",defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"angleAxis",AxisComp:zh},{axisType:"radiusAxis",AxisComp:Ah}],formatAxisMap:pf,defaultProps:{layout:"radial",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),Fb=Mg({chartName:"ComposedChart",GraphicalChild:[rm,gm,Oy,Im],axisComponents:[{axisType:"xAxis",AxisComp:Nm},{axisType:"yAxis",AxisComp:Dm},{axisType:"zAxis",AxisComp:bm}],formatAxisMap:_y});function Ub(){return Ub=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ub.apply(this,arguments)}function Wb(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return $b(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $b(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Vb=function(t,e,r,n,i){var o,a=r-n;return o="M ".concat(t,",").concat(e),o+="L ".concat(t+r,",").concat(e),o+="L ".concat(t+r-a/2,",").concat(e+i),o+="L ".concat(t+r-a/2-n,",").concat(e+i),o+="L ".concat(t,",").concat(e," Z")},Hb=function(t){var n=(0,e.useRef)(),o=Wb((0,e.useState)(-1),2),a=o[0],c=o[1];(0,e.useLayoutEffect)((function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&c(t)}catch(t){}}),[]);var u=t.x,s=t.y,l=t.upperWidth,f=t.lowerWidth,p=t.height,h=t.className,d=t.animationEasing,y=t.animationDuration,v=t.animationBegin,m=t.isUpdateAnimationActive;if(u!==+u||s!==+s||l!==+l||f!==+f||p!==+p||0===l&&0===f||0===p)return null;var g=i()("recharts-trapezoid",h);return m?r().createElement(Qr,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:u,y:s},to:{upperWidth:l,lowerWidth:f,height:p,x:u,y:s},duration:y,animationEasing:d,isActive:m},(function(e){var i=e.upperWidth,o=e.lowerWidth,c=e.height,u=e.x,s=e.y;return r().createElement(Qr,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:y,easing:d},r().createElement("path",Ub({},rt(t,!0),{className:g,d:Vb(u,s,i,o,c),ref:n})))})):r().createElement("g",null,r().createElement("path",Ub({},rt(t,!0),{className:g,d:Vb(u,s,l,f,p)})))};function qb(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Gb(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gb(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Gb(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Xb(t){return Xb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xb(t)}function Kb(){return Kb=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Kb.apply(this,arguments)}function Yb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Jb(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Yb(Object(r),!0).forEach((function(e){ox(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Yb(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Zb(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Qb(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ax(n.key),n)}}function tx(t,e){return tx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},tx(t,e)}function ex(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=ix(t);if(e){var i=ix(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return rx(this,r)}}function rx(t,e){if(e&&("object"===Xb(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return nx(t)}function nx(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ix(t){return ix=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ix(t)}function ox(t,e,r){return(e=ax(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ax(t){var e=function(t,e){if("object"!==Xb(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Xb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Xb(e)?e:String(e)}Hb.defaultProps={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"};var cx=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tx(t,e)}(c,t);var e,n,o,a=ex(c);function c(){var t;Zb(this,c);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return ox(nx(t=a.call.apply(a,[this].concat(r))),"state",{isAnimationFinished:!1}),ox(nx(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),l()(e)&&e()})),ox(nx(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),l()(e)&&e()})),t}return e=c,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curTrapezoids:t.trapezoids,prevTrapezoids:e.curTrapezoids}:t.trapezoids!==e.curTrapezoids?{curTrapezoids:t.trapezoids}:null}},{key:"renderTrapezoidItem",value:function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):l()(t)?t(e):Uh()(t)?r().createElement(Hb,Kb({},e,t)):r().createElement(Hb,e)}}],(n=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"renderTrapezoidsStatically",value:function(t){var e=this,n=this.props.activeShape;return t.map((function(t,i){var o=e.isActiveIndex(i)?n:null,a=Jb(Jb({},t),{},{stroke:t.stroke});return r().createElement(ht,Kb({className:"recharts-funnel-trapezoid"},U(e.props,t,i),{key:"trapezoid-".concat(i),role:"img"}),c.renderTrapezoidItem(o,a))}))}},{key:"renderTrapezoidsWithAnimation",value:function(){var t=this,e=this.props,n=e.trapezoids,i=e.isAnimationActive,o=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,s=this.state.prevTrapezoids;return r().createElement(Qr,{begin:o,duration:a,isActive:i,easing:c,from:{t:0},to:{t:1},key:"funnel-".concat(u),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var i=e.t,o=n.map((function(t,e){var r=s&&s[e];if(r){var n=C(r.x,t.x),o=C(r.y,t.y),a=C(r.upperWidth,t.upperWidth),c=C(r.lowerWidth,t.lowerWidth),u=C(r.height,t.height);return Jb(Jb({},t),{},{x:n(i),y:o(i),upperWidth:a(i),lowerWidth:c(i),height:u(i)})}var l=C(t.x+t.upperWidth/2,t.x),f=C(t.y+t.height/2,t.y),p=C(0,t.upperWidth),h=C(0,t.lowerWidth),d=C(0,t.height);return Jb(Jb({},t),{},{x:l(i),y:f(i),upperWidth:p(i),lowerWidth:h(i),height:d(i)})}));return r().createElement(ht,null,t.renderTrapezoidsStatically(o))}))}},{key:"renderTrapezoids",value:function(){var t=this.props,e=t.trapezoids,r=t.isAnimationActive,n=this.state.prevTrapezoids;return!(r&&e&&e.length)||n&&di()(n,e)?this.renderTrapezoidsStatically(e):this.renderTrapezoidsWithAnimation()}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.trapezoids,o=t.className,a=t.isAnimationActive,c=this.state.isAnimationFinished;if(e||!n||!n.length)return null;var u=i()("recharts-trapezoids",o);return r().createElement(ht,{className:u},this.renderTrapezoids(),(!a||c)&&zf.renderCallByParent(this.props,n))}}])&&Qb(e.prototype,n),o&&Qb(e,o),Object.defineProperty(e,"prototype",{writable:!1}),c}(e.PureComponent);ox(cx,"displayName","Funnel"),ox(cx,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",labelLine:!0,hide:!1,isAnimationActive:!fn.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",lastShapeType:"triangle"}),ox(cx,"getRealFunnelData",(function(t){var e=t.props,r=e.data,n=e.children,i=rt(t.props),o=Y(n,Un);return r&&r.length?r.map((function(t,e){return Jb(Jb(Jb({payload:t},i),t),o&&o[e]&&o[e].props)})):o&&o.length?o.map((function(t){return Jb(Jb({},i),t.props)})):[]})),ox(cx,"getRealWidthHeight",(function(t,e){var r=t.props.width,n=e.width,i=e.height,o=e.left,a=e.right,c=e.top,u=e.bottom,s=i,l=n;return S()(r)?l=r:p()(r)&&(l=l*parseFloat(r)/100),{realWidth:l-o-a-50,realHeight:s-u-c,offsetX:(n-l)/2,offsetY:(i-s)/2}})),ox(cx,"getComposedData",(function(t){var e=t.item,r=t.offset,n=cx.getRealFunnelData(e),i=e.props,o=i.dataKey,a=i.nameKey,c=i.tooltipType,u=i.lastShapeType,s=i.reversed,l=r.left,f=r.top,p=cx.getRealWidthHeight(e,r),h=p.realHeight,d=p.realWidth,y=p.offsetX,v=p.offsetY,m=Math.max.apply(null,n.map((function(t){return kl(t,o,0)}))),g=n.length,b=h/g,x={x:r.left,y:r.top,width:r.width,height:r.height},w=n.map((function(t,e){var r,i=kl(t,o,0),s=kl(t,a,e),p=i;if(e!==g-1)(r=kl(n[e+1],o,0))instanceof Array&&(r=qb(r,1)[0]);else if(i instanceof Array&&2===i.length){var h=qb(i,2);p=h[0],r=h[1]}else r="rectangle"===u?p:0;var w=(m-p)*d/(2*m)+f+25+y,O=b*e+l+v,S=p/m*d,A=r/m*d,j=[{name:s,value:p,payload:t,dataKey:o,type:c}],E={x:w+S/2,y:O+b/2};return Jb(Jb({x:w,y:O,width:Math.max(S,A),upperWidth:S,lowerWidth:A,height:b,name:s,val:p,tooltipPayload:j,tooltipPosition:E},Dg()(t,"width")),{},{payload:t,parentViewBox:x,labelViewBox:{x:w+(S-A)/4,y:O,width:Math.abs(S-A)/2+Math.min(S,A),height:b}})}));return s&&(w=w.map((function(t,e){var r=t.y-e*b+(g-1-e)*b;return Jb(Jb({},t),{},{upperWidth:t.lowerWidth,lowerWidth:t.upperWidth,x:t.x-(t.lowerWidth-t.upperWidth)/2,y:t.y-e*b+(g-1-e)*b,tooltipPosition:Jb(Jb({},t.tooltipPosition),{},{y:r+b/2}),labelViewBox:Jb(Jb({},t.labelViewBox),{},{y:r})})}))),{trapezoids:w,data:n}}));var ux=Mg({chartName:"FunnelChart",GraphicalChild:cx,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",axisComponents:[],defaultProps:{layout:"centric"}})})(),a})()));
//# sourceMappingURL=Recharts.js.map