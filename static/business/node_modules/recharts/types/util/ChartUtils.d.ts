import { ReactElement, ReactNode } from 'react';
import { AxisType, BaseAxisProps, DataKey, LayoutType, PolarLayoutType, TickItem } from './types';
export declare function getValueByDataKey<T>(obj: T, dataKey: DataKey<any>, defaultValue?: any): any;
export declare function getDomainOfDataByKey<T>(data: Array<T>, key: string, type: string, filterNil?: boolean): any[];
export declare const calculateActiveTickIndex: (coordinate: number, ticks?: Array<TickItem>, unsortedTicks?: Array<TickItem>, axis?: BaseAxisProps) => number;
export declare const getMainColorOfGraphicItem: (item: ReactElement) => any;
interface FormattedGraphicalItem {
    props: any;
    childIndex: number;
    item: any;
}
export declare const getLegendProps: ({ children, formattedGraphicalItems, legendWidth, legendContent, }: {
    children: any;
    formattedGraphicalItems?: Array<FormattedGraphicalItem>;
    legendWidth: number;
    legendContent?: any;
}) => {
    payload: any[];
    item: import("react").DetailedReactHTMLElement<import("../component/Legend").Props, HTMLElement>;
    height: any;
    width?: number;
    content?: import("../component/DefaultLegendContent").ContentType;
    iconSize?: number;
    iconType?: "circle" | "cross" | "diamond" | "square" | "star" | "triangle" | "wye" | "plainline" | "line" | "rect";
    layout?: LayoutType;
    align?: import("../component/DefaultLegendContent").HorizontalAlignmentType;
    verticalAlign?: import("../component/DefaultLegendContent").VerticalAlignmentType;
    inactiveColor?: string;
    formatter?: import("../component/DefaultLegendContent").Formatter;
    onMouseEnter?: ((data: import("../component/DefaultLegendContent").Payload & {
        dataKey?: DataKey<any>;
    }, index: number, event: import("react").MouseEvent<Element, MouseEvent>) => void) & ((data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void);
    onMouseLeave?: ((data: import("../component/DefaultLegendContent").Payload & {
        dataKey?: DataKey<any>;
    }, index: number, event: import("react").MouseEvent<Element, MouseEvent>) => void) & ((data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void);
    onClick?: ((data: import("../component/DefaultLegendContent").Payload & {
        dataKey?: DataKey<any>;
    }, index: number, event: import("react").MouseEvent<Element, MouseEvent>) => void) & ((data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void);
    'aria-activedescendant'?: string;
    'aria-atomic'?: boolean | "true" | "false";
    'aria-autocomplete'?: "none" | "list" | "inline" | "both";
    'aria-busy'?: boolean | "true" | "false";
    'aria-checked'?: boolean | "true" | "false" | "mixed";
    'aria-colcount'?: number;
    'aria-colindex'?: number;
    'aria-colspan'?: number;
    'aria-controls'?: string;
    'aria-current'?: boolean | "time" | "true" | "false" | "page" | "step" | "location" | "date";
    'aria-describedby'?: string;
    'aria-details'?: string;
    'aria-disabled'?: boolean | "true" | "false";
    'aria-dropeffect'?: "none" | "link" | "copy" | "execute" | "move" | "popup";
    'aria-errormessage'?: string;
    'aria-expanded'?: boolean | "true" | "false";
    'aria-flowto'?: string;
    'aria-grabbed'?: boolean | "true" | "false";
    'aria-haspopup'?: boolean | "dialog" | "menu" | "grid" | "listbox" | "tree" | "true" | "false";
    'aria-hidden'?: boolean | "true" | "false";
    'aria-invalid'?: boolean | "true" | "false" | "grammar" | "spelling";
    'aria-keyshortcuts'?: string;
    'aria-label'?: string;
    'aria-labelledby'?: string;
    'aria-level'?: number;
    'aria-live'?: "off" | "assertive" | "polite";
    'aria-modal'?: boolean | "true" | "false";
    'aria-multiline'?: boolean | "true" | "false";
    'aria-multiselectable'?: boolean | "true" | "false";
    'aria-orientation'?: "horizontal" | "vertical";
    'aria-owns'?: string;
    'aria-placeholder'?: string;
    'aria-posinset'?: number;
    'aria-pressed'?: boolean | "true" | "false" | "mixed";
    'aria-readonly'?: boolean | "true" | "false";
    'aria-relevant'?: "text" | "additions" | "additions removals" | "additions text" | "all" | "removals" | "removals additions" | "removals text" | "text additions" | "text removals";
    'aria-required'?: boolean | "true" | "false";
    'aria-roledescription'?: string;
    'aria-rowcount'?: number;
    'aria-rowindex'?: number;
    'aria-rowspan'?: number;
    'aria-selected'?: boolean | "true" | "false";
    'aria-setsize'?: number;
    'aria-sort'?: "none" | "ascending" | "descending" | "other";
    'aria-valuemax'?: number;
    'aria-valuemin'?: number;
    'aria-valuenow'?: number;
    'aria-valuetext'?: string;
    children?: ReactNode;
    dangerouslySetInnerHTML?: {
        __html: string;
    };
    onCopy?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCopyCapture?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCut?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCutCapture?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPaste?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPasteCapture?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionEnd?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionEndCapture?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionStart?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionStartCapture?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionUpdate?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionUpdateCapture?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onFocus?: (data: any, index: number, event: import("react").FocusEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Element>) => void;
    onFocusCapture?: (data: any, index: number, event: import("react").FocusEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Element>) => void;
    onBlur?: (data: any, index: number, event: import("react").FocusEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Element>) => void;
    onBlurCapture?: (data: any, index: number, event: import("react").FocusEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Element>) => void;
    onChange?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onChangeCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onBeforeInput?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onBeforeInputCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onInput?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onInputCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onReset?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onResetCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onSubmit?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onSubmitCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onInvalid?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onInvalidCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onLoad?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onError?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onErrorCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onKeyDown?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onKeyDownCapture?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onKeyPress?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onKeyPressCapture?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onKeyUp?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onKeyUpCapture?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAbort?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onAbortCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onCanPlay?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onCanPlayCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onCanPlayThrough?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onCanPlayThroughCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onDurationChange?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onDurationChangeCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEmptied?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEmptiedCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEncrypted?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEncryptedCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEnded?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEndedCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadedData?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadedDataCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadedMetadata?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadedMetadataCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadStart?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadStartCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPause?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPauseCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPlay?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPlayCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPlaying?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPlayingCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onProgress?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onProgressCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onRateChange?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onRateChangeCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSeeked?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSeekedCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSeeking?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSeekingCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onStalled?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onStalledCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSuspend?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSuspendCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onTimeUpdate?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onTimeUpdateCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onVolumeChange?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onVolumeChangeCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onWaiting?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onWaitingCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onAuxClick?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onAuxClickCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onClickCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onContextMenu?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onContextMenuCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onDoubleClick?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onDoubleClickCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onDrag?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragEnd?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragEndCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragEnter?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragEnterCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragExit?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragExitCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragLeave?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragLeaveCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragOver?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragOverCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragStart?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragStartCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDrop?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDropCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onMouseDown?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseDownCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseMove?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseMoveCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseOut?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseOutCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseOver?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseOverCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseUp?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseUpCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onSelect?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSelectCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onTouchCancel?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchCancelCapture?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchEnd?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchEndCapture?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchMove?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchMoveCapture?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchStart?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchStartCapture?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerDown?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerDownCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerMove?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerMoveCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerUp?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerUpCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerCancel?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerCancelCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerEnter?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerEnterCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerLeave?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerLeaveCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerOver?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerOverCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerOut?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerOutCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onGotPointerCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onGotPointerCaptureCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onLostPointerCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onLostPointerCaptureCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onScroll?: (data: any, index: number, event: import("react").UIEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, UIEvent>) => void;
    onScrollCapture?: (data: any, index: number, event: import("react").UIEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, UIEvent>) => void;
    onWheel?: (data: any, index: number, event: import("react").WheelEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onWheelCapture?: (data: any, index: number, event: import("react").WheelEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationStart?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationStartCapture?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationEnd?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationEndCapture?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationIteration?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationIterationCapture?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTransitionEnd?: (data: any, index: number, event: import("react").TransitionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTransitionEndCapture?: (data: any, index: number, event: import("react").TransitionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    string?: string | number;
    className?: string;
    color?: string;
    id?: string;
    lang?: string;
    max?: string | number;
    media?: string;
    method?: string;
    min?: string | number;
    name?: string;
    style?: import("react").CSSProperties;
    target?: string;
    type?: string;
    role?: import("react").AriaRole;
    tabIndex?: number;
    crossOrigin?: "" | "anonymous" | "use-credentials";
    accentHeight?: string | number;
    accumulate?: "none" | "sum";
    additive?: "replace" | "sum";
    alignmentBaseline?: "alphabetic" | "hanging" | "ideographic" | "mathematical" | "auto" | "baseline" | "before-edge" | "text-before-edge" | "middle" | "central" | "after-edge" | "text-after-edge" | "inherit";
    allowReorder?: "no" | "yes";
    alphabetic?: string | number;
    amplitude?: string | number;
    arabicForm?: "initial" | "medial" | "terminal" | "isolated";
    ascent?: string | number;
    attributeName?: string;
    attributeType?: string;
    autoReverse?: boolean | "true" | "false";
    azimuth?: string | number;
    baseFrequency?: string | number;
    baselineShift?: string | number;
    baseProfile?: string | number;
    bbox?: string | number;
    begin?: string | number;
    bias?: string | number;
    by?: string | number;
    calcMode?: string | number;
    capHeight?: string | number;
    clip?: string | number;
    clipPath?: string;
    clipPathUnits?: string | number;
    clipRule?: string | number;
    colorInterpolation?: string | number;
    colorInterpolationFilters?: "auto" | "inherit" | "sRGB" | "linearRGB";
    colorProfile?: string | number;
    colorRendering?: string | number;
    contentScriptType?: string | number;
    contentStyleType?: string | number;
    cursor?: string | number;
    cx?: string | number;
    cy?: string | number;
    d?: string;
    decelerate?: string | number;
    descent?: string | number;
    diffuseConstant?: string | number;
    direction?: string | number;
    display?: string | number;
    divisor?: string | number;
    dominantBaseline?: string | number;
    dur?: string | number;
    dx?: string | number;
    dy?: string | number;
    edgeMode?: string | number;
    elevation?: string | number;
    enableBackground?: string | number;
    end?: string | number;
    exponent?: string | number;
    externalResourcesRequired?: boolean | "true" | "false";
    fill?: string;
    fillOpacity?: string | number;
    fillRule?: "inherit" | "nonzero" | "evenodd";
    filter?: string;
    filterRes?: string | number;
    filterUnits?: string | number;
    floodColor?: string | number;
    floodOpacity?: string | number;
    focusable?: boolean | "auto" | "true" | "false";
    fontFamily?: string;
    fontSize?: string | number;
    fontSizeAdjust?: string | number;
    fontStretch?: string | number;
    fontStyle?: string | number;
    fontVariant?: string | number;
    fontWeight?: string | number;
    format?: string | number;
    fr?: string | number;
    from?: string | number;
    fx?: string | number;
    fy?: string | number;
    g1?: string | number;
    g2?: string | number;
    glyphName?: string | number;
    glyphOrientationHorizontal?: string | number;
    glyphOrientationVertical?: string | number;
    glyphRef?: string | number;
    gradientTransform?: string;
    gradientUnits?: string;
    hanging?: string | number;
    horizAdvX?: string | number;
    horizOriginX?: string | number;
    href?: string;
    ideographic?: string | number;
    imageRendering?: string | number;
    in2?: string | number;
    in?: string;
    intercept?: string | number;
    k1?: string | number;
    k2?: string | number;
    k3?: string | number;
    k4?: string | number;
    k?: string | number;
    kernelMatrix?: string | number;
    kernelUnitLength?: string | number;
    kerning?: string | number;
    keyPoints?: string | number;
    keySplines?: string | number;
    keyTimes?: string | number;
    lengthAdjust?: string | number;
    letterSpacing?: string | number;
    lightingColor?: string | number;
    limitingConeAngle?: string | number;
    local?: string | number;
    markerEnd?: string;
    markerHeight?: string | number;
    markerMid?: string;
    markerStart?: string;
    markerUnits?: string | number;
    markerWidth?: string | number;
    mask?: string;
    maskContentUnits?: string | number;
    maskUnits?: string | number;
    mathematical?: string | number;
    mode?: string | number;
    numOctaves?: string | number;
    offset?: string | number;
    opacity?: string | number;
    operator?: string | number;
    order?: string | number;
    orient?: string | number;
    orientation?: string | number;
    origin?: string | number;
    overflow?: string | number;
    overlinePosition?: string | number;
    overlineThickness?: string | number;
    paintOrder?: string | number;
    panose1?: string | number;
    path?: string;
    pathLength?: string | number;
    patternContentUnits?: string;
    patternTransform?: string | number;
    patternUnits?: string;
    pointerEvents?: string | number;
    points?: string;
    pointsAtX?: string | number;
    pointsAtY?: string | number;
    pointsAtZ?: string | number;
    preserveAlpha?: boolean | "true" | "false";
    preserveAspectRatio?: string;
    primitiveUnits?: string | number;
    r?: string | number;
    radius?: string | number;
    refX?: string | number;
    refY?: string | number;
    renderingIntent?: string | number;
    repeatCount?: string | number;
    repeatDur?: string | number;
    requiredExtensions?: string | number;
    requiredFeatures?: string | number;
    restart?: string | number;
    result?: string;
    rotate?: string | number;
    rx?: string | number;
    ry?: string | number;
    scale?: string | number;
    seed?: string | number;
    shapeRendering?: string | number;
    slope?: string | number;
    spacing?: string | number;
    specularConstant?: string | number;
    specularExponent?: string | number;
    speed?: string | number;
    spreadMethod?: string;
    startOffset?: string | number;
    stdDeviation?: string | number;
    stemh?: string | number;
    stemv?: string | number;
    stitchTiles?: string | number;
    stopColor?: string;
    stopOpacity?: string | number;
    strikethroughPosition?: string | number;
    strikethroughThickness?: string | number;
    stroke?: string;
    strokeDasharray?: string | number;
    strokeDashoffset?: string | number;
    strokeLinecap?: "square" | "inherit" | "butt" | "round";
    strokeLinejoin?: "inherit" | "round" | "miter" | "bevel";
    strokeMiterlimit?: string | number;
    strokeOpacity?: string | number;
    strokeWidth?: string | number;
    surfaceScale?: string | number;
    systemLanguage?: string | number;
    tableValues?: string | number;
    targetX?: string | number;
    targetY?: string | number;
    textAnchor?: string;
    textDecoration?: string | number;
    textLength?: string | number;
    textRendering?: string | number;
    to?: string | number;
    transform?: string;
    u1?: string | number;
    u2?: string | number;
    underlinePosition?: string | number;
    underlineThickness?: string | number;
    unicode?: string | number;
    unicodeBidi?: string | number;
    unicodeRange?: string | number;
    unitsPerEm?: string | number;
    vAlphabetic?: string | number;
    values?: string;
    vectorEffect?: string | number;
    version?: string;
    vertAdvY?: string | number;
    vertOriginX?: string | number;
    vertOriginY?: string | number;
    vHanging?: string | number;
    vIdeographic?: string | number;
    viewBox?: string;
    viewTarget?: string | number;
    visibility?: string | number;
    vMathematical?: string | number;
    widths?: string | number;
    wordSpacing?: string | number;
    writingMode?: string | number;
    x1?: string | number;
    x2?: string | number;
    x?: string | number;
    xChannelSelector?: string;
    xHeight?: string | number;
    xlinkActuate?: string;
    xlinkArcrole?: string;
    xlinkHref?: string;
    xlinkRole?: string;
    xlinkShow?: string;
    xlinkTitle?: string;
    xlinkType?: string;
    xmlBase?: string;
    xmlLang?: string;
    xmlns?: string;
    xmlnsXlink?: string;
    xmlSpace?: string;
    y1?: string | number;
    y2?: string | number;
    y?: string | number;
    yChannelSelector?: string;
    z?: string | number;
    zoomAndPan?: string;
    ref?: import("react").LegacyRef<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>;
    key?: string | number;
    wrapperStyle?: import("react").CSSProperties;
    chartWidth?: number;
    chartHeight?: number;
    margin?: {
        top?: number;
        left?: number;
        bottom?: number;
        right?: number;
    };
    payloadUniqBy?: boolean | ((entry: import("../component/DefaultLegendContent").Payload) => import("../component/DefaultLegendContent").Payload);
    onBBoxUpdate?: (box: DOMRect) => void;
} | {
    payload: any[];
    item: import("react").DetailedReactHTMLElement<import("../component/Legend").Props, HTMLElement>;
    width: any;
    height?: number;
    content?: import("../component/DefaultLegendContent").ContentType;
    iconSize?: number;
    iconType?: "circle" | "cross" | "diamond" | "square" | "star" | "triangle" | "wye" | "plainline" | "line" | "rect";
    layout?: LayoutType;
    align?: import("../component/DefaultLegendContent").HorizontalAlignmentType;
    verticalAlign?: import("../component/DefaultLegendContent").VerticalAlignmentType;
    inactiveColor?: string;
    formatter?: import("../component/DefaultLegendContent").Formatter;
    onMouseEnter?: ((data: import("../component/DefaultLegendContent").Payload & {
        dataKey?: DataKey<any>;
    }, index: number, event: import("react").MouseEvent<Element, MouseEvent>) => void) & ((data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void);
    onMouseLeave?: ((data: import("../component/DefaultLegendContent").Payload & {
        dataKey?: DataKey<any>;
    }, index: number, event: import("react").MouseEvent<Element, MouseEvent>) => void) & ((data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void);
    onClick?: ((data: import("../component/DefaultLegendContent").Payload & {
        dataKey?: DataKey<any>;
    }, index: number, event: import("react").MouseEvent<Element, MouseEvent>) => void) & ((data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void);
    'aria-activedescendant'?: string;
    'aria-atomic'?: boolean | "true" | "false";
    'aria-autocomplete'?: "none" | "list" | "inline" | "both";
    'aria-busy'?: boolean | "true" | "false";
    'aria-checked'?: boolean | "true" | "false" | "mixed";
    'aria-colcount'?: number;
    'aria-colindex'?: number;
    'aria-colspan'?: number;
    'aria-controls'?: string;
    'aria-current'?: boolean | "time" | "true" | "false" | "page" | "step" | "location" | "date";
    'aria-describedby'?: string;
    'aria-details'?: string;
    'aria-disabled'?: boolean | "true" | "false";
    'aria-dropeffect'?: "none" | "link" | "copy" | "execute" | "move" | "popup";
    'aria-errormessage'?: string;
    'aria-expanded'?: boolean | "true" | "false";
    'aria-flowto'?: string;
    'aria-grabbed'?: boolean | "true" | "false";
    'aria-haspopup'?: boolean | "dialog" | "menu" | "grid" | "listbox" | "tree" | "true" | "false";
    'aria-hidden'?: boolean | "true" | "false";
    'aria-invalid'?: boolean | "true" | "false" | "grammar" | "spelling";
    'aria-keyshortcuts'?: string;
    'aria-label'?: string;
    'aria-labelledby'?: string;
    'aria-level'?: number;
    'aria-live'?: "off" | "assertive" | "polite";
    'aria-modal'?: boolean | "true" | "false";
    'aria-multiline'?: boolean | "true" | "false";
    'aria-multiselectable'?: boolean | "true" | "false";
    'aria-orientation'?: "horizontal" | "vertical";
    'aria-owns'?: string;
    'aria-placeholder'?: string;
    'aria-posinset'?: number;
    'aria-pressed'?: boolean | "true" | "false" | "mixed";
    'aria-readonly'?: boolean | "true" | "false";
    'aria-relevant'?: "text" | "additions" | "additions removals" | "additions text" | "all" | "removals" | "removals additions" | "removals text" | "text additions" | "text removals";
    'aria-required'?: boolean | "true" | "false";
    'aria-roledescription'?: string;
    'aria-rowcount'?: number;
    'aria-rowindex'?: number;
    'aria-rowspan'?: number;
    'aria-selected'?: boolean | "true" | "false";
    'aria-setsize'?: number;
    'aria-sort'?: "none" | "ascending" | "descending" | "other";
    'aria-valuemax'?: number;
    'aria-valuemin'?: number;
    'aria-valuenow'?: number;
    'aria-valuetext'?: string;
    children?: ReactNode;
    dangerouslySetInnerHTML?: {
        __html: string;
    };
    onCopy?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCopyCapture?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCut?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCutCapture?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPaste?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPasteCapture?: (data: any, index: number, event: import("react").ClipboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionEnd?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionEndCapture?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionStart?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionStartCapture?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionUpdate?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onCompositionUpdateCapture?: (data: any, index: number, event: import("react").CompositionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onFocus?: (data: any, index: number, event: import("react").FocusEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Element>) => void;
    onFocusCapture?: (data: any, index: number, event: import("react").FocusEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Element>) => void;
    onBlur?: (data: any, index: number, event: import("react").FocusEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Element>) => void;
    onBlurCapture?: (data: any, index: number, event: import("react").FocusEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Element>) => void;
    onChange?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onChangeCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onBeforeInput?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onBeforeInputCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onInput?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onInputCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onReset?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onResetCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onSubmit?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onSubmitCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onInvalid?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onInvalidCapture?: (data: any, index: number, event: import("react").FormEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onLoad?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onError?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onErrorCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onKeyDown?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onKeyDownCapture?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onKeyPress?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onKeyPressCapture?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onKeyUp?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onKeyUpCapture?: (data: any, index: number, event: import("react").KeyboardEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAbort?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onAbortCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onCanPlay?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onCanPlayCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onCanPlayThrough?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onCanPlayThroughCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onDurationChange?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onDurationChangeCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEmptied?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEmptiedCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEncrypted?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEncryptedCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEnded?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onEndedCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadedData?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadedDataCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadedMetadata?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadedMetadataCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadStart?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onLoadStartCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPause?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPauseCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPlay?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPlayCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPlaying?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onPlayingCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onProgress?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onProgressCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onRateChange?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onRateChangeCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSeeked?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSeekedCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSeeking?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSeekingCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onStalled?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onStalledCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSuspend?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSuspendCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onTimeUpdate?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onTimeUpdateCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onVolumeChange?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onVolumeChangeCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onWaiting?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onWaitingCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onAuxClick?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onAuxClickCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onClickCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onContextMenu?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onContextMenuCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onDoubleClick?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onDoubleClickCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onDrag?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragEnd?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragEndCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragEnter?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragEnterCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragExit?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragExitCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragLeave?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragLeaveCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragOver?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragOverCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragStart?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDragStartCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDrop?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onDropCapture?: (data: any, index: number, event: import("react").DragEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onMouseDown?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseDownCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseMove?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseMoveCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseOut?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseOutCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseOver?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseOverCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseUp?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onMouseUpCapture?: (data: any, index: number, event: import("react").MouseEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, MouseEvent>) => void;
    onSelect?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onSelectCapture?: (data: any, index: number, event: import("react").SyntheticEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, Event>) => void;
    onTouchCancel?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchCancelCapture?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchEnd?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchEndCapture?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchMove?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchMoveCapture?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchStart?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTouchStartCapture?: (data: any, index: number, event: import("react").TouchEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerDown?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerDownCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerMove?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerMoveCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerUp?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerUpCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerCancel?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerCancelCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerEnter?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerEnterCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerLeave?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerLeaveCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerOver?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerOverCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerOut?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onPointerOutCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onGotPointerCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onGotPointerCaptureCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onLostPointerCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onLostPointerCaptureCapture?: (data: any, index: number, event: import("react").PointerEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onScroll?: (data: any, index: number, event: import("react").UIEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, UIEvent>) => void;
    onScrollCapture?: (data: any, index: number, event: import("react").UIEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>, UIEvent>) => void;
    onWheel?: (data: any, index: number, event: import("react").WheelEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onWheelCapture?: (data: any, index: number, event: import("react").WheelEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationStart?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationStartCapture?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationEnd?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationEndCapture?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationIteration?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onAnimationIterationCapture?: (data: any, index: number, event: import("react").AnimationEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTransitionEnd?: (data: any, index: number, event: import("react").TransitionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    onTransitionEndCapture?: (data: any, index: number, event: import("react").TransitionEvent<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>) => void;
    string?: string | number;
    className?: string;
    color?: string;
    id?: string;
    lang?: string;
    max?: string | number;
    media?: string;
    method?: string;
    min?: string | number;
    name?: string;
    style?: import("react").CSSProperties;
    target?: string;
    type?: string;
    role?: import("react").AriaRole;
    tabIndex?: number;
    crossOrigin?: "" | "anonymous" | "use-credentials";
    accentHeight?: string | number;
    accumulate?: "none" | "sum";
    additive?: "replace" | "sum";
    alignmentBaseline?: "alphabetic" | "hanging" | "ideographic" | "mathematical" | "auto" | "baseline" | "before-edge" | "text-before-edge" | "middle" | "central" | "after-edge" | "text-after-edge" | "inherit";
    allowReorder?: "no" | "yes";
    alphabetic?: string | number;
    amplitude?: string | number;
    arabicForm?: "initial" | "medial" | "terminal" | "isolated";
    ascent?: string | number;
    attributeName?: string;
    attributeType?: string;
    autoReverse?: boolean | "true" | "false";
    azimuth?: string | number;
    baseFrequency?: string | number;
    baselineShift?: string | number;
    baseProfile?: string | number;
    bbox?: string | number;
    begin?: string | number;
    bias?: string | number;
    by?: string | number;
    calcMode?: string | number;
    capHeight?: string | number;
    clip?: string | number;
    clipPath?: string;
    clipPathUnits?: string | number;
    clipRule?: string | number;
    colorInterpolation?: string | number;
    colorInterpolationFilters?: "auto" | "inherit" | "sRGB" | "linearRGB";
    colorProfile?: string | number;
    colorRendering?: string | number;
    contentScriptType?: string | number;
    contentStyleType?: string | number;
    cursor?: string | number;
    cx?: string | number;
    cy?: string | number;
    d?: string;
    decelerate?: string | number;
    descent?: string | number;
    diffuseConstant?: string | number;
    direction?: string | number;
    display?: string | number;
    divisor?: string | number;
    dominantBaseline?: string | number;
    dur?: string | number;
    dx?: string | number;
    dy?: string | number;
    edgeMode?: string | number;
    elevation?: string | number;
    enableBackground?: string | number;
    end?: string | number;
    exponent?: string | number;
    externalResourcesRequired?: boolean | "true" | "false";
    fill?: string;
    fillOpacity?: string | number;
    fillRule?: "inherit" | "nonzero" | "evenodd";
    filter?: string;
    filterRes?: string | number;
    filterUnits?: string | number;
    floodColor?: string | number;
    floodOpacity?: string | number;
    focusable?: boolean | "auto" | "true" | "false";
    fontFamily?: string;
    fontSize?: string | number;
    fontSizeAdjust?: string | number;
    fontStretch?: string | number;
    fontStyle?: string | number;
    fontVariant?: string | number;
    fontWeight?: string | number;
    format?: string | number;
    fr?: string | number;
    from?: string | number;
    fx?: string | number;
    fy?: string | number;
    g1?: string | number;
    g2?: string | number;
    glyphName?: string | number;
    glyphOrientationHorizontal?: string | number;
    glyphOrientationVertical?: string | number;
    glyphRef?: string | number;
    gradientTransform?: string;
    gradientUnits?: string;
    hanging?: string | number;
    horizAdvX?: string | number;
    horizOriginX?: string | number;
    href?: string;
    ideographic?: string | number;
    imageRendering?: string | number;
    in2?: string | number;
    in?: string;
    intercept?: string | number;
    k1?: string | number;
    k2?: string | number;
    k3?: string | number;
    k4?: string | number;
    k?: string | number;
    kernelMatrix?: string | number;
    kernelUnitLength?: string | number;
    kerning?: string | number;
    keyPoints?: string | number;
    keySplines?: string | number;
    keyTimes?: string | number;
    lengthAdjust?: string | number;
    letterSpacing?: string | number;
    lightingColor?: string | number;
    limitingConeAngle?: string | number;
    local?: string | number;
    markerEnd?: string;
    markerHeight?: string | number;
    markerMid?: string;
    markerStart?: string;
    markerUnits?: string | number;
    markerWidth?: string | number;
    mask?: string;
    maskContentUnits?: string | number;
    maskUnits?: string | number;
    mathematical?: string | number;
    mode?: string | number;
    numOctaves?: string | number;
    offset?: string | number;
    opacity?: string | number;
    operator?: string | number;
    order?: string | number;
    orient?: string | number;
    orientation?: string | number;
    origin?: string | number;
    overflow?: string | number;
    overlinePosition?: string | number;
    overlineThickness?: string | number;
    paintOrder?: string | number;
    panose1?: string | number;
    path?: string;
    pathLength?: string | number;
    patternContentUnits?: string;
    patternTransform?: string | number;
    patternUnits?: string;
    pointerEvents?: string | number;
    points?: string;
    pointsAtX?: string | number;
    pointsAtY?: string | number;
    pointsAtZ?: string | number;
    preserveAlpha?: boolean | "true" | "false";
    preserveAspectRatio?: string;
    primitiveUnits?: string | number;
    r?: string | number;
    radius?: string | number;
    refX?: string | number;
    refY?: string | number;
    renderingIntent?: string | number;
    repeatCount?: string | number;
    repeatDur?: string | number;
    requiredExtensions?: string | number;
    requiredFeatures?: string | number;
    restart?: string | number;
    result?: string;
    rotate?: string | number;
    rx?: string | number;
    ry?: string | number;
    scale?: string | number;
    seed?: string | number;
    shapeRendering?: string | number;
    slope?: string | number;
    spacing?: string | number;
    specularConstant?: string | number;
    specularExponent?: string | number;
    speed?: string | number;
    spreadMethod?: string;
    startOffset?: string | number;
    stdDeviation?: string | number;
    stemh?: string | number;
    stemv?: string | number;
    stitchTiles?: string | number;
    stopColor?: string;
    stopOpacity?: string | number;
    strikethroughPosition?: string | number;
    strikethroughThickness?: string | number;
    stroke?: string;
    strokeDasharray?: string | number;
    strokeDashoffset?: string | number;
    strokeLinecap?: "square" | "inherit" | "butt" | "round";
    strokeLinejoin?: "inherit" | "round" | "miter" | "bevel";
    strokeMiterlimit?: string | number;
    strokeOpacity?: string | number;
    strokeWidth?: string | number;
    surfaceScale?: string | number;
    systemLanguage?: string | number;
    tableValues?: string | number;
    targetX?: string | number;
    targetY?: string | number;
    textAnchor?: string;
    textDecoration?: string | number;
    textLength?: string | number;
    textRendering?: string | number;
    to?: string | number;
    transform?: string;
    u1?: string | number;
    u2?: string | number;
    underlinePosition?: string | number;
    underlineThickness?: string | number;
    unicode?: string | number;
    unicodeBidi?: string | number;
    unicodeRange?: string | number;
    unitsPerEm?: string | number;
    vAlphabetic?: string | number;
    values?: string;
    vectorEffect?: string | number;
    version?: string;
    vertAdvY?: string | number;
    vertOriginX?: string | number;
    vertOriginY?: string | number;
    vHanging?: string | number;
    vIdeographic?: string | number;
    viewBox?: string;
    viewTarget?: string | number;
    visibility?: string | number;
    vMathematical?: string | number;
    widths?: string | number;
    wordSpacing?: string | number;
    writingMode?: string | number;
    x1?: string | number;
    x2?: string | number;
    x?: string | number;
    xChannelSelector?: string;
    xHeight?: string | number;
    xlinkActuate?: string;
    xlinkArcrole?: string;
    xlinkHref?: string;
    xlinkRole?: string;
    xlinkShow?: string;
    xlinkTitle?: string;
    xlinkType?: string;
    xmlBase?: string;
    xmlLang?: string;
    xmlns?: string;
    xmlnsXlink?: string;
    xmlSpace?: string;
    y1?: string | number;
    y2?: string | number;
    y?: string | number;
    yChannelSelector?: string;
    z?: string | number;
    zoomAndPan?: string;
    ref?: import("react").LegacyRef<ReactElement<any, string | ((props: any) => ReactElement<any, any>) | (new (props: any) => import("react").Component<any, any, any>)>>;
    key?: string | number;
    wrapperStyle?: import("react").CSSProperties;
    chartWidth?: number;
    chartHeight?: number;
    margin?: {
        top?: number;
        left?: number;
        bottom?: number;
        right?: number;
    };
    payloadUniqBy?: boolean | ((entry: import("../component/DefaultLegendContent").Payload) => import("../component/DefaultLegendContent").Payload);
    onBBoxUpdate?: (box: DOMRect) => void;
};
export declare const getBarSizeList: ({ barSize: globalSize, stackGroups, }: {
    barSize: number | string;
    stackGroups: any;
}) => Record<string, any>;
export declare const getBarPosition: ({ barGap, barCategoryGap, bandSize, sizeList, maxBarSize, }: {
    barGap: string | number;
    barCategoryGap: string | number;
    bandSize: number;
    sizeList: Array<any>;
    maxBarSize: number;
}) => any;
export declare const appendOffsetOfLegend: (offset: any, items: Array<FormattedGraphicalItem>, props: any, legendBox: any) => any;
export declare const getDomainOfErrorBars: (data: any[], item: any, dataKey: any, layout?: LayoutType, axisType?: AxisType) => any;
export declare const parseErrorBarsOfAxis: (data: any[], items: any[], dataKey: any, axisType: AxisType, layout?: LayoutType) => any;
export declare const getDomainOfItemsWithSameAxis: (data: any[], items: any[], type: string, layout?: LayoutType, filterNil?: boolean) => any;
export declare const isCategoricalAxis: (layout: LayoutType | PolarLayoutType, axisType: AxisType) => boolean;
export declare const getCoordinatesOfGrid: (ticks: Array<TickItem>, min: number, max: number) => number[];
export declare const getTicksOfAxis: (axis: BaseAxisProps & {
    duplicateDomain?: any;
    realScaleType?: 'scaleBand' | 'band' | 'point' | 'linear';
    scale?: any;
    axisType?: AxisType;
    ticks?: any;
    niceTicks?: any;
    isCategorical?: boolean;
    categoricalDomain?: any;
}, isGrid?: boolean, isAll?: boolean) => TickItem[] | null;
export declare const combineEventHandlers: (defaultHandler: Function, parentHandler: Function, childHandler: Function) => (arg1: any, arg2: any, arg3: any, arg4: any) => void;
export declare const parseScale: (axis: any, chartType?: string, hasBar?: boolean) => {
    scale: any;
    realScaleType: string;
} | {
    scale: (...args: any[]) => any;
    realScaleType?: undefined;
};
export declare const checkDomainOfScale: (scale: any) => void;
export declare const findPositionOfBar: (barPosition: any[], child: ReactNode) => any;
export declare const truncateByDomain: (value: any[], domain: any[]) => any[];
export declare const offsetSign: (series: any) => void;
export declare const offsetPositive: (series: any) => void;
export declare const getStackedData: (data: any, stackItems: any, offsetType: string) => import("d3-shape").Series<{
    [key: string]: number;
}, string>[];
export declare const getStackGroupsByAxisId: (data: any, _items: Array<any>, numericAxisId: string, cateAxisId: string, offsetType: any, reverseStackOrder: boolean) => {};
export declare const getTicksOfScale: (scale: any, opts: any) => {
    niceTicks: any;
};
export declare const getCateCoordinateOfLine: ({ axis, ticks, bandSize, entry, index, dataKey, }: {
    axis: any;
    ticks: Array<TickItem>;
    bandSize: number;
    entry: any;
    index: number;
    dataKey?: string | number | ((obj: any) => any);
}) => any;
export declare const getCateCoordinateOfBar: ({ axis, ticks, offset, bandSize, entry, index, }: {
    axis: any;
    ticks: Array<TickItem>;
    offset: any;
    bandSize: number;
    entry: any;
    index: number;
}) => any;
export declare const getBaseValueOfBar: ({ numericAxis, }: {
    numericAxis: any;
}) => any;
export declare const getStackedDataOfItem: (item: any, stackGroups: any) => any;
export declare const getDomainOfStackGroups: (stackGroups: any, startIndex: number, endIndex: number) => number[];
export declare const MIN_VALUE_REG: RegExp;
export declare const MAX_VALUE_REG: RegExp;
export declare const parseSpecifiedDomain: (specifiedDomain: any, dataDomain: any, allowDataOverflow?: boolean) => any;
export declare const getBandSizeOfAxis: (axis?: any, ticks?: Array<TickItem>, isBar?: boolean) => any;
export declare const parseDomainOfCategoryAxis: (specifiedDomain: Array<any>, calculatedDomain: Array<any>, axisChild: ReactElement) => any[];
export declare const getTooltipItem: (graphicalItem: any, payload: any) => {
    dataKey: any;
    unit: any;
    formatter: any;
    name: any;
    color: any;
    value: any;
    type: any;
    payload: any;
    chartType: any;
};
export {};
