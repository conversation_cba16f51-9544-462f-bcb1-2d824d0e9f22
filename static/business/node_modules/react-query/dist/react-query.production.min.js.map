{"version": 3, "file": "react-query.production.min.js", "sources": ["../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "../src/core/subscribable.ts", "../node_modules/@babel/runtime/helpers/esm/extends.js", "../src/core/utils.ts", "../src/core/focusManager.ts", "../src/core/onlineManager.ts", "../src/core/retryer.ts", "../src/core/notifyManager.ts", "../src/core/logger.ts", "../src/core/query.ts", "../src/core/queryCache.ts", "../src/core/mutation.ts", "../src/core/mutationCache.ts", "../src/core/infiniteQueryBehavior.ts", "../src/core/queryClient.ts", "../src/core/queryObserver.ts", "../src/core/queriesObserver.ts", "../src/core/infiniteQueryObserver.ts", "../src/core/mutationObserver.ts", "../src/core/hydration.ts", "../src/react/QueryClientProvider.tsx", "../src/react/QueryErrorResetBoundary.tsx", "../src/react/useIsFetching.ts", "../src/react/utils.ts", "../src/react/useBaseQuery.ts", "../src/react/Hydrate.tsx", "../src/react/useInfiniteQuery.ts", "../src/react/useIsMutating.ts", "../src/react/useMutation.ts", "../src/react/useQueries.ts", "../src/react/useQuery.ts"], "sourcesContent": ["export default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}", "type Listener = () => void\n\nexport class Subscribable<TListener extends Function = Listener> {\n  protected listeners: TListener[]\n\n  constructor() {\n    this.listeners = []\n  }\n\n  subscribe(listener?: TListener): () => void {\n    const callback = listener || (() => undefined)\n\n    this.listeners.push(callback as TListener)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners = this.listeners.filter(x => x !== callback)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.length > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n", "export default function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}", "import type { Mutation } from './mutation'\nimport type { Query } from './query'\nimport { EnsuredQueryKey } from './types'\nimport type {\n  MutationFunction,\n  MutationKey,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n} from './types'\n\n// TYPES\n\nexport interface QueryFilters {\n  /**\n   * Include or exclude active queries\n   */\n  active?: boolean\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include or exclude inactive queries\n   */\n  inactive?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: QueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include or exclude fetching queries\n   */\n  fetching?: boolean\n}\n\nexport interface MutationFilters {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (mutation: Mutation<any, any, any>) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: Mutation<PERSON>ey\n  /**\n   * Include or exclude fetching mutations\n   */\n  fetching?: boolean\n}\n\nexport type DataUpdateFunction<TInput, TOutput> = (input: TInput) => TOutput\n\nexport type Updater<TInput, TOutput> =\n  | TOutput\n  | DataUpdateFunction<TInput, TOutput>\n\nexport type QueryStatusFilter = 'all' | 'active' | 'inactive' | 'none'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined'\n\nexport function noop(): undefined {\n  return undefined\n}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as DataUpdateFunction<TInput, TOutput>)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function ensureQueryKeyArray<T extends QueryKey>(\n  value: T\n): EnsuredQueryKey<T> {\n  return (Array.isArray(value)\n    ? value\n    : ([value] as unknown)) as EnsuredQueryKey<T>\n}\n\nexport function difference<T>(array1: T[], array2: T[]): T[] {\n  return array1.filter(x => array2.indexOf(x) === -1)\n}\n\nexport function replaceAt<T>(array: T[], index: number, value: T): T[] {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function parseQueryArgs<\n  TOptions extends QueryOptions<any, any, any, TQueryKey>,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  arg1: TQueryKey | TOptions,\n  arg2?: QueryFunction<any, TQueryKey> | TOptions,\n  arg3?: TOptions\n): TOptions {\n  if (!isQueryKey(arg1)) {\n    return arg1 as TOptions\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3, queryKey: arg1, queryFn: arg2 } as TOptions\n  }\n\n  return { ...arg2, queryKey: arg1 } as TOptions\n}\n\nexport function parseMutationArgs<\n  TOptions extends MutationOptions<any, any, any, any>\n>(\n  arg1: MutationKey | MutationFunction<any, any> | TOptions,\n  arg2?: MutationFunction<any, any> | TOptions,\n  arg3?: TOptions\n): TOptions {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3, mutationKey: arg1, mutationFn: arg2 } as TOptions\n    }\n    return { ...arg2, mutationKey: arg1 } as TOptions\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2, mutationFn: arg1 } as TOptions\n  }\n\n  return { ...arg1 } as TOptions\n}\n\nexport function parseFilterArgs<\n  TFilters extends QueryFilters,\n  TOptions = unknown\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions\n): [TFilters, TOptions | undefined] {\n  return (isQueryKey(arg1)\n    ? [{ ...arg2, queryKey: arg1 }, arg3]\n    : [arg1 || {}, arg2]) as [TFilters, TOptions]\n}\n\nexport function parseMutationFilterArgs(\n  arg1?: QueryKey | MutationFilters,\n  arg2?: MutationFilters\n): MutationFilters | undefined {\n  return isQueryKey(arg1) ? { ...arg2, mutationKey: arg1 } : arg1\n}\n\nexport function mapQueryStatusFilter(\n  active?: boolean,\n  inactive?: boolean\n): QueryStatusFilter {\n  if (\n    (active === true && inactive === true) ||\n    (active == null && inactive == null)\n  ) {\n    return 'all'\n  } else if (active === false && inactive === false) {\n    return 'none'\n  } else {\n    // At this point, active|inactive can only be true|false or false|true\n    // so, when only one value is provided, the missing one has to be the negated value\n    const isActive = active ?? !inactive\n    return isActive ? 'active' : 'inactive'\n  }\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>\n): boolean {\n  const {\n    active,\n    exact,\n    fetching,\n    inactive,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  const queryStatusFilter = mapQueryStatusFilter(active, inactive)\n\n  if (queryStatusFilter === 'none') {\n    return false\n  } else if (queryStatusFilter !== 'all') {\n    const isActive = query.isActive()\n    if (queryStatusFilter === 'active' && !isActive) {\n      return false\n    }\n    if (queryStatusFilter === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (typeof fetching === 'boolean' && query.isFetching() !== fetching) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>\n): boolean {\n  const { exact, fetching, predicate, mutationKey } = filters\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (\n        hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)\n      ) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (\n    typeof fetching === 'boolean' &&\n    (mutation.state.status === 'loading') !== fetching\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: QueryOptions<any, any, any, TQueryKey>\n): string {\n  const hashFn = options?.queryKeyHashFn || hashQueryKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query keys hash function.\n */\nexport function hashQueryKey(queryKey: QueryKey): string {\n  const asArray = ensureQueryKeyArray(queryKey)\n  return stableValueHash(asArray)\n}\n\n/**\n * Hashes the value into a stable hash.\n */\nexport function stableValueHash(value: any): string {\n  return JSON.stringify(value, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean {\n  return partialDeepEqual(ensureQueryKeyArray(a), ensureQueryKeyArray(b))\n}\n\n/**\n * Checks if `b` partially matches with `a`.\n */\nexport function partialDeepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(key => !partialDeepEqual(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = Array.isArray(a) && Array.isArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aSize = array ? a.length : Object.keys(a).length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      copy[key] = replaceEqualDeep(a[key], b[key])\n      if (copy[key] === a[key]) {\n        equalItems++\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\nexport function shallowEqualObjects<T>(a: T, b: T): boolean {\n  if ((a && !b) || (b && !a)) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isQueryKey(value: any): value is QueryKey {\n  return typeof value === 'string' || Array.isArray(value)\n}\n\nexport function isError(value: any): value is Error {\n  return value instanceof Error\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise(resolve => {\n    setTimeout(resolve, timeout)\n  })\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nexport function scheduleMicrotask(callback: () => void): void {\n  Promise.resolve()\n    .then(callback)\n    .catch(error =>\n      setTimeout(() => {\n        throw error\n      })\n    )\n}\n\nexport function getAbortController(): AbortController | undefined {\n  if (typeof AbortController === 'function') {\n    return new AbortController()\n  }\n}\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable {\n  private focused?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = onFocus => {\n      if (!isServer && window?.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibillitychange and focus\n        window.addEventListener('visibilitychange', listener, false)\n        window.addEventListener('focus', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n          window.removeEventListener('focus', listener)\n        }\n      }\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup(focused => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    this.focused = focused\n\n    if (focused) {\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    this.listeners.forEach(listener => {\n      listener()\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.focused === 'boolean') {\n      return this.focused\n    }\n\n    // document global can be unavailable in react native\n    if (typeof document === 'undefined') {\n      return true\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(\n      document.visibilityState\n    )\n  }\n}\n\nexport const focusManager = new FocusManager()\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setOnline: (online?: boolean) => void\n) => (() => void) | undefined\n\nexport class OnlineManager extends Subscribable {\n  private online?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = onOnline => {\n      if (!isServer && window?.addEventListener) {\n        const listener = () => onOnline()\n        // Listen to online\n        window.addEventListener('online', listener, false)\n        window.addEventListener('offline', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', listener)\n          window.removeEventListener('offline', listener)\n        }\n      }\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((online?: boolean) => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online)\n      } else {\n        this.onOnline()\n      }\n    })\n  }\n\n  setOnline(online?: boolean): void {\n    this.online = online\n\n    if (online) {\n      this.onOnline()\n    }\n  }\n\n  onOnline(): void {\n    this.listeners.forEach(listener => {\n      listener()\n    })\n  }\n\n  isOnline(): boolean {\n    if (typeof this.online === 'boolean') {\n      return this.online\n    }\n\n    if (\n      typeof navigator === 'undefined' ||\n      typeof navigator.onLine === 'undefined'\n    ) {\n      return true\n    }\n\n    return navigator.onLine\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n", "import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { sleep } from './utils'\nimport { CancelOptions } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = unknown> {\n  fn: () => TData | Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError = unknown> = (\n  failureCount: number,\n  error: TError\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = unknown> = (\n  failureCount: number,\n  error: TError\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\ninterface Cancelable {\n  cancel(): void\n}\n\nexport function isCancelable(value: any): value is Cancelable {\n  return typeof value?.cancel === 'function'\n}\n\nexport class CancelledError {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\n// CLASS\n\nexport class Retryer<TData = unknown, TError = unknown> {\n  cancel: (options?: CancelOptions) => void\n  cancelRetry: () => void\n  continueRetry: () => void\n  continue: () => void\n  failureCount: number\n  isPaused: boolean\n  isResolved: boolean\n  isTransportCancelable: boolean\n  promise: Promise<TData>\n\n  private abort?: () => void\n\n  constructor(config: RetryerConfig<TData, TError>) {\n    let cancelRetry = false\n    let cancelFn: ((options?: CancelOptions) => void) | undefined\n    let continueFn: ((value?: unknown) => void) | undefined\n    let promiseResolve: (data: TData) => void\n    let promiseReject: (error: TError) => void\n\n    this.abort = config.abort\n    this.cancel = cancelOptions => cancelFn?.(cancelOptions)\n    this.cancelRetry = () => {\n      cancelRetry = true\n    }\n    this.continueRetry = () => {\n      cancelRetry = false\n    }\n    this.continue = () => continueFn?.()\n    this.failureCount = 0\n    this.isPaused = false\n    this.isResolved = false\n    this.isTransportCancelable = false\n    this.promise = new Promise<TData>((outerResolve, outerReject) => {\n      promiseResolve = outerResolve\n      promiseReject = outerReject\n    })\n\n    const resolve = (value: any) => {\n      if (!this.isResolved) {\n        this.isResolved = true\n        config.onSuccess?.(value)\n        continueFn?.()\n        promiseResolve(value)\n      }\n    }\n\n    const reject = (value: any) => {\n      if (!this.isResolved) {\n        this.isResolved = true\n        config.onError?.(value)\n        continueFn?.()\n        promiseReject(value)\n      }\n    }\n\n    const pause = () => {\n      return new Promise(continueResolve => {\n        continueFn = continueResolve\n        this.isPaused = true\n        config.onPause?.()\n      }).then(() => {\n        continueFn = undefined\n        this.isPaused = false\n        config.onContinue?.()\n      })\n    }\n\n    // Create loop function\n    const run = () => {\n      // Do nothing if already resolved\n      if (this.isResolved) {\n        return\n      }\n\n      let promiseOrValue: any\n\n      // Execute query\n      try {\n        promiseOrValue = config.fn()\n      } catch (error) {\n        promiseOrValue = Promise.reject(error)\n      }\n\n      // Create callback to cancel this fetch\n      cancelFn = cancelOptions => {\n        if (!this.isResolved) {\n          reject(new CancelledError(cancelOptions))\n\n          this.abort?.()\n\n          // Cancel transport if supported\n          if (isCancelable(promiseOrValue)) {\n            try {\n              promiseOrValue.cancel()\n            } catch {}\n          }\n        }\n      }\n\n      // Check if the transport layer support cancellation\n      this.isTransportCancelable = isCancelable(promiseOrValue)\n\n      Promise.resolve(promiseOrValue)\n        .then(resolve)\n        .catch(error => {\n          // Stop if the fetch is already resolved\n          if (this.isResolved) {\n            return\n          }\n\n          // Do we need to retry the request?\n          const retry = config.retry ?? 3\n          const retryDelay = config.retryDelay ?? defaultRetryDelay\n          const delay =\n            typeof retryDelay === 'function'\n              ? retryDelay(this.failureCount, error)\n              : retryDelay\n          const shouldRetry =\n            retry === true ||\n            (typeof retry === 'number' && this.failureCount < retry) ||\n            (typeof retry === 'function' && retry(this.failureCount, error))\n\n          if (cancelRetry || !shouldRetry) {\n            // We are done if the query does not need to be retried\n            reject(error)\n            return\n          }\n\n          this.failureCount++\n\n          // Notify on fail\n          config.onFail?.(this.failureCount, error)\n\n          // Delay\n          sleep(delay)\n            // Pause if the document is not visible or when the device is offline\n            .then(() => {\n              if (!focusManager.isFocused() || !onlineManager.isOnline()) {\n                return pause()\n              }\n            })\n            .then(() => {\n              if (cancelRetry) {\n                reject(error)\n              } else {\n                run()\n              }\n            })\n        })\n    }\n\n    // Start loop\n    run()\n  }\n}\n", "import { scheduleMicrotask } from './utils'\n\n// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\n// CLASS\n\nexport class NotifyManager {\n  private queue: NotifyCallback[]\n  private transactions: number\n  private notifyFn: NotifyFunction\n  private batchNotifyFn: BatchNotifyFunction\n\n  constructor() {\n    this.queue = []\n    this.transactions = 0\n\n    this.notifyFn = (callback: () => void) => {\n      callback()\n    }\n\n    this.batchNotifyFn = (callback: () => void) => {\n      callback()\n    }\n  }\n\n  batch<T>(callback: () => T): T {\n    let result\n    this.transactions++\n    try {\n      result = callback()\n    } finally {\n      this.transactions--\n      if (!this.transactions) {\n        this.flush()\n      }\n    }\n    return result\n  }\n\n  schedule(callback: NotifyCallback): void {\n    if (this.transactions) {\n      this.queue.push(callback)\n    } else {\n      scheduleMicrotask(() => {\n        this.notifyFn(callback)\n      })\n    }\n  }\n\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  batchCalls<T extends Function>(callback: T): T {\n    return ((...args: any[]) => {\n      this.schedule(() => {\n        callback(...args)\n      })\n    }) as any\n  }\n\n  flush(): void {\n    const queue = this.queue\n    this.queue = []\n    if (queue.length) {\n      scheduleMicrotask(() => {\n        this.batchNotifyFn(() => {\n          queue.forEach(callback => {\n            this.notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  setNotifyFunction(fn: NotifyFunction) {\n    this.notifyFn = fn\n  }\n\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  setBatchNotifyFunction(fn: BatchNotifyFunction) {\n    this.batchNotifyFn = fn\n  }\n}\n\n// SINGLETON\n\nexport const notifyManager = new NotifyManager()\n", "// TYPES\n\nexport interface Logger {\n  log: LogFunction\n  warn: LogFunction\n  error: LogFunction\n}\n\ntype LogFunction = (...args: any[]) => void\n\n// FUNCTIONS\n\nlet logger: Logger = console\n\nexport function getLogger(): Logger {\n  return logger\n}\n\nexport function setLogger(newLogger: Logger) {\n  logger = newLogger\n}\n", "import {\n  getAbort<PERSON><PERSON>roller,\n  Updater,\n  functionalUpdate,\n  isValidTimeout,\n  noop,\n  replaceEqual<PERSON>eep,\n  timeUntilStale,\n  ensureQueryKeyArray,\n} from './utils'\nimport type {\n  InitialDataFunction,\n  QueryKey,\n  QueryOptions,\n  QueryStatus,\n  QueryFunctionContext,\n  EnsuredQueryKey,\n  QueryMeta,\n  CancelOptions,\n  SetDataOptions,\n} from './types'\nimport type { QueryCache } from './queryCache'\nimport type { QueryObserver } from './queryObserver'\nimport { notifyManager } from './notifyManager'\nimport { getLogger } from './logger'\nimport { Retryer, isCancelledError } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey\n> {\n  cache: QueryCache\n  queryKey: TQueryKey\n  queryHash: string\n  options?: QueryOptions<TQueryFnData, TE<PERSON>r, TData, TQ<PERSON>yKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n  meta: QueryMeta | undefined\n}\n\nexport interface QueryState<TData = unknown, TError = unknown> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchMeta: any\n  isFetching: boolean\n  isInvalidated: boolean\n  isPaused: boolean\n  status: QueryStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  queryKey: EnsuredQueryKey<TQueryKey>\n  state: QueryState<TData, TError>\n  meta: QueryMeta | undefined\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>\n  ) => void\n}\n\nexport interface FetchOptions {\n  cancelRefetch?: boolean\n  meta?: any\n}\n\ninterface FailedAction {\n  type: 'failed'\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: any\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: QueryState<TData, TError>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n> {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  initialState: QueryState<TData, TError>\n  revertState?: QueryState<TData, TError>\n  state: QueryState<TData, TError>\n  cacheTime!: number\n  meta: QueryMeta | undefined\n\n  private cache: QueryCache\n  private promise?: Promise<TData>\n  private gcTimeout?: number\n  private retryer?: Retryer<TData, TError>\n  private observers: QueryObserver<any, any, any, any, any>[]\n  private defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  private abortSignalConsumed: boolean\n  private hadObservers: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    this.abortSignalConsumed = false\n    this.hadObservers = false\n    this.defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.cache = config.cache\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.initialState = config.state || this.getDefaultState(this.options)\n    this.state = this.initialState\n    this.meta = config.meta\n    this.scheduleGc()\n  }\n\n  private setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.meta = options?.meta\n\n    // Default to 5 minutes if not cache time is set\n    this.cacheTime = Math.max(\n      this.cacheTime || 0,\n      this.options.cacheTime ?? 5 * 60 * 1000\n    )\n  }\n\n  setDefaultOptions(\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): void {\n    this.defaultOptions = options\n  }\n\n  private scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.cacheTime)\n    }\n  }\n\n  private clearGcTimeout() {\n    clearTimeout(this.gcTimeout)\n    this.gcTimeout = undefined\n  }\n\n  private optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.isFetching) {\n        if (this.hadObservers) {\n          this.scheduleGc()\n        }\n      } else {\n        this.cache.remove(this)\n      }\n    }\n  }\n\n  setData(\n    updater: Updater<TData | undefined, TData>,\n    options?: SetDataOptions\n  ): TData {\n    const prevData = this.state.data\n\n    // Get the new data\n    let data = functionalUpdate(updater, prevData)\n\n    // Use prev data if an isDataEqual function is defined and returns `true`\n    if (this.options.isDataEqual?.(prevData, data)) {\n      data = prevData as TData\n    } else if (this.options.structuralSharing !== false) {\n      // Structurally share data between prev and new data if needed\n      data = replaceEqualDeep(prevData, data)\n    }\n\n    // Set data and mark it as cached\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n    })\n\n    return data\n  }\n\n  setState(\n    state: QueryState<TData, TError>,\n    setStateOptions?: SetStateOptions\n  ): void {\n    this.dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.promise\n    this.retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    this.clearGcTimeout()\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some(observer => observer.options.enabled !== false)\n  }\n\n  isFetching(): boolean {\n    return this.state.isFetching\n  }\n\n  isStale(): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      this.observers.some(observer => observer.getCurrentResult().isStale)\n    )\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus())\n\n    if (observer) {\n      observer.refetch()\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect())\n\n    if (observer) {\n      observer.refetch()\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer)\n      this.hadObservers = true\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.indexOf(observer) !== -1) {\n      this.observers = this.observers.filter(x => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.retryer.isTransportCancelable || this.abortSignalConsumed) {\n            this.retryer.cancel({ revert: true })\n          } else {\n            this.retryer.cancelRetry()\n          }\n        }\n\n        if (this.cacheTime) {\n          this.scheduleGc()\n        } else {\n          this.cache.remove(this)\n        }\n      }\n\n      this.cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions\n  ): Promise<TData> {\n    if (this.state.isFetching) {\n      if (this.state.dataUpdatedAt && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({ silent: true })\n      } else if (this.promise) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.retryer?.continueRetry()\n        // Return current promise if we are already fetching\n        return this.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    const queryKey = ensureQueryKeyArray(this.queryKey)\n    const abortController = getAbortController()\n\n    // Create query function context\n    const queryFnContext: QueryFunctionContext<TQueryKey> = {\n      queryKey,\n      pageParam: undefined,\n      meta: this.meta,\n    }\n\n    Object.defineProperty(queryFnContext, 'signal', {\n      enumerable: true,\n      get: () => {\n        if (abortController) {\n          this.abortSignalConsumed = true\n          return abortController.signal\n        }\n        return undefined\n      },\n    })\n\n    // Create fetch function\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject('Missing queryFn')\n      }\n      this.abortSignalConsumed = false\n      return this.options.queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const context: FetchContext<TQueryFnData, TError, TData, TQueryKey> = {\n      fetchOptions,\n      options: this.options,\n      queryKey: queryKey,\n      state: this.state,\n      fetchFn,\n      meta: this.meta,\n    }\n\n    if (this.options.behavior?.onFetch) {\n      this.options.behavior?.onFetch(context)\n    }\n\n    // Store state in case the current fetch needs to be reverted\n    this.revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      !this.state.isFetching ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    // Try to fetch the data\n    this.retryer = new Retryer({\n      fn: context.fetchFn as () => TData,\n      abort: abortController?.abort?.bind(abortController),\n      onSuccess: data => {\n        this.setData(data as TData)\n\n        // Notify cache callback\n        this.cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n\n        // Remove query after fetching if cache time is 0\n        if (this.cacheTime === 0) {\n          this.optionalRemove()\n        }\n      },\n      onError: (error: TError | { silent?: boolean }) => {\n        // Optimistically update state if needed\n        if (!(isCancelledError(error) && error.silent)) {\n          this.dispatch({\n            type: 'error',\n            error: error as TError,\n          })\n        }\n\n        if (!isCancelledError(error)) {\n          // Notify cache callback\n          this.cache.config.onError?.(error, this as Query<any, any, any, any>)\n\n          // Log error\n          getLogger().error(error)\n        }\n\n        // Remove query after fetching if cache time is 0\n        if (this.cacheTime === 0) {\n          this.optionalRemove()\n        }\n      },\n      onFail: () => {\n        this.dispatch({ type: 'failed' })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n    })\n\n    this.promise = this.retryer.promise\n\n    return this.promise\n  }\n\n  private dispatch(action: Action<TData, TError>): void {\n    this.state = this.reducer(this.state, action)\n\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate(action)\n      })\n\n      this.cache.notify({ query: this, type: 'queryUpdated', action })\n    })\n  }\n\n  protected getDefaultState(\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): QueryState<TData, TError> {\n    const data =\n      typeof options.initialData === 'function'\n        ? (options.initialData as InitialDataFunction<TData>)()\n        : options.initialData\n\n    const hasInitialData = typeof options.initialData !== 'undefined'\n\n    const initialDataUpdatedAt = hasInitialData\n      ? typeof options.initialDataUpdatedAt === 'function'\n        ? (options.initialDataUpdatedAt as () => number | undefined)()\n        : options.initialDataUpdatedAt\n      : 0\n\n    const hasData = typeof data !== 'undefined'\n\n    return {\n      data,\n      dataUpdateCount: 0,\n      dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n      error: null,\n      errorUpdateCount: 0,\n      errorUpdatedAt: 0,\n      fetchFailureCount: 0,\n      fetchMeta: null,\n      isFetching: false,\n      isInvalidated: false,\n      isPaused: false,\n      status: hasData ? 'success' : 'idle',\n    }\n  }\n\n  protected reducer(\n    state: QueryState<TData, TError>,\n    action: Action<TData, TError>\n  ): QueryState<TData, TError> {\n    switch (action.type) {\n      case 'failed':\n        return {\n          ...state,\n          fetchFailureCount: state.fetchFailureCount + 1,\n        }\n      case 'pause':\n        return {\n          ...state,\n          isPaused: true,\n        }\n      case 'continue':\n        return {\n          ...state,\n          isPaused: false,\n        }\n      case 'fetch':\n        return {\n          ...state,\n          fetchFailureCount: 0,\n          fetchMeta: action.meta ?? null,\n          isFetching: true,\n          isPaused: false,\n          ...(!state.dataUpdatedAt && {\n            error: null,\n            status: 'loading',\n          }),\n        }\n      case 'success':\n        return {\n          ...state,\n          data: action.data,\n          dataUpdateCount: state.dataUpdateCount + 1,\n          dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n          error: null,\n          fetchFailureCount: 0,\n          isFetching: false,\n          isInvalidated: false,\n          isPaused: false,\n          status: 'success',\n        }\n      case 'error':\n        const error = action.error as unknown\n\n        if (isCancelledError(error) && error.revert && this.revertState) {\n          return { ...this.revertState }\n        }\n\n        return {\n          ...state,\n          error: error as TError,\n          errorUpdateCount: state.errorUpdateCount + 1,\n          errorUpdatedAt: Date.now(),\n          fetchFailureCount: state.fetchFailureCount + 1,\n          isFetching: false,\n          isPaused: false,\n          status: 'error',\n        }\n      case 'invalidate':\n        return {\n          ...state,\n          isInvalidated: true,\n        }\n      case 'setState':\n        return {\n          ...state,\n          ...action.state,\n        }\n      default:\n        return state\n    }\n  }\n}\n", "import {\n  QueryFilters,\n  hashQueryKeyByOptions,\n  matchQuery,\n  parseFilterArgs,\n} from './utils'\nimport { Action, Query, QueryState } from './query'\nimport type { QueryKey, QueryOptions } from './types'\nimport { notifyManager } from './notifyManager'\nimport type { QueryClient } from './queryClient'\nimport { Subscribable } from './subscribable'\nimport { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (error: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n}\n\ninterface QueryHashMap {\n  [hash: string]: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryAdded {\n  type: 'queryAdded'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved {\n  type: 'queryRemoved'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated {\n  type: 'queryUpdated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventObserverAdded {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventObserverRemoved {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventObserverResultsUpdated {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ntype QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventObserverAdded\n  | NotifyEventObserverRemoved\n  | NotifyEventObserverResultsUpdated\n\ntype QueryCacheListener = (event?: QueryCacheNotifyEvent) => void\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  config: QueryCacheConfig\n\n  private queries: Query<any, any, any, any>[]\n  private queriesMap: QueryHashMap\n\n  constructor(config?: QueryCacheConfig) {\n    super()\n    this.config = config || {}\n    this.queries = []\n    this.queriesMap = {}\n  }\n\n  build<TQueryFnData, TError, TData, TQueryKey extends QueryKey>(\n    client: QueryClient,\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    state?: QueryState<TData, TError>\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey!\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n        meta: options.meta,\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query\n      this.queries.push(query)\n      this.notify({\n        type: 'queryAdded',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.queriesMap[query.queryHash]\n\n    if (queryInMap) {\n      query.destroy()\n\n      this.queries = this.queries.filter(x => x !== query)\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash]\n      }\n\n      this.notify({ type: 'queryRemoved', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueyKey extends QueryKey = QueryKey\n  >(\n    queryHash: string\n  ): Query<TQueryFnData, TError, TData, TQueyKey> | undefined {\n    return this.queriesMap[queryHash]\n  }\n\n  getAll(): Query[] {\n    return this.queries\n  }\n\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    arg1: QueryKey,\n    arg2?: QueryFilters\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const [filters] = parseFilterArgs(arg1, arg2)\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.queries.find(query => matchQuery(filters, query))\n  }\n\n  findAll(queryKey?: QueryKey, filters?: QueryFilters): Query[]\n  findAll(filters?: QueryFilters): Query[]\n  findAll(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): Query[]\n  findAll(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): Query[] {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    return Object.keys(filters).length > 0\n      ? this.queries.filter(query => matchQuery(filters, query))\n      : this.queries\n  }\n\n  notify(event: QueryCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(listener => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onOnline()\n      })\n    })\n  }\n}\n", "import type { MutationOptions, MutationStatus, MutationMeta } from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport { getLogger } from './logger'\nimport { notify<PERSON>anager } from './notifyManager'\nimport { <PERSON>try<PERSON> } from './retryer'\nimport { noop } from './utils'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n  meta?: MutationMeta\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n}\n\ninterface FailedAction {\n  type: 'failed'\n}\n\ninterface LoadingAction<TVariables, TContext> {\n  type: 'loading'\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError, TVariables, TContext> {\n  type: 'setState'\n  state: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction\n  | LoadingAction<TVariables, TContext>\n  | PauseAction\n  | SetStateAction<TData, TError, TVariables, TContext>\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n> {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  mutationId: number\n  meta: MutationMeta | undefined\n\n  private observers: MutationObserver<TData, TError, TVariables, TContext>[]\n  private mutationCache: MutationCache\n  private retryer?: Retryer<TData, TError>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    this.options = {\n      ...config.defaultOptions,\n      ...config.options,\n    }\n    this.mutationId = config.mutationId\n    this.mutationCache = config.mutationCache\n    this.observers = []\n    this.state = config.state || getDefaultState()\n    this.meta = config.meta\n  }\n\n  setState(state: MutationState<TData, TError, TVariables, TContext>): void {\n    this.dispatch({ type: 'setState', state })\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer)\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.observers = this.observers.filter(x => x !== observer)\n  }\n\n  cancel(): Promise<void> {\n    if (this.retryer) {\n      this.retryer.cancel()\n      return this.retryer.promise.then(noop).catch(noop)\n    }\n    return Promise.resolve()\n  }\n\n  continue(): Promise<TData> {\n    if (this.retryer) {\n      this.retryer.continue()\n      return this.retryer.promise\n    }\n    return this.execute()\n  }\n\n  execute(): Promise<TData> {\n    let data: TData\n\n    const restored = this.state.status === 'loading'\n\n    let promise = Promise.resolve()\n\n    if (!restored) {\n      this.dispatch({ type: 'loading', variables: this.options.variables! })\n      promise = promise\n        .then(() => {\n          // Notify cache callback\n          this.mutationCache.config.onMutate?.(\n            this.state.variables,\n            this as Mutation<unknown, unknown, unknown, unknown>\n          )\n        })\n        .then(() => this.options.onMutate?.(this.state.variables!))\n        .then(context => {\n          if (context !== this.state.context) {\n            this.dispatch({\n              type: 'loading',\n              context,\n              variables: this.state.variables,\n            })\n          }\n        })\n    }\n\n    return promise\n      .then(() => this.executeMutation())\n      .then(result => {\n        data = result\n        // Notify cache callback\n        this.mutationCache.config.onSuccess?.(\n          data,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>\n        )\n      })\n      .then(() =>\n        this.options.onSuccess?.(\n          data,\n          this.state.variables!,\n          this.state.context!\n        )\n      )\n      .then(() =>\n        this.options.onSettled?.(\n          data,\n          null,\n          this.state.variables!,\n          this.state.context\n        )\n      )\n      .then(() => {\n        this.dispatch({ type: 'success', data })\n        return data\n      })\n      .catch(error => {\n        // Notify cache callback\n        this.mutationCache.config.onError?.(\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>\n        )\n\n        // Log error\n        getLogger().error(error)\n\n        return Promise.resolve()\n          .then(() =>\n            this.options.onError?.(\n              error,\n              this.state.variables!,\n              this.state.context\n            )\n          )\n          .then(() =>\n            this.options.onSettled?.(\n              undefined,\n              error,\n              this.state.variables!,\n              this.state.context\n            )\n          )\n          .then(() => {\n            this.dispatch({ type: 'error', error })\n            throw error\n          })\n      })\n  }\n\n  private executeMutation(): Promise<TData> {\n    this.retryer = new Retryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject('No mutationFn found')\n        }\n        return this.options.mutationFn(this.state.variables!)\n      },\n      onFail: () => {\n        this.dispatch({ type: 'failed' })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n    })\n\n    return this.retryer.promise\n  }\n\n  private dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    this.state = reducer(this.state, action)\n\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onMutationUpdate(action)\n      })\n      this.mutationCache.notify(this)\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n  }\n}\n\nfunction reducer<TData, TError, TVariables, TContext>(\n  state: MutationState<TData, TError, TVariables, TContext>,\n  action: Action<TData, TError, TVariables, TContext>\n): MutationState<TData, TError, TVariables, TContext> {\n  switch (action.type) {\n    case 'failed':\n      return {\n        ...state,\n        failureCount: state.failureCount + 1,\n      }\n    case 'pause':\n      return {\n        ...state,\n        isPaused: true,\n      }\n    case 'continue':\n      return {\n        ...state,\n        isPaused: false,\n      }\n    case 'loading':\n      return {\n        ...state,\n        context: action.context,\n        data: undefined,\n        error: null,\n        isPaused: false,\n        status: 'loading',\n        variables: action.variables,\n      }\n    case 'success':\n      return {\n        ...state,\n        data: action.data,\n        error: null,\n        status: 'success',\n        isPaused: false,\n      }\n    case 'error':\n      return {\n        ...state,\n        data: undefined,\n        error: action.error,\n        failureCount: state.failureCount + 1,\n        isPaused: false,\n        status: 'error',\n      }\n    case 'setState':\n      return {\n        ...state,\n        ...action.state,\n      }\n    default:\n      return state\n  }\n}\n", "import type { MutationOptions } from './types'\nimport type { QueryClient } from './queryClient'\nimport { notifyManager } from './notifyManager'\nimport { Mutation, MutationState } from './mutation'\nimport { matchMutation, MutationFilters, noop } from './utils'\nimport { Subscribable } from './subscribable'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown, unknown>\n  ) => void\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown, unknown>\n  ) => void\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown, unknown>\n  ) => void\n}\n\ntype MutationCacheListener = (mutation?: Mutation) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  config: MutationCacheConfig\n\n  private mutations: Mutation<any, any, any, any>[]\n  private mutationId: number\n\n  constructor(config?: MutationCacheConfig) {\n    super()\n    this.config = config || {}\n    this.mutations = []\n    this.mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey\n        ? client.getMutationDefaults(options.mutationKey)\n        : undefined,\n      meta: options.meta,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.mutations.push(mutation)\n    this.notify(mutation)\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    this.mutations = this.mutations.filter(x => x !== mutation)\n    mutation.cancel()\n    this.notify(mutation)\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.mutations.forEach(mutation => {\n        this.remove(mutation)\n      })\n    })\n  }\n\n  getAll(): Mutation[] {\n    return this.mutations\n  }\n\n  find<TData = unknown, TError = unknown, TVariables = any, TContext = unknown>(\n    filters: MutationFilters\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.mutations.find(mutation => matchMutation(filters, mutation))\n  }\n\n  findAll(filters: MutationFilters): Mutation[] {\n    return this.mutations.filter(mutation => matchMutation(filters, mutation))\n  }\n\n  notify(mutation?: Mutation<any, any, any, any>) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(listener => {\n        listener(mutation)\n      })\n    })\n  }\n\n  onFocus(): void {\n    this.resumePausedMutations()\n  }\n\n  onOnline(): void {\n    this.resumePausedMutations()\n  }\n\n  resumePausedMutations(): Promise<void> {\n    const pausedMutations = this.mutations.filter(x => x.state.isPaused)\n    return notifyManager.batch(() =>\n      pausedMutations.reduce(\n        (promise, mutation) =>\n          promise.then(() => mutation.continue().catch(noop)),\n        Promise.resolve()\n      )\n    )\n  }\n}\n", "import type { QueryBehavior } from './query'\nimport { isCancelable } from './retryer'\nimport type {\n  InfiniteData,\n  QueryFunctionContext,\n  QueryOptions,\n  RefetchQueryFilters,\n} from './types'\nimport { getAbortController } from './utils'\n\nexport function infiniteQueryBehavior<\n  TQueryFnData,\n  TError,\n  TData\n>(): QueryBehavior<TQueryFnData, TError, InfiniteData<TData>> {\n  return {\n    onFetch: context => {\n      context.fetchFn = () => {\n        const refetchPage: RefetchQueryFilters['refetchPage'] | undefined =\n          context.fetchOptions?.meta?.refetchPage\n        const fetchMore = context.fetchOptions?.meta?.fetchMore\n        const pageParam = fetchMore?.pageParam\n        const isFetchingNextPage = fetchMore?.direction === 'forward'\n        const isFetchingPreviousPage = fetchMore?.direction === 'backward'\n        const oldPages = context.state.data?.pages || []\n        const oldPageParams = context.state.data?.pageParams || []\n        const abortController = getAbortController()\n        const abortSignal = abortController?.signal\n        let newPageParams = oldPageParams\n        let cancelled = false\n\n        // Get query function\n        const queryFn =\n          context.options.queryFn || (() => Promise.reject('Missing queryFn'))\n\n        const buildNewPages = (\n          pages: unknown[],\n          param: unknown,\n          page: unknown,\n          previous?: boolean\n        ) => {\n          newPageParams = previous\n            ? [param, ...newPageParams]\n            : [...newPageParams, param]\n          return previous ? [page, ...pages] : [...pages, page]\n        }\n\n        // Create function to fetch a page\n        const fetchPage = (\n          pages: unknown[],\n          manual?: boolean,\n          param?: unknown,\n          previous?: boolean\n        ): Promise<unknown[]> => {\n          if (cancelled) {\n            return Promise.reject('Cancelled')\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages)\n          }\n\n          const queryFnContext: QueryFunctionContext = {\n            queryKey: context.queryKey,\n            signal: abortSignal,\n            pageParam: param,\n            meta: context.meta,\n          }\n\n          const queryFnResult = queryFn(queryFnContext)\n\n          const promise = Promise.resolve(queryFnResult).then(page =>\n            buildNewPages(pages, param, page, previous)\n          )\n\n          if (isCancelable(queryFnResult)) {\n            const promiseAsAny = promise as any\n            promiseAsAny.cancel = queryFnResult.cancel\n          }\n\n          return promise\n        }\n\n        let promise: Promise<unknown[]>\n\n        // Fetch first page?\n        if (!oldPages.length) {\n          promise = fetchPage([])\n        }\n\n        // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getNextPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param)\n        }\n\n        // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getPreviousPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param, true)\n        }\n\n        // Refetch pages\n        else {\n          newPageParams = []\n\n          const manual = typeof context.options.getNextPageParam === 'undefined'\n\n          const shouldFetchFirstPage =\n            refetchPage && oldPages[0]\n              ? refetchPage(oldPages[0], 0, oldPages)\n              : true\n\n          // Fetch first page\n          promise = shouldFetchFirstPage\n            ? fetchPage([], manual, oldPageParams[0])\n            : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0]))\n\n          // Fetch remaining pages\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then(pages => {\n              const shouldFetchNextPage =\n                refetchPage && oldPages[i]\n                  ? refetchPage(oldPages[i], i, oldPages)\n                  : true\n\n              if (shouldFetchNextPage) {\n                const param = manual\n                  ? oldPageParams[i]\n                  : getNextPageParam(context.options, pages)\n                return fetchPage(pages, manual, param)\n              }\n              return Promise.resolve(\n                buildNewPages(pages, oldPageParams[i], oldPages[i])\n              )\n            })\n          }\n        }\n\n        const finalPromise = promise.then(pages => ({\n          pages,\n          pageParams: newPageParams,\n        }))\n\n        const finalPromiseAsAny = finalPromise as any\n\n        finalPromiseAsAny.cancel = () => {\n          cancelled = true\n          abortController?.abort()\n          if (isCancelable(promise)) {\n            promise.cancel()\n          }\n        }\n\n        return finalPromise\n      }\n    },\n  }\n}\n\nexport function getNextPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[]\n): unknown | undefined {\n  return options.getNextPageParam?.(pages[pages.length - 1], pages)\n}\n\nexport function getPreviousPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[]\n): unknown | undefined {\n  return options.getPreviousPageParam?.(pages[0], pages)\n}\n\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasNextPage(\n  options: QueryOptions<any, any>,\n  pages?: unknown\n): boolean | undefined {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages)\n    return (\n      typeof nextPageParam !== 'undefined' &&\n      nextPageParam !== null &&\n      nextPageParam !== false\n    )\n  }\n}\n\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasPreviousPage(\n  options: QueryOptions<any, any>,\n  pages?: unknown\n): boolean | undefined {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages)\n    return (\n      typeof previousPageParam !== 'undefined' &&\n      previousPageParam !== null &&\n      previousPageParam !== false\n    )\n  }\n}\n", "import {\n  QueryFilters,\n  Updater,\n  hashQueryKey,\n  noop,\n  parseFilterArgs,\n  parseQueryArgs,\n  partialMatchKey,\n  hashQueryKeyByOptions,\n  MutationFilters,\n} from './utils'\nimport type {\n  QueryClientConfig,\n  DefaultOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  ResetQueryFilters,\n  SetDataOptions,\n} from './types'\nimport type { QueryState } from './query'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport { CancelOptions } from './types'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: QueryOptions<any, any, any>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  private queryCache: QueryCache\n  private mutationCache: MutationCache\n  private defaultOptions: DefaultOptions\n  private queryDefaults: QueryDefaults[]\n  private mutationDefaults: MutationDefaults[]\n  private unsubscribeFocus?: () => void\n  private unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.queryCache = config.queryCache || new QueryCache()\n    this.mutationCache = config.mutationCache || new MutationCache()\n    this.defaultOptions = config.defaultOptions || {}\n    this.queryDefaults = []\n    this.mutationDefaults = []\n  }\n\n  mount(): void {\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        this.mutationCache.onFocus()\n        this.queryCache.onFocus()\n      }\n    })\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        this.mutationCache.onOnline()\n        this.queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.unsubscribeFocus?.()\n    this.unsubscribeOnline?.()\n  }\n\n  isFetching(filters?: QueryFilters): number\n  isFetching(queryKey?: QueryKey, filters?: QueryFilters): number\n  isFetching(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): number {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    filters.fetching = true\n    return this.queryCache.findAll(filters).length\n  }\n\n  isMutating(filters?: MutationFilters): number {\n    return this.mutationCache.findAll({ ...filters, fetching: true }).length\n  }\n\n  getQueryData<TData = unknown>(\n    queryKey: QueryKey,\n    filters?: QueryFilters\n  ): TData | undefined {\n    return this.queryCache.find<TData>(queryKey, filters)?.state.data\n  }\n\n  getQueriesData<TData = unknown>(queryKey: QueryKey): [QueryKey, TData][]\n  getQueriesData<TData = unknown>(filters: QueryFilters): [QueryKey, TData][]\n  getQueriesData<TData = unknown>(\n    queryKeyOrFilters: QueryKey | QueryFilters\n  ): [QueryKey, TData][] {\n    return this.getQueryCache()\n      .findAll(queryKeyOrFilters)\n      .map(({ queryKey, state }) => {\n        const data = state.data as TData\n        return [queryKey, data]\n      })\n  }\n\n  setQueryData<TData>(\n    queryKey: QueryKey,\n    updater: Updater<TData | undefined, TData>,\n    options?: SetDataOptions\n  ): TData {\n    const parsedOptions = parseQueryArgs(queryKey)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n    return this.queryCache\n      .build(this, defaultedOptions)\n      .setData(updater, options)\n  }\n\n  setQueriesData<TData>(\n    queryKey: QueryKey,\n    updater: Updater<TData | undefined, TData>,\n    options?: SetDataOptions\n  ): [QueryKey, TData][]\n\n  setQueriesData<TData>(\n    filters: QueryFilters,\n    updater: Updater<TData | undefined, TData>,\n    options?: SetDataOptions\n  ): [QueryKey, TData][]\n\n  setQueriesData<TData>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n    updater: Updater<TData | undefined, TData>,\n    options?: SetDataOptions\n  ): [QueryKey, TData][] {\n    return notifyManager.batch(() =>\n      this.getQueryCache()\n        .findAll(queryKeyOrFilters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TData>(queryKey, updater, options),\n        ])\n    )\n  }\n\n  getQueryState<TData = unknown, TError = undefined>(\n    queryKey: QueryKey,\n    filters?: QueryFilters\n  ): QueryState<TData, TError> | undefined {\n    return this.queryCache.find<TData, TError>(queryKey, filters)?.state\n  }\n\n  removeQueries(filters?: QueryFilters): void\n  removeQueries(queryKey?: QueryKey, filters?: QueryFilters): void\n  removeQueries(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): void {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    const queryCache = this.queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TPageData = unknown>(\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions\n  ): Promise<void>\n  resetQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions\n  ): Promise<void>\n  resetQueries(\n    arg1?: QueryKey | ResetQueryFilters,\n    arg2?: ResetQueryFilters | ResetOptions,\n    arg3?: ResetOptions\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n    const queryCache = this.queryCache\n\n    const refetchFilters: RefetchQueryFilters = {\n      ...filters,\n      active: true,\n    }\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        query.reset()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  cancelQueries(filters?: QueryFilters, options?: CancelOptions): Promise<void>\n  cancelQueries(\n    queryKey?: QueryKey,\n    filters?: QueryFilters,\n    options?: CancelOptions\n  ): Promise<void>\n  cancelQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: QueryFilters | CancelOptions,\n    arg3?: CancelOptions\n  ): Promise<void> {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3)\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true\n    }\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache.findAll(filters).map(query => query.cancel(cancelOptions))\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TPageData = unknown>(\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions\n  ): Promise<void>\n  invalidateQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions\n  ): Promise<void>\n  invalidateQueries(\n    arg1?: QueryKey | InvalidateQueryFilters,\n    arg2?: InvalidateQueryFilters | InvalidateOptions,\n    arg3?: InvalidateOptions\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    const refetchFilters: RefetchQueryFilters = {\n      ...filters,\n      // if filters.refetchActive is not provided and filters.active is explicitly false,\n      // e.g. invalidateQueries({ active: false }), we don't want to refetch active queries\n      active: filters.refetchActive ?? filters.active ?? true,\n      inactive: filters.refetchInactive ?? false,\n    }\n\n    return notifyManager.batch(() => {\n      this.queryCache.findAll(filters).forEach(query => {\n        query.invalidate()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  refetchQueries<TPageData = unknown>(\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions\n  ): Promise<void>\n  refetchQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions\n  ): Promise<void>\n  refetchQueries(\n    arg1?: QueryKey | RefetchQueryFilters,\n    arg2?: RefetchQueryFilters | RefetchOptions,\n    arg3?: RefetchOptions\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache.findAll(filters).map(query =>\n        query.fetch(undefined, {\n          ...options,\n          meta: { refetchPage: filters?.refetchPage },\n        })\n      )\n    )\n\n    let promise = Promise.all(promises).then(noop)\n\n    if (!options?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(defaultedOptions.staleTime)\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void> {\n    return this.fetchQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<InfiniteData<TData>> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    parsedOptions.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData\n    >()\n    return this.fetchQuery(parsedOptions)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  cancelMutations(): Promise<void> {\n    const promises = notifyManager.batch(() =>\n      this.mutationCache.getAll().map(mutation => mutation.cancel())\n    )\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  resumePausedMutations(): Promise<void> {\n    return this.getMutationCache().resumePausedMutations()\n  }\n\n  executeMutation<\n    TData = unknown,\n    TError = unknown,\n    TVariables = void,\n    TContext = unknown\n  >(\n    options: MutationOptions<TData, TError, TVariables, TContext>\n  ): Promise<TData> {\n    return this.mutationCache.build(this, options).execute()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.mutationCache\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.defaultOptions = options\n  }\n\n  setQueryDefaults(\n    queryKey: QueryKey,\n    options: QueryObserverOptions<any, any, any, any>\n  ): void {\n    const result = this.queryDefaults.find(\n      x => hashQueryKey(queryKey) === hashQueryKey(x.queryKey)\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.queryDefaults.push({ queryKey, defaultOptions: options })\n    }\n  }\n\n  getQueryDefaults(\n    queryKey?: QueryKey\n  ): QueryObserverOptions<any, any, any, any, any> | undefined {\n    return queryKey\n      ? this.queryDefaults.find(x => partialMatchKey(queryKey, x.queryKey))\n          ?.defaultOptions\n      : undefined\n  }\n\n  setMutationDefaults(\n    mutationKey: MutationKey,\n    options: MutationObserverOptions<any, any, any, any>\n  ): void {\n    const result = this.mutationDefaults.find(\n      x => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey)\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.mutationDefaults.push({ mutationKey, defaultOptions: options })\n    }\n  }\n\n  getMutationDefaults(\n    mutationKey?: MutationKey\n  ): MutationObserverOptions<any, any, any, any> | undefined {\n    return mutationKey\n      ? this.mutationDefaults.find(x =>\n          partialMatchKey(mutationKey, x.mutationKey)\n        )?.defaultOptions\n      : undefined\n  }\n\n  defaultQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey extends QueryKey\n  >(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey> {\n    if (options?._defaulted) {\n      return options\n    }\n\n    const defaultedOptions = {\n      ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options?.queryKey),\n      ...options,\n      _defaulted: true,\n    } as QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      )\n    }\n\n    return defaultedOptions\n  }\n\n  defaultQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey extends QueryKey\n  >(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey> {\n    return this.defaultQueryOptions(options)\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options?.mutationKey),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.queryCache.clear()\n    this.mutationCache.clear()\n  }\n}\n", "import { RefetchQueryFilters } from './types'\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceEqualDeep,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport type {\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n  ResultOptions,\n} from './types'\nimport type { Query, QueryState, Action, FetchOptions } from './query'\nimport type { QueryClient } from './queryClient'\nimport { focusManager } from './focusManager'\nimport { Subscribable } from './subscribable'\nimport { getLogger } from './logger'\nimport { isCancelledError } from './retryer'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>\n) => void\n\nexport interface NotifyOptions {\n  cache?: boolean\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\nexport interface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  options: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n\n  private client: QueryClient\n  private currentQuery!: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  private currentQueryInitialState!: QueryState<TQueryData, TError>\n  private currentResult!: QueryObserverResult<TData, TError>\n  private currentResultState?: QueryState<TQueryData, TError>\n  private currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  private previousQueryResult?: QueryObserverResult<TData, TError>\n  private selectError: Error | null\n  private selectFn?: (data: TQueryData) => TData\n  private selectResult?: TData\n  private staleTimeoutId?: number\n  private refetchIntervalId?: number\n  private currentRefetchInterval?: number | false\n  private trackedProps!: Array<keyof QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ) {\n    super()\n\n    this.client = client\n    this.options = options\n    this.trackedProps = []\n    this.selectError = null\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.remove = this.remove.bind(this)\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.length === 1) {\n      this.currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch()\n      }\n\n      this.updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.length) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnReconnect\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus\n    )\n  }\n\n  destroy(): void {\n    this.listeners = []\n    this.clearTimers()\n    this.currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.currentQuery\n\n    this.options = this.client.defaultQueryObserverOptions(options)\n\n    if (\n      typeof this.options.enabled !== 'undefined' &&\n      typeof this.options.enabled !== 'boolean'\n    ) {\n      throw new Error('Expected enabled to be a boolean')\n    }\n\n    // Keep previous query key if the user does not supply one\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey\n    }\n\n    this.updateQuery()\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions\n      )\n    ) {\n      this.executeFetch()\n    }\n\n    // Update result\n    this.updateResult(notifyOptions)\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        this.options.staleTime !== prevOptions.staleTime)\n    ) {\n      this.updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        nextRefetchInterval !== this.currentRefetchInterval)\n    ) {\n      this.updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): QueryObserverResult<TData, TError> {\n    const defaultedOptions = this.client.defaultQueryObserverOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(\n        this.client,\n        defaultedOptions as QueryOptions<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >\n      )\n\n    return this.createResult(query, defaultedOptions)\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n    defaultedOptions: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): QueryObserverResult<TData, TError> {\n    const trackedResult = {} as QueryObserverResult<TData, TError>\n\n    const trackProp = (key: keyof QueryObserverResult) => {\n      if (!this.trackedProps.includes(key)) {\n        this.trackedProps.push(key)\n      }\n    }\n\n    Object.keys(result).forEach(key => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          trackProp(key as keyof QueryObserverResult)\n          return result[key as keyof QueryObserverResult]\n        },\n      })\n    })\n\n    if (defaultedOptions.useErrorBoundary || defaultedOptions.suspense) {\n      trackProp('error')\n    }\n\n    return trackedResult\n  }\n\n  getNextResult(\n    options?: ResultOptions\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return new Promise((resolve, reject) => {\n      const unsubscribe = this.subscribe(result => {\n        if (!result.isFetching) {\n          unsubscribe()\n          if (result.isError && options?.throwOnError) {\n            reject(result.error)\n          } else {\n            resolve(result)\n          }\n        }\n      })\n    })\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.currentQuery\n  }\n\n  remove(): void {\n    this.client.getQueryCache().remove(this.currentQuery)\n  }\n\n  refetch<TPageData>(\n    options?: RefetchOptions & RefetchQueryFilters<TPageData>\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.fetch({\n      ...options,\n      meta: { refetchPage: options?.refetchPage },\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.client.defaultQueryObserverOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(\n        this.client,\n        defaultedOptions as QueryOptions<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >\n      )\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions?: ObserverFetchOptions\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.executeFetch(fetchOptions).then(() => {\n      this.updateResult()\n      return this.currentResult\n    })\n  }\n\n  private executeFetch(\n    fetchOptions?: ObserverFetchOptions\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  private updateStaleTimeout(): void {\n    this.clearStaleTimeout()\n\n    if (\n      isServer ||\n      this.currentResult.isStale ||\n      !isValidTimeout(this.options.staleTime)\n    ) {\n      return\n    }\n\n    const time = timeUntilStale(\n      this.currentResult.dataUpdatedAt,\n      this.options.staleTime\n    )\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  private computeRefetchInterval() {\n    return typeof this.options.refetchInterval === 'function'\n      ? this.options.refetchInterval(this.currentResult.data, this.currentQuery)\n      : this.options.refetchInterval ?? false\n  }\n\n  private updateRefetchInterval(nextInterval: number | false): void {\n    this.clearRefetchInterval()\n\n    this.currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      this.options.enabled === false ||\n      !isValidTimeout(this.currentRefetchInterval) ||\n      this.currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.executeFetch()\n      }\n    }, this.currentRefetchInterval)\n  }\n\n  private updateTimers(): void {\n    this.updateStaleTimeout()\n    this.updateRefetchInterval(this.computeRefetchInterval())\n  }\n\n  private clearTimers(): void {\n    this.clearStaleTimeout()\n    this.clearRefetchInterval()\n  }\n\n  private clearStaleTimeout(): void {\n    clearTimeout(this.staleTimeoutId)\n    this.staleTimeoutId = undefined\n  }\n\n  private clearRefetchInterval(): void {\n    clearInterval(this.refetchIntervalId)\n    this.refetchIntervalId = undefined\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.currentQuery\n    const prevOptions = this.options\n    const prevResult = this.currentResult\n    const prevResultState = this.currentResultState\n    const prevResultOptions = this.currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.currentQueryInitialState\n    const prevQueryResult = queryChange\n      ? this.currentResult\n      : this.previousQueryResult\n\n    const { state } = query\n    let { dataUpdatedAt, error, errorUpdatedAt, isFetching, status } = state\n    let isPreviousData = false\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options.optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        isFetching = true\n        if (!dataUpdatedAt) {\n          status = 'loading'\n        }\n      }\n    }\n\n    // Keep previous data if needed\n    if (\n      options.keepPreviousData &&\n      !state.dataUpdateCount &&\n      prevQueryResult?.isSuccess &&\n      status !== 'error'\n    ) {\n      data = prevQueryResult.data\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt\n      status = prevQueryResult.status\n      isPreviousData = true\n    }\n    // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (\n        prevResult &&\n        state.data === prevResultState?.data &&\n        options.select === this.selectFn\n      ) {\n        data = this.selectResult\n      } else {\n        try {\n          this.selectFn = options.select\n          data = options.select(state.data)\n          if (options.structuralSharing !== false) {\n            data = replaceEqualDeep(prevResult?.data, data)\n          }\n          this.selectResult = data\n          this.selectError = null\n        } catch (selectError) {\n          getLogger().error(selectError)\n          this.selectError = selectError\n        }\n      }\n    }\n    // Use query data\n    else {\n      data = (state.data as unknown) as TData\n    }\n\n    // Show placeholder data if needed\n    if (\n      typeof options.placeholderData !== 'undefined' &&\n      typeof data === 'undefined' &&\n      (status === 'loading' || status === 'idle')\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n      } else {\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (options.placeholderData as PlaceholderDataFunction<TQueryData>)()\n            : options.placeholderData\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData)\n            if (options.structuralSharing !== false) {\n              placeholderData = replaceEqualDeep(\n                prevResult?.data,\n                placeholderData\n              )\n            }\n            this.selectError = null\n          } catch (selectError) {\n            getLogger().error(selectError)\n            this.selectError = selectError\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success'\n        data = placeholderData as TData\n        isPlaceholderData = true\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError as any\n      data = this.selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      isLoading: status === 'loading',\n      isSuccess: status === 'success',\n      isError: status === 'error',\n      isIdle: status === 'idle',\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        state.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && status !== 'loading',\n      isLoadingError: status === 'error' && state.dataUpdatedAt === 0,\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: status === 'error' && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove,\n    }\n\n    return result as QueryObserverResult<TData, TError>\n  }\n\n  private shouldNotifyListeners(\n    result: QueryObserverResult,\n    prevResult?: QueryObserverResult\n  ): boolean {\n    if (!prevResult) {\n      return true\n    }\n\n    const { notifyOnChangeProps, notifyOnChangePropsExclusions } = this.options\n\n    if (!notifyOnChangeProps && !notifyOnChangePropsExclusions) {\n      return true\n    }\n\n    if (notifyOnChangeProps === 'tracked' && !this.trackedProps.length) {\n      return true\n    }\n\n    const includedProps =\n      notifyOnChangeProps === 'tracked'\n        ? this.trackedProps\n        : notifyOnChangeProps\n\n    return Object.keys(result).some(key => {\n      const typedKey = key as keyof QueryObserverResult\n      const changed = result[typedKey] !== prevResult[typedKey]\n      const isIncluded = includedProps?.some(x => x === key)\n      const isExcluded = notifyOnChangePropsExclusions?.some(x => x === key)\n      return changed && !isExcluded && (!includedProps || isIncluded)\n    })\n  }\n\n  updateResult(notifyOptions?: NotifyOptions): void {\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    this.currentResult = this.createResult(this.currentQuery, this.options)\n    this.currentResultState = this.currentQuery.state\n    this.currentResultOptions = this.options\n\n    // Only notify if something has changed\n    if (shallowEqualObjects(this.currentResult, prevResult)) {\n      return\n    }\n\n    // Determine which callbacks to trigger\n    const defaultNotifyOptions: NotifyOptions = { cache: true }\n\n    if (\n      notifyOptions?.listeners !== false &&\n      this.shouldNotifyListeners(this.currentResult, prevResult)\n    ) {\n      defaultNotifyOptions.listeners = true\n    }\n\n    this.notify({ ...defaultNotifyOptions, ...notifyOptions })\n  }\n\n  private updateQuery(): void {\n    const query = this.client\n      .getQueryCache()\n      .build(\n        this.client,\n        this.options as QueryOptions<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >\n      )\n\n    if (query === this.currentQuery) {\n      return\n    }\n\n    const prevQuery = this.currentQuery\n    this.currentQuery = query\n    this.currentQueryInitialState = query.state\n    this.previousQueryResult = this.currentResult\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(action: Action<TData, TError>): void {\n    const notifyOptions: NotifyOptions = {}\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true\n    }\n\n    this.updateResult(notifyOptions)\n\n    if (this.hasListeners()) {\n      this.updateTimers()\n    }\n  }\n\n  private notify(notifyOptions: NotifyOptions): void {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        this.options.onSuccess?.(this.currentResult.data!)\n        this.options.onSettled?.(this.currentResult.data!, null)\n      } else if (notifyOptions.onError) {\n        this.options.onError?.(this.currentResult.error!)\n        this.options.onSettled?.(undefined, this.currentResult.error!)\n      }\n\n      // Then trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(listener => {\n          listener(this.currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      if (notifyOptions.cache) {\n        this.client\n          .getQueryCache()\n          .notify({ query: this.currentQuery, type: 'observerResultsUpdated' })\n      }\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>\n): boolean {\n  return (\n    options.enabled !== false &&\n    !query.state.dataUpdatedAt &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.dataUpdatedAt > 0 &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: typeof options['refetchOnMount'] &\n    typeof options['refetchOnWindowFocus'] &\n    typeof options['refetchOnReconnect']\n) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>\n): boolean {\n  return (\n    options.enabled !== false &&\n    (query !== prevQuery || prevOptions.enabled === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>\n): boolean {\n  return query.isStaleByTime(options.staleTime)\n}\n", "import { difference, replaceAt } from './utils'\nimport { notifyManager } from './notifyManager'\nimport type { QueryObserverOptions, QueryObserverResult } from './types'\nimport type { QueryClient } from './queryClient'\nimport { NotifyOptions, QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\n\ntype QueriesObserverListener = (result: QueryObserverResult[]) => void\n\nexport class QueriesObserver extends Subscribable<QueriesObserverListener> {\n  private client: QueryClient\n  private result: QueryObserverResult[]\n  private queries: QueryObserverOptions[]\n  private observers: QueryObserver[]\n  private observersMap: Record<string, QueryObserver>\n\n  constructor(client: QueryClient, queries?: QueryObserverOptions[]) {\n    super()\n\n    this.client = client\n    this.queries = []\n    this.result = []\n    this.observers = []\n    this.observersMap = {}\n\n    if (queries) {\n      this.setQueries(queries)\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.length === 1) {\n      this.observers.forEach(observer => {\n        observer.subscribe(result => {\n          this.onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.length) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = []\n    this.observers.forEach(observer => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: QueryObserverOptions[],\n    notifyOptions?: NotifyOptions\n  ): void {\n    this.queries = queries\n    this.updateObservers(notifyOptions)\n  }\n\n  getCurrentResult(): QueryObserverResult[] {\n    return this.result\n  }\n\n  getOptimisticResult(queries: QueryObserverOptions[]): QueryObserverResult[] {\n    return this.findMatchingObservers(queries).map(match =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions)\n    )\n  }\n\n  private findMatchingObservers(\n    queries: QueryObserverOptions[]\n  ): QueryObserverMatch[] {\n    const prevObservers = this.observers\n    const defaultedQueryOptions = queries.map(options =>\n      this.client.defaultQueryObserverOptions(options)\n    )\n\n    const matchingObservers: QueryObserverMatch[] = defaultedQueryOptions.flatMap(\n      defaultedOptions => {\n        const match = prevObservers.find(\n          observer => observer.options.queryHash === defaultedOptions.queryHash\n        )\n        if (match != null) {\n          return [{ defaultedQueryOptions: defaultedOptions, observer: match }]\n        }\n        return []\n      }\n    )\n\n    const matchedQueryHashes = matchingObservers.map(\n      match => match.defaultedQueryOptions.queryHash\n    )\n    const unmatchedQueries = defaultedQueryOptions.filter(\n      defaultedOptions =>\n        !matchedQueryHashes.includes(defaultedOptions.queryHash)\n    )\n\n    const unmatchedObservers = prevObservers.filter(\n      prevObserver =>\n        !matchingObservers.some(match => match.observer === prevObserver)\n    )\n\n    const newOrReusedObservers: QueryObserverMatch[] = unmatchedQueries.map(\n      (options, index) => {\n        if (options.keepPreviousData) {\n          // return previous data from one of the observers that no longer match\n          const previouslyUsedObserver = unmatchedObservers[index]\n          if (previouslyUsedObserver !== undefined) {\n            return {\n              defaultedQueryOptions: options,\n              observer: previouslyUsedObserver,\n            }\n          }\n        }\n        return {\n          defaultedQueryOptions: options,\n          observer: this.getObserver(options),\n        }\n      }\n    )\n\n    const sortMatchesByOrderOfQueries = (\n      a: QueryObserverMatch,\n      b: QueryObserverMatch\n    ): number =>\n      defaultedQueryOptions.indexOf(a.defaultedQueryOptions) -\n      defaultedQueryOptions.indexOf(b.defaultedQueryOptions)\n\n    return matchingObservers\n      .concat(newOrReusedObservers)\n      .sort(sortMatchesByOrderOfQueries)\n  }\n\n  private getObserver(options: QueryObserverOptions): QueryObserver {\n    const defaultedOptions = this.client.defaultQueryObserverOptions(options)\n    const currentObserver = this.observersMap[defaultedOptions.queryHash!]\n    return currentObserver ?? new QueryObserver(this.client, defaultedOptions)\n  }\n\n  private updateObservers(notifyOptions?: NotifyOptions): void {\n    notifyManager.batch(() => {\n      const prevObservers = this.observers\n\n      const newObserverMatches = this.findMatchingObservers(this.queries)\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach(match =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions)\n      )\n\n      const newObservers = newObserverMatches.map(match => match.observer)\n      const newObserversMap = Object.fromEntries(\n        newObservers.map(observer => [observer.options.queryHash, observer])\n      )\n      const newResult = newObservers.map(observer =>\n        observer.getCurrentResult()\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index]\n      )\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.observers = newObservers\n      this.observersMap = newObserversMap\n      this.result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach(observer => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach(observer => {\n        observer.subscribe(result => {\n          this.onUpdate(observer, result)\n        })\n      })\n\n      this.notify()\n    })\n  }\n\n  private onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.observers.indexOf(observer)\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result)\n      this.notify()\n    }\n  }\n\n  private notify(): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach(listener => {\n        listener(this.result)\n      })\n    })\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: QueryObserverOptions\n  observer: QueryObserver\n}\n", "import type {\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport {\n  NotifyOptions,\n  ObserverFetchOptions,\n  QueryObserver,\n} from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  InfiniteData<TData>,\n  InfiniteData<TQueryData>\n> {\n  // Type override\n  subscribe!: (\n    listener?: InfiniteQueryObserverListener<TData, TError>\n  ) => () => void\n\n  // Type override\n  getCurrentResult!: () => InfiniteQueryObserverResult<TData, TError>\n\n  // Type override\n  protected fetch!: (\n    fetchOptions?: ObserverFetchOptions\n  ) => Promise<InfiniteQueryObserverResult<TData, TError>>\n\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData\n    >\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options?: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData\n    >,\n    notifyOptions?: NotifyOptions\n  ): void {\n    super.setOptions(\n      {\n        ...options,\n        behavior: infiniteQueryBehavior(),\n      },\n      notifyOptions\n    )\n  }\n\n  getOptimisticResult(\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData\n    >\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage(\n    options?: FetchNextPageOptions\n  ): Promise<InfiniteQueryObserverResult<TData, TError>> {\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: options?.cancelRefetch ?? true,\n      throwOnError: options?.throwOnError,\n      meta: {\n        fetchMore: { direction: 'forward', pageParam: options?.pageParam },\n      },\n    })\n  }\n\n  fetchPreviousPage(\n    options?: FetchPreviousPageOptions\n  ): Promise<InfiniteQueryObserverResult<TData, TError>> {\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: options?.cancelRefetch ?? true,\n      throwOnError: options?.throwOnError,\n      meta: {\n        fetchMore: { direction: 'backward', pageParam: options?.pageParam },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, InfiniteData<TQueryData>>,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData\n    >\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const result = super.createResult(query, options)\n    return {\n      ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data?.pages),\n      hasPreviousPage: hasPreviousPage(options, state.data?.pages),\n      isFetchingNextPage:\n        state.isFetching && state.fetchMeta?.fetchMore?.direction === 'forward',\n      isFetchingPreviousPage:\n        state.isFetching &&\n        state.fetchMeta?.fetchMore?.direction === 'backward',\n    }\n  }\n}\n", "import { Action, getDefaultState, Mutation } from './mutation'\nimport { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport type { QueryClient } from './queryClient'\nimport { Subscribable } from './subscribable'\nimport type {\n  MutateOptions,\n  MutationObserverBaseResult,\n  MutationObserverResult,\n  MutationObserverOptions,\n} from './types'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>\n) => void\n\ninterface NotifyOptions {\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TE<PERSON>r, TVariables, TContext>\n\n  private client: QueryClient\n  private currentResult!: MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >\n  private currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  private mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>\n  ) {\n    super()\n\n    this.client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options?: MutationObserverOptions<TData, TError, TVariables, TContext>\n  ) {\n    this.options = this.client.defaultMutationOptions(options)\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.length) {\n      this.currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.updateResult()\n\n    // Determine which callbacks to trigger\n    const notifyOptions: NotifyOptions = {\n      listeners: true,\n    }\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true\n    }\n\n    this.notify(notifyOptions)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.currentResult\n  }\n\n  reset(): void {\n    this.currentMutation = undefined\n    this.updateResult()\n    this.notify({ listeners: true })\n  }\n\n  mutate(\n    variables?: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>\n  ): Promise<TData> {\n    this.mutateOptions = options\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this)\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, {\n      ...this.options,\n      variables:\n        typeof variables !== 'undefined' ? variables : this.options.variables,\n    })\n\n    this.currentMutation.addObserver(this)\n\n    return this.currentMutation.execute()\n  }\n\n  private updateResult(): void {\n    const state = this.currentMutation\n      ? this.currentMutation.state\n      : getDefaultState<TData, TError, TVariables, TContext>()\n\n    const result: MutationObserverBaseResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    > = {\n      ...state,\n      isLoading: state.status === 'loading',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    }\n\n    this.currentResult = result as MutationObserverResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    >\n  }\n\n  private notify(options: NotifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions) {\n        if (options.onSuccess) {\n          this.mutateOptions.onSuccess?.(\n            this.currentResult.data!,\n            this.currentResult.variables!,\n            this.currentResult.context!\n          )\n          this.mutateOptions.onSettled?.(\n            this.currentResult.data!,\n            null,\n            this.currentResult.variables!,\n            this.currentResult.context\n          )\n        } else if (options.onError) {\n          this.mutateOptions.onError?.(\n            this.currentResult.error!,\n            this.currentResult.variables!,\n            this.currentResult.context\n          )\n          this.mutateOptions.onSettled?.(\n            undefined,\n            this.currentResult.error,\n            this.currentResult.variables!,\n            this.currentResult.context\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      if (options.listeners) {\n        this.listeners.forEach(listener => {\n          listener(this.currentResult)\n        })\n      }\n    })\n  }\n}\n", "import type { QueryClient } from './queryClient'\nimport type { Query, QueryState } from './query'\nimport type {\n  MutationKey,\n  MutationOptions,\n  QueryKey,\n  QueryOptions,\n} from './types'\nimport type { Mutation, MutationState } from './mutation'\n\n// TYPES\n\nexport interface DehydrateOptions {\n  dehydrateMutations?: boolean\n  dehydrateQueries?: boolean\n  shouldDehydrateMutation?: ShouldDehydrateMutationFunction\n  shouldDehydrateQuery?: ShouldDehydrateQueryFunction\n}\n\nexport interface HydrateOptions {\n  defaultOptions?: {\n    queries?: QueryOptions\n    mutations?: MutationOptions\n  }\n}\n\ninterface DehydratedMutation {\n  mutationKey?: MutationKey\n  state: MutationState\n}\n\ninterface DehydratedQuery {\n  queryHash: string\n  queryKey: QueryKey\n  state: QueryState\n}\n\nexport interface DehydratedState {\n  mutations: DehydratedMutation[]\n  queries: DehydratedQuery[]\n}\n\nexport type ShouldDehydrateQueryFunction = (query: Query) => boolean\n\nexport type ShouldDehydrateMutationFunction = (mutation: Mutation) => boolean\n\n// FUNCTIONS\n\nfunction dehydrateMutation(mutation: Mutation): DehydratedMutation {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state,\n  }\n}\n\n// Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\nfunction dehydrateQuery(query: Query): DehydratedQuery {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash,\n  }\n}\n\nfunction defaultShouldDehydrateMutation(mutation: Mutation) {\n  return mutation.state.isPaused\n}\n\nfunction defaultShouldDehydrateQuery(query: Query) {\n  return query.state.status === 'success'\n}\n\nexport function dehydrate(\n  client: QueryClient,\n  options?: DehydrateOptions\n): DehydratedState {\n  options = options || {}\n\n  const mutations: DehydratedMutation[] = []\n  const queries: DehydratedQuery[] = []\n\n  if (options?.dehydrateMutations !== false) {\n    const shouldDehydrateMutation =\n      options.shouldDehydrateMutation || defaultShouldDehydrateMutation\n\n    client\n      .getMutationCache()\n      .getAll()\n      .forEach(mutation => {\n        if (shouldDehydrateMutation(mutation)) {\n          mutations.push(dehydrateMutation(mutation))\n        }\n      })\n  }\n\n  if (options?.dehydrateQueries !== false) {\n    const shouldDehydrateQuery =\n      options.shouldDehydrateQuery || defaultShouldDehydrateQuery\n\n    client\n      .getQueryCache()\n      .getAll()\n      .forEach(query => {\n        if (shouldDehydrateQuery(query)) {\n          queries.push(dehydrateQuery(query))\n        }\n      })\n  }\n\n  return { mutations, queries }\n}\n\nexport function hydrate(\n  client: QueryClient,\n  dehydratedState: unknown,\n  options?: HydrateOptions\n): void {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return\n  }\n\n  const mutationCache = client.getMutationCache()\n  const queryCache = client.getQueryCache()\n\n  const mutations = (dehydratedState as DehydratedState).mutations || []\n  const queries = (dehydratedState as DehydratedState).queries || []\n\n  mutations.forEach(dehydratedMutation => {\n    mutationCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.mutations,\n        mutationKey: dehydratedMutation.mutationKey,\n      },\n      dehydratedMutation.state\n    )\n  })\n\n  queries.forEach(dehydratedQuery => {\n    const query = queryCache.get(dehydratedQuery.queryHash)\n\n    // Do not hydrate if an existing query exists with newer data\n    if (query) {\n      if (query.state.dataUpdatedAt < dehydratedQuery.state.dataUpdatedAt) {\n        query.setState(dehydratedQuery.state)\n      }\n      return\n    }\n\n    // Restore query\n    queryCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.queries,\n        queryKey: dehydratedQuery.queryKey,\n        queryHash: dehydratedQuery.queryHash,\n      },\n      dehydratedQuery.state\n    )\n  })\n}\n", "import React from 'react'\n\nimport { QueryClient } from '../core'\n\ndeclare global {\n  interface Window {\n    ReactQueryClientContext?: React.Context<QueryClient | undefined>\n  }\n}\n\nconst defaultContext = React.createContext<QueryClient | undefined>(undefined)\nconst QueryClientSharingContext = React.createContext<boolean>(false)\n\n// if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\nfunction getQueryClientContext(contextSharing: boolean) {\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext\n    }\n\n    return window.ReactQueryClientContext\n  }\n\n  return defaultContext\n}\n\nexport const useQueryClient = () => {\n  const queryClient = React.useContext(\n    getQueryClientContext(React.useContext(QueryClientSharingContext))\n  )\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return queryClient\n}\n\nexport interface QueryClientProviderProps {\n  client: QueryClient\n  contextSharing?: boolean\n  children?: React.ReactNode\n}\n\nexport const QueryClientProvider = ({\n  client,\n  contextSharing = false,\n  children,\n}: QueryClientProviderProps): JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  const Context = getQueryClientContext(contextSharing)\n\n  return (\n    <QueryClientSharingContext.Provider value={contextSharing}>\n      <Context.Provider value={client}>{children}</Context.Provider>\n    </QueryClientSharingContext.Provider>\n  )\n}\n", "import React from 'react'\n\n// CONTEXT\n\ninterface QueryErrorResetBoundaryValue {\n  clearReset: () => void\n  isReset: () => boolean\n  reset: () => void\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport interface QueryErrorResetBoundaryProps {\n  children:\n    | ((value: QueryErrorResetBoundaryValue) => React.ReactNode)\n    | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const value = React.useMemo(() => createValue(), [])\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function'\n        ? (children as Function)(value)\n        : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n", "import React from 'react'\n\nimport { notifyManager } from '../core/notifyManager'\nimport { QueryKey } from '../core/types'\nimport { parseFilterArgs, QueryFilters } from '../core/utils'\nimport { QueryClient } from '../core'\nimport { useQueryClient } from './QueryClientProvider'\n\nconst checkIsFetching = (\n  queryClient: QueryClient,\n  filters: QueryFilters,\n  isFetching: number,\n  setIsFetching: React.Dispatch<React.SetStateAction<number>>\n) => {\n  const newIsFetching = queryClient.isFetching(filters)\n  if (isFetching !== newIsFetching) {\n    setIsFetching(newIsFetching)\n  }\n}\n\nexport function useIsFetching(filters?: QueryFilters): number\nexport function useIsFetching(\n  queryKey?: QueryKey,\n  filters?: QueryFilters\n): number\nexport function useIsFetching(\n  arg1?: QueryKey | QueryFilters,\n  arg2?: QueryFilters\n): number {\n  const mountedRef = React.useRef(false)\n\n  const queryClient = useQueryClient()\n\n  const [filters] = parseFilterArgs(arg1, arg2)\n  const [isFetching, setIsFetching] = React.useState(\n    queryClient.isFetching(filters)\n  )\n\n  const filtersRef = React.useRef(filters)\n  filtersRef.current = filters\n  const isFetchingRef = React.useRef(isFetching)\n  isFetchingRef.current = isFetching\n\n  React.useEffect(() => {\n    mountedRef.current = true\n\n    checkIsFetching(\n      queryClient,\n      filtersRef.current,\n      isFetchingRef.current,\n      setIsFetching\n    )\n\n    const unsubscribe = queryClient.getQueryCache().subscribe(\n      notifyManager.batchCalls(() => {\n        if (mountedRef.current) {\n          checkIsFetching(\n            queryClient,\n            filtersRef.current,\n            isFetchingRef.current,\n            setIsFetching\n          )\n        }\n      })\n    )\n\n    return () => {\n      mountedRef.current = false\n      unsubscribe()\n    }\n  }, [queryClient])\n\n  return isFetching\n}\n", "export function shouldThrowError<T extends (...args: any[]) => boolean>(\n  suspense: boolean | undefined,\n  _useErrorBoundary: boolean | T | undefined,\n  params: Parameters<T>\n): boolean {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params)\n  }\n\n  // Allow useErrorBoundary to override suspense's throwing behavior\n  if (typeof _useErrorBoundary === 'boolean') return _useErrorBoundary\n\n  // If suspense is enabled default to throwing errors\n  return !!suspense\n}\n", "import React from 'react'\n\nimport { QueryKey } from '../core'\nimport { notifyManager } from '../core/notifyManager'\nimport { QueryObserver } from '../core/queryObserver'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport { useQueryClient } from './QueryClientProvider'\nimport { UseBaseQueryOptions } from './types'\nimport { shouldThrowError } from './utils'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  Observer: typeof QueryObserver\n) {\n  const mountedRef = React.useRef(false)\n  const [, forceUpdate] = React.useState(0)\n\n  const queryClient = useQueryClient()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const defaultedOptions = queryClient.defaultQueryObserverOptions(options)\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions.optimisticResults = true\n\n  // Include callbacks in batch renders\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(\n      defaultedOptions.onError\n    )\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(\n      defaultedOptions.onSuccess\n    )\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(\n      defaultedOptions.onSettled\n    )\n  }\n\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000\n    }\n\n    // Set cache time to 1 if the option has been set to 0\n    // when using suspense to prevent infinite loop of fetches\n    if (defaultedOptions.cacheTime === 0) {\n      defaultedOptions.cacheTime = 1\n    }\n  }\n\n  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      defaultedOptions.retryOnMount = false\n    }\n  }\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        queryClient,\n        defaultedOptions\n      )\n  )\n\n  let result = observer.getOptimisticResult(defaultedOptions)\n\n  React.useEffect(() => {\n    mountedRef.current = true\n\n    errorResetBoundary.clearReset()\n\n    const unsubscribe = observer.subscribe(\n      notifyManager.batchCalls(() => {\n        if (mountedRef.current) {\n          forceUpdate(x => x + 1)\n        }\n      })\n    )\n\n    // Update result to make sure we did not miss any query updates\n    // between creating the observer and subscribing to it.\n    observer.updateResult()\n\n    return () => {\n      mountedRef.current = false\n      unsubscribe()\n    }\n  }, [errorResetBoundary, observer])\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, { listeners: false })\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (defaultedOptions.suspense && result.isLoading) {\n    throw observer\n      .fetchOptimistic(defaultedOptions)\n      .then(({ data }) => {\n        defaultedOptions.onSuccess?.(data as TData)\n        defaultedOptions.onSettled?.(data, null)\n      })\n      .catch(error => {\n        errorResetBoundary.clearReset()\n        defaultedOptions.onError?.(error)\n        defaultedOptions.onSettled?.(undefined, error)\n      })\n  }\n\n  // Handle error boundary\n  if (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    shouldThrowError(\n      defaultedOptions.suspense,\n      defaultedOptions.useErrorBoundary,\n      [result.error, observer.getCurrentQuery()]\n    )\n  ) {\n    throw result.error\n  }\n\n  // Handle result property usage tracking\n  if (defaultedOptions.notifyOnChangeProps === 'tracked') {\n    result = observer.trackResult(result, defaultedOptions)\n  }\n\n  return result\n}\n", "import React from 'react'\n\nimport { hydrate, HydrateOptions } from '../core'\nimport { useQueryClient } from './QueryClientProvider'\n\nexport function useHydrate(state: unknown, options?: HydrateOptions) {\n  const queryClient = useQueryClient()\n\n  const optionsRef = React.useRef(options)\n  optionsRef.current = options\n\n  // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n  React.useMemo(() => {\n    if (state) {\n      hydrate(queryClient, state, optionsRef.current)\n    }\n  }, [queryClient, state])\n}\n\nexport interface HydrateProps {\n  state?: unknown\n  options?: HydrateOptions\n  children?: React.ReactNode\n}\n\nexport const Hydrate = ({ children, options, state }: HydrateProps) => {\n  useHydrate(state, options)\n  return children as React.ReactElement\n}\n", "import { QueryObserver } from '../core'\nimport { InfiniteQueryObserver } from '../core/infiniteQueryObserver'\nimport { QueryFunction, QueryKey } from '../core/types'\nimport { parseQueryArgs } from '../core/utils'\nimport { UseInfiniteQueryOptions, UseInfiniteQueryResult } from './types'\nimport { useBaseQuery } from './useBaseQuery'\n\n// HOOK\n\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  options: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  T<PERSON><PERSON><PERSON><PERSON>ey extends QueryKey = QueryKey\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey'\n  >\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey' | 'queryFn'\n  >\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  arg1:\n    | TQueryKey\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg3?: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >\n): UseInfiniteQueryResult<TData, TError> {\n  const options = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(\n    options,\n    InfiniteQueryObserver as typeof QueryObserver\n  ) as UseInfiniteQueryResult<TData, TError>\n}\n", "import React from 'react'\n\nimport { notifyManager } from '../core/notifyManager'\nimport { Mutation<PERSON>ey } from '../core/types'\nimport { MutationFilters, parseMutationFilterArgs } from '../core/utils'\nimport { useQueryClient } from './QueryClientProvider'\n\nexport function useIsMutating(filters?: MutationFilters): number\nexport function useIsMutating(\n  mutationKey?: MutationKey,\n  filters?: Omit<MutationFilters, 'mutationKey'>\n): number\nexport function useIsMutating(\n  arg1?: MutationKey | MutationFilters,\n  arg2?: Omit<MutationFilters, 'mutationKey'>\n): number {\n  const mountedRef = React.useRef(false)\n  const filters = parseMutationFilterArgs(arg1, arg2)\n\n  const queryClient = useQueryClient()\n\n  const [isMutating, setIsMutating] = React.useState(\n    queryClient.isMutating(filters)\n  )\n\n  const filtersRef = React.useRef(filters)\n  filtersRef.current = filters\n  const isMutatingRef = React.useRef(isMutating)\n  isMutatingRef.current = isMutating\n\n  React.useEffect(() => {\n    mountedRef.current = true\n\n    const unsubscribe = queryClient.getMutationCache().subscribe(\n      notifyManager.batchCalls(() => {\n        if (mountedRef.current) {\n          const newIsMutating = queryClient.isMutating(filtersRef.current)\n          if (isMutatingRef.current !== newIsMutating) {\n            setIsMutating(newIsMutating)\n          }\n        }\n      })\n    )\n\n    return () => {\n      mountedRef.current = false\n      unsubscribe()\n    }\n  }, [queryClient])\n\n  return isMutating\n}\n", "import React from 'react'\n\nimport { notifyManager } from '../core/notifyManager'\nimport { noop, parseMutationArgs } from '../core/utils'\nimport { MutationObserver } from '../core/mutationObserver'\nimport { useQueryClient } from './QueryClientProvider'\nimport {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\nimport { MutationFunction, MutationKey } from '../core/types'\nimport { shouldThrowError } from './utils'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n>(\n  mutationFn: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationFn'\n  >\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n>(\n  mutationKey: MutationKey,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey'\n  >\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n>(\n  mutationKey: MutationKey,\n  mutationFn?: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey' | 'mutationFn'\n  >\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n>(\n  arg1:\n    | MutationKey\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg2?:\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg3?: UseMutationOptions<TData, TError, TVariables, TContext>\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const mountedRef = React.useRef(false)\n  const [, forceUpdate] = React.useState(0)\n\n  const options = parseMutationArgs(arg1, arg2, arg3)\n  const queryClient = useQueryClient()\n\n  const obsRef = React.useRef<\n    MutationObserver<TData, TError, TVariables, TContext>\n  >()\n\n  if (!obsRef.current) {\n    obsRef.current = new MutationObserver(queryClient, options)\n  } else {\n    obsRef.current.setOptions(options)\n  }\n\n  const currentResult = obsRef.current.getCurrentResult()\n\n  React.useEffect(() => {\n    mountedRef.current = true\n\n    const unsubscribe = obsRef.current!.subscribe(\n      notifyManager.batchCalls(() => {\n        if (mountedRef.current) {\n          forceUpdate(x => x + 1)\n        }\n      })\n    )\n    return () => {\n      mountedRef.current = false\n      unsubscribe()\n    }\n  }, [])\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >((variables, mutateOptions) => {\n    obsRef.current!.mutate(variables, mutateOptions).catch(noop)\n  }, [])\n\n  if (\n    currentResult.error &&\n    shouldThrowError(undefined, obsRef.current.options.useErrorBoundary, [\n      currentResult.error,\n    ])\n  ) {\n    throw currentResult.error\n  }\n\n  return { ...currentResult, mutate, mutateAsync: currentResult.mutate }\n}\n", "import React, { useMemo } from 'react'\nimport { QueryFunction } from '../core/types'\n\nimport { notifyManager } from '../core/notifyManager'\nimport { QueriesObserver } from '../core/queriesObserver'\nimport { useQueryClient } from './QueryClientProvider'\nimport { UseQueryOptions, UseQueryResult } from './types'\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\ntype GetOptions<T extends any> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptions<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryOptions<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryOptions<unknown, TError, TData>\n    : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n    T extends [infer TQueryFnData, infer TError, infer TData]\n    ? UseQueryOptions<TQueryFnData, TError, TData>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryOptions<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryOptions<TQueryFnData>\n    : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryOptions<TQueryFnData, unknown, TData, TQueryKey>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey> }\n    ? UseQueryOptions<TQueryFnData, unknown, TQueryFnData, TQueryKey>\n    : // Fallback\n      UseQueryOptions\n\ntype GetResults<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryResult<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryResult<TData, TError>\n    : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n    T extends [any, infer TError, infer TData]\n    ? UseQueryResult<TData, TError>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryResult<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryResult<TQueryFnData>\n    : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<any, any>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryResult<TData>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, any> }\n    ? UseQueryResult<TQueryFnData>\n    : // Fallback\n      UseQueryResult\n\n/**\n * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type QueriesOptions<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = []\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryOptions[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetOptions<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesOptions<[...Tail], [...Result, GetOptions<Head>], [...Depth, 1]>\n  : unknown[] extends T\n  ? T\n  : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n  // use this to infer the param types in the case of Array.map() argument\n  T extends UseQueryOptions<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      infer TQueryKey\n    >[]\n  ? UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>[]\n  : // Fallback\n    UseQueryOptions[]\n\n/**\n * QueriesResults reducer recursively maps type param to results\n */\nexport type QueriesResults<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = []\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryResult[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetResults<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesResults<[...Tail], [...Result, GetResults<Head>], [...Depth, 1]>\n  : T extends UseQueryOptions<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      any\n    >[]\n  ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n    UseQueryResult<unknown extends TData ? TQueryFnData : TData, TError>[]\n  : // Fallback\n    UseQueryResult[]\n\nexport function useQueries<T extends any[]>(\n  queries: readonly [...QueriesOptions<T>]\n): QueriesResults<T> {\n  const mountedRef = React.useRef(false)\n  const [, forceUpdate] = React.useState(0)\n\n  const queryClient = useQueryClient()\n\n  const defaultedQueries = useMemo(\n    () =>\n      queries.map(options => {\n        const defaultedOptions = queryClient.defaultQueryObserverOptions(\n          options\n        )\n\n        // Make sure the results are already in fetching state before subscribing or updating options\n        defaultedOptions.optimisticResults = true\n\n        return defaultedOptions\n      }),\n    [queries, queryClient]\n  )\n\n  const [observer] = React.useState(\n    () => new QueriesObserver(queryClient, defaultedQueries)\n  )\n\n  const result = observer.getOptimisticResult(defaultedQueries)\n\n  React.useEffect(() => {\n    mountedRef.current = true\n\n    const unsubscribe = observer.subscribe(\n      notifyManager.batchCalls(() => {\n        if (mountedRef.current) {\n          forceUpdate(x => x + 1)\n        }\n      })\n    )\n\n    return () => {\n      mountedRef.current = false\n      unsubscribe()\n    }\n  }, [observer])\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, { listeners: false })\n  }, [defaultedQueries, observer])\n\n  return result as QueriesResults<T>\n}\n", "import { QueryObserver } from '../core'\nimport { QueryFunction, QueryKey } from '../core/types'\nimport { parseQueryArgs } from '../core/utils'\nimport { UseQueryOptions, UseQueryResult } from './types'\nimport { useBaseQuery } from './useBaseQuery'\n\n// HOOK\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey'\n  >\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn'\n  >\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  arg1: TQueryKey | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg3?: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n): UseQueryResult<TData, TError> {\n  const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(parsedOptions, QueryObserver)\n}\n"], "names": ["_inherits<PERSON><PERSON>e", "subClass", "superClass", "prototype", "Object", "create", "constructor", "__proto__", "Subscribable", "listeners", "subscribe", "listener", "callback", "push", "onSubscribe", "_this", "filter", "x", "onUnsubscribe", "hasListeners", "this", "length", "_extends", "assign", "target", "i", "arguments", "source", "key", "hasOwnProperty", "call", "apply", "isServer", "window", "noop", "isValidTimeout", "value", "Infinity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "difference", "array1", "array2", "indexOf", "timeUntilStale", "updatedAt", "staleTime", "Math", "max", "Date", "now", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg1", "arg2", "arg3", "is<PERSON>uery<PERSON>ey", "query<PERSON><PERSON>", "queryFn", "parseFilter<PERSON><PERSON>s", "matchQuery", "filters", "query", "active", "exact", "fetching", "inactive", "predicate", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "queryS<PERSON>us<PERSON><PERSON>er", "mapQueryStatusFilter", "isActive", "isStale", "isFetching", "matchMutation", "mutation", "<PERSON><PERSON><PERSON>", "hashQuery<PERSON>ey", "state", "status", "queryKeyHashFn", "asArray", "JSON", "stringify", "_", "val", "isPlainObject", "keys", "sort", "reduce", "result", "a", "b", "partialDeepEqual", "some", "replaceEqualDeep", "array", "aSize", "bItems", "bSize", "copy", "equalItems", "o", "hasObjectPrototype", "ctor", "prot", "toString", "scheduleMicrotask", "Promise", "resolve", "then", "catch", "error", "setTimeout", "getAbortController", "AbortController", "focusManager", "setup", "onFocus", "_window", "addEventListener", "removeEventListener", "cleanup", "setEventListener", "undefined", "focused", "_this2", "setFocused", "for<PERSON>ach", "isFocused", "document", "includes", "visibilityState", "onlineManager", "onOnline", "online", "setOnline", "isOnline", "navigator", "onLine", "defaultRetryDelay", "failureCount", "min", "isCancelable", "cancel", "CancelledError", "revert", "silent", "isCancelledError", "<PERSON><PERSON><PERSON>", "config", "cancelFn", "continueFn", "promiseResolve", "promiseReject", "cancelRetry", "abort", "cancelOptions", "continueRetry", "continue", "isPaused", "isResolved", "isTransportCancelable", "promise", "outerResolve", "outerReject", "onSuccess", "reject", "onError", "run", "promiseOrValue", "fn", "timeout", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "continueResolve", "onPause", "onContinue", "notify<PERSON><PERSON>ger", "queue", "transactions", "notifyFn", "batchNotifyFn", "batch", "flush", "schedule", "batchCalls", "args", "_this3", "setNotifyFunction", "setBatchNotifyFunction", "logger", "console", "<PERSON><PERSON><PERSON><PERSON>", "Query", "abortSignalConsumed", "hadObservers", "defaultOptions", "setOptions", "observers", "cache", "initialState", "getDefaultState", "meta", "scheduleGc", "cacheTime", "setDefaultOptions", "clearGcTimeout", "gcTimeout", "optionalRemove", "clearTimeout", "remove", "setData", "updater", "prevData", "data", "input", "functionalUpdate", "isDataEqual", "_this$options$isDataE", "structuralSharing", "dispatch", "type", "dataUpdatedAt", "setState", "setStateOptions", "retryer", "destroy", "reset", "observer", "enabled", "isInvalidated", "getCurrentResult", "isStaleByTime", "find", "shouldFetchOnWindowFocus", "refetch", "shouldFetchOnReconnect", "addObserver", "notify", "removeObserver", "getObserversCount", "invalidate", "fetch", "fetchOptions", "cancelRefetch", "abortController", "queryFnContext", "pageParam", "defineProperty", "enumerable", "get", "signal", "context", "fetchFn", "behavior", "_this$options$behavio", "onFetch", "revertState", "fetchMeta", "_context$fetchOptions", "_context$fetchOptions2", "_abortController$abor", "bind", "action", "reducer", "onQueryUpdate", "initialData", "initialDataUpdatedAt", "hasData", "dataUpdateCount", "errorUpdateCount", "errorUpdatedAt", "fetchFailureCount", "Query<PERSON>ache", "queries", "queriesMap", "build", "client", "defaultQueryOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "queryInMap", "clear", "getAll", "findAll", "event", "_this4", "_this5", "Mutation", "mutationId", "mutationCache", "execute", "restored", "variables", "onMutate", "executeMutation", "onSettled", "mutationFn", "onMutationUpdate", "MutationCache", "mutations", "defaultMutationOptions", "getMutationDefaults", "resumePausedMutations", "pausedMutations", "infiniteQueryBehavior", "refetchPage", "fetchMore", "_context$fetchOptions3", "_context$fetchOptions4", "isFetchingNextPage", "direction", "isFetchingPreviousPage", "oldPages", "pages", "oldPageParams", "pageParams", "abortSignal", "newPageParams", "cancelled", "buildNewPages", "param", "page", "previous", "fetchPage", "manual", "queryFnResult", "getNextPageParam", "getPreviousPageParam", "shouldFetchFirstPage", "finalPromise", "hasNextPage", "nextPageParam", "hasPreviousPage", "previousPageParam", "QueryClient", "queryCache", "queryDefaults", "mutationDefaults", "mount", "unsubscribeFocus", "unsubscribeOnline", "unmount", "isMutating", "getQueryData", "_this$queryCache$find", "getQueriesData", "query<PERSON>eyOrFilters", "get<PERSON><PERSON><PERSON><PERSON>ache", "map", "setQueryData", "parsedOptions", "defaultedOptions", "setQueriesData", "getQueryState", "_this$queryCache$find2", "removeQueries", "resetQueries", "refetchFilters", "refetchQueries", "cancelQueries", "promises", "all", "invalidateQueries", "refetchActive", "refetchInactive", "_this6", "throwOnError", "<PERSON><PERSON><PERSON><PERSON>", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "prefetchInfiniteQuery", "cancelMutations", "_this7", "getMutationCache", "getDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_this$queryDefaults$f", "setMutationDefaults", "_this$mutationDefault", "_defaulted", "defaultQueryObserverOptions", "QueryObserver", "trackedProps", "selectError", "bindMethods", "<PERSON><PERSON><PERSON><PERSON>", "shouldFetchOnMount", "executeFetch", "updateTimers", "shouldFetchOn", "refetchOnReconnect", "refetchOnWindowFocus", "clearTimers", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "Error", "updateQuery", "mounted", "shouldFetchOptionally", "updateResult", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "createResult", "currentResult", "trackResult", "trackedResult", "trackProp", "configurable", "useErrorBoundary", "suspense", "getNextResult", "unsubscribe", "isError", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchOptimistic", "clearStaleTimeout", "staleTimeoutId", "refetchInterval", "nextInterval", "clearRefetchInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "clearInterval", "prevResult", "prevResultState", "currentResultState", "prevResultOptions", "currentResultOptions", "query<PERSON>hange", "queryInitialState", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "isPreviousData", "isPlaceholderData", "optimisticResults", "fetchOnMount", "fetchOptionally", "keepPreviousData", "isSuccess", "select", "selectFn", "selectResult", "placeholderData", "isLoading", "isIdle", "isFetched", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isRefetchError", "shouldNotifyListeners", "notifyOnChangeProps", "notifyOnChangePropsExclusions", "includedProps", "<PERSON><PERSON><PERSON>", "changed", "isIncluded", "isExcluded", "shallowEqualObjects", "defaultNotifyOptions", "_this8", "retryOnMount", "shouldLoadOnMount", "refetchOnMount", "field", "QueriesObserver", "observersMap", "setQueries", "onUpdate", "updateObservers", "findMatchingObservers", "match", "defaultedQueryOptions", "prevObservers", "matchingObservers", "flatMap", "matchedQueryHashes", "unmatchedQueries", "unmatchedObservers", "prevObserver", "newOrReusedObservers", "index", "previouslyUsedObserver", "getObserver", "concat", "currentObserver", "newObserverMatches", "newObservers", "newObserversMap", "fromEntries", "newResult", "hasIndexChange", "slice", "replaceAt", "InfiniteQueryObserver", "_QueryObserver", "fetchNextPage", "fetchPreviousPage", "_state$data", "_state$data2", "MutationObserver", "mutate", "currentMutation", "mutateOptions", "defaultShouldDehydrateMutation", "defaultShouldDehydrateQuery", "hydrate", "dehydratedState", "dehydratedMutation", "_options$defaultOptio", "dehydrated<PERSON><PERSON>y", "_options$defaultOptio2", "defaultContext", "React", "createContext", "QueryClientSharingContext", "getQueryClientContext", "contextSharing", "ReactQueryClientContext", "useQueryClient", "queryClient", "useContext", "createValue", "isReset", "clear<PERSON><PERSON>t", "QueryErrorResetBoundaryContext", "useQueryErrorResetBoundary", "checkIsFetching", "setIsFetching", "newIsFetching", "shouldThrowError", "_useErrorBoundary", "params", "useBaseQuery", "Observer", "mountedRef", "useRef", "forceUpdate", "useState", "errorResetBoundary", "useEffect", "current", "useHydrate", "optionsRef", "useMemo", "children", "Context", "Provider", "dehydrateMutations", "shouldDehydrateMutation", "dehydrateMutation", "dehydrateQueries", "shouldDehydrateQuery", "dehydrate<PERSON><PERSON>y", "<PERSON><PERSON><PERSON><PERSON>", "filtersRef", "isFetchingRef", "parseMutationFilterArgs", "setIsMutating", "isMutatingRef", "newIsMutating", "parseMutationArgs", "obsRef", "useCallback", "mutateAsync", "defaultedQueries"], "mappings": "2QAAe,SAASA,EAAeC,EAAUC,GAC/CD,EAASE,UAAYC,OAAOC,OAAOH,EAAWC,WAC9CF,EAASE,UAAUG,YAAcL,EACjCA,EAASM,UAAYL,MCDVM,+BAIJC,UAAY,8BAGnBC,UAAA,SAAUC,cACFC,EAAWD,GAAa,yBAEzBF,UAAUI,KAAKD,QAEfE,cAEE,WACLC,EAAKN,UAAYM,EAAKN,UAAUO,QAAO,SAAAC,UAAKA,IAAML,KAClDG,EAAKG,oBAITC,aAAA,kBACSC,KAAKX,UAAUY,OAAS,KAGvBP,YAAV,eAIUI,cAAV,kBC9Ba,SAASI,WACtBA,EAAWlB,OAAOmB,QAAU,SAAUC,OAC/B,IAAIC,EAAI,EAAGA,EAAIC,UAAUL,OAAQI,IAAK,KACrCE,EAASD,UAAUD,OAElB,IAAIG,KAAOD,EACVvB,OAAOD,UAAU0B,eAAeC,KAAKH,EAAQC,KAC/CJ,EAAOI,GAAOD,EAAOC,WAKpBJ,IAGOO,MAAMX,KAAMM,WC2DvB,IAAMM,EAA6B,oBAAXC,OAExB,SAASC,KAaT,SAASC,EAAeC,SACL,iBAAVA,GAAsBA,GAAS,GAAKA,IAAUC,EAAAA,EAGvD,SAASC,EACdF,UAEQG,MAAMC,QAAQJ,GAClBA,EACC,CAACA,GAGD,SAASK,EAAcC,EAAaC,UAClCD,EAAO1B,QAAO,SAAAC,UAA4B,IAAvB0B,EAAOC,QAAQ3B,MASpC,SAAS4B,EAAeC,EAAmBC,UACzCC,KAAKC,IAAIH,GAAaC,GAAa,GAAKG,KAAKC,MAAO,GAGtD,SAASC,EAIdC,EACAC,EACAC,UAEKC,EAAWH,GAII,mBAATC,OACGC,GAAME,SAAUJ,EAAMK,QAASJ,SAGjCA,GAAMG,SAAUJ,IAPnBA,EA+BJ,SAASM,EAIdN,EACAC,EACAC,UAEQC,EAAWH,GACf,MAAMC,GAAMG,SAAUJ,IAAQE,GAC9B,CAACF,GAAQ,GAAIC,GA6BZ,SAASM,EACdC,EACAC,OAGEC,EAOEF,EAPFE,OACAC,EAMEH,EANFG,MACAC,EAKEJ,EALFI,SACAC,EAIEL,EAJFK,SACAC,EAGEN,EAHFM,UACAV,EAEEI,EAFFJ,SACAW,EACEP,EADFO,SAGEZ,EAAWC,MACTO,MACEF,EAAMO,YAAcC,EAAsBb,EAAUK,EAAMS,gBACrD,OAEJ,IAAKC,EAAgBV,EAAML,SAAUA,UACnC,MAILgB,EA3CD,SACLV,EACAG,UAGc,IAAXH,IAAgC,IAAbG,GACT,MAAVH,GAA8B,MAAZG,EAEZ,OACa,IAAXH,IAAiC,IAAbG,EACtB,cAIUH,EAAAA,GAAWG,GACV,SAAW,WA4BLQ,CAAqBX,EAAQG,MAE7B,SAAtBO,SACK,EACF,GAA0B,QAAtBA,EAA6B,KAChCE,EAAWb,EAAMa,cACG,WAAtBF,IAAmCE,SAC9B,KAEiB,aAAtBF,GAAoCE,SAC/B,SAIU,kBAAVP,GAAuBN,EAAMc,YAAcR,MAI9B,kBAAbH,GAA0BH,EAAMe,eAAiBZ,MAIxDE,IAAcA,EAAUL,KAOvB,SAASgB,EACdjB,EACAkB,OAEQf,EAA4CH,EAA5CG,MAAOC,EAAqCJ,EAArCI,SAAUE,EAA2BN,EAA3BM,UAAWa,EAAgBnB,EAAhBmB,eAChCxB,EAAWwB,GAAc,KACtBD,EAASR,QAAQS,mBACb,KAELhB,MAEAiB,EAAaF,EAASR,QAAQS,eAAiBC,EAAaD,UAErD,OAEJ,IAAKR,EAAgBO,EAASR,QAAQS,YAAaA,UACjD,SAKW,kBAAbf,GACoB,YAA1Bc,EAASG,MAAMC,SAA0BlB,MAKxCE,IAAcA,EAAUY,IAOvB,SAAST,EACdb,EACAc,iBAEeA,SAAAA,EAASa,iBAAkBH,GAC5BxB,GAMT,SAASwB,EAAaxB,OAQGrB,EAPxBiD,EAAU/C,EAAoBmB,UAONrB,EANPiD,EAOhBC,KAAKC,UAAUnD,GAAO,SAACoD,EAAGC,UAC/BC,EAAcD,GACVrF,OAAOuF,KAAKF,GACTG,OACAC,QAAO,SAACC,EAAQlE,UACfkE,EAAOlE,GAAO6D,EAAI7D,GACXkE,IACN,IACLL,KAOD,SAASjB,EAAgBuB,EAAaC,UAOtC,SAASC,EAAiBF,EAAQC,MACnCD,IAAMC,SACD,YAGED,UAAaC,SACf,KAGLD,GAAKC,GAAkB,iBAAND,GAA+B,iBAANC,SACpC5F,OAAOuF,KAAKK,GAAGE,MAAK,SAAAtE,UAAQqE,EAAiBF,EAAEnE,GAAMoE,EAAEpE,cAG1D,EAnBAqE,CAAiB3D,EAAoByD,GAAIzD,EAAoB0D,IA4B/D,SAASG,EAAiBJ,EAAQC,MACnCD,IAAMC,SACDD,MAGHK,EAAQ7D,MAAMC,QAAQuD,IAAMxD,MAAMC,QAAQwD,MAE5CI,GAAUV,EAAcK,IAAML,EAAcM,GAAK,SAC7CK,EAAQD,EAAQL,EAAE1E,OAASjB,OAAOuF,KAAKI,GAAG1E,OAC1CiF,EAASF,EAAQJ,EAAI5F,OAAOuF,KAAKK,GACjCO,EAAQD,EAAOjF,OACfmF,EAAYJ,EAAQ,GAAK,GAE3BK,EAAa,EAERhF,EAAI,EAAGA,EAAI8E,EAAO9E,IAAK,KACxBG,EAAMwE,EAAQ3E,EAAI6E,EAAO7E,GAC/B+E,EAAK5E,GAAOuE,EAAiBJ,EAAEnE,GAAMoE,EAAEpE,IACnC4E,EAAK5E,KAASmE,EAAEnE,IAClB6E,WAIGJ,IAAUE,GAASE,IAAeJ,EAAQN,EAAIS,SAGhDR,EAqBF,SAASN,EAAcgB,OACvBC,EAAmBD,UACf,MAIHE,EAAOF,EAAEpG,oBACK,IAATsG,SACF,MAIHC,EAAOD,EAAKzG,kBACbwG,EAAmBE,MAKnBA,EAAKhF,eAAe,iBAQ3B,SAAS8E,EAAmBD,SACmB,oBAAtCtG,OAAOD,UAAU2G,SAAShF,KAAK4E,GAGjC,SAASlD,EAAWpB,SACD,iBAAVA,GAAsBG,MAAMC,QAAQJ,GAiB7C,SAAS2E,EAAkBnG,GAChCoG,QAAQC,UACLC,KAAKtG,GACLuG,OAAM,SAAAC,UACLC,YAAW,iBACHD,QAKP,SAASE,OACiB,mBAApBC,uBACF,IAAIA,oBC9WFC,EAAe,gEAvEnBC,MAAQ,SAAAC,aACN1F,aAAYC,eAAA0F,EAAQC,kBAAkB,KACnCjH,EAAW,kBAAM+G,YAEvBzF,OAAO2F,iBAAiB,mBAAoBjH,GAAU,GACtDsB,OAAO2F,iBAAiB,QAASjH,GAAU,GAEpC,WAELsB,OAAO4F,oBAAoB,mBAAoBlH,GAC/CsB,OAAO4F,oBAAoB,QAASlH,0CAMlCG,YAAV,WACOM,KAAK0G,cACHC,iBAAiB3G,KAAKqG,UAIrBvG,cAAV,iBACOE,KAAKD,+BACH2G,4BACAA,aAAUE,MAInBD,iBAAA,SAAiBN,qBACVA,MAAQA,gBACRK,4BACAA,QAAUL,GAAM,SAAAQ,GACI,kBAAZA,EACTC,EAAKC,WAAWF,GAEhBC,EAAKR,gBAKXS,WAAA,SAAWF,QACJA,QAAUA,EAEXA,QACGP,aAITA,QAAA,gBACOjH,UAAU2H,SAAQ,SAAAzH,GACrBA,UAIJ0H,UAAA,iBAC8B,kBAAjBjH,KAAK6G,QACP7G,KAAK6G,QAIU,oBAAbK,UAIJ,MAACN,EAAW,UAAW,aAAaO,SACzCD,SAASE,qBA1EmBhI,IC+ErBiI,EAAgB,gEAvEpBhB,MAAQ,SAAAiB,aACN1G,aAAYC,eAAA0F,EAAQC,kBAAkB,KACnCjH,EAAW,kBAAM+H,YAEvBzG,OAAO2F,iBAAiB,SAAUjH,GAAU,GAC5CsB,OAAO2F,iBAAiB,UAAWjH,GAAU,GAEtC,WAELsB,OAAO4F,oBAAoB,SAAUlH,GACrCsB,OAAO4F,oBAAoB,UAAWlH,0CAMpCG,YAAV,WACOM,KAAK0G,cACHC,iBAAiB3G,KAAKqG,UAIrBvG,cAAV,iBACOE,KAAKD,+BACH2G,4BACAA,aAAUE,MAInBD,iBAAA,SAAiBN,qBACVA,MAAQA,gBACRK,4BACAA,QAAUL,GAAM,SAACkB,GACE,kBAAXA,EACTT,EAAKU,UAAUD,GAEfT,EAAKQ,iBAKXE,UAAA,SAAUD,QACHA,OAASA,EAEVA,QACGD,cAITA,SAAA,gBACOjI,UAAU2H,SAAQ,SAAAzH,GACrBA,UAIJkI,SAAA,iBAC6B,kBAAhBzH,KAAKuH,OACPvH,KAAKuH,OAIS,oBAAdG,gBACqB,IAArBA,UAAUC,QAKZD,UAAUC,WA3EcvI,IC0BnC,SAASwI,EAAkBC,UAClBjG,KAAKkG,IAAI,aAAO,EAAKD,GAAc,KAOrC,SAASE,EAAa/G,SACK,yBAAlBA,SAAAA,EAAOgH,YAGVC,EAGX,SAAY9E,QACL+E,aAAS/E,SAAAA,EAAS+E,YAClBC,aAAShF,SAAAA,EAASgF,QAIpB,SAASC,EAAiBpH,UACxBA,aAAiBiH,MAKbI,EAaX,SAAYC,OAENC,EACAC,EACAC,EACAC,SAJAC,GAAc,OAMbC,MAAQN,EAAOM,WACfZ,OAAS,SAAAa,gBAAiBN,SAAAA,EAAWM,SACrCF,YAAc,WACjBA,GAAc,QAEXG,cAAgB,WACnBH,GAAc,QAEXI,SAAW,wBAAMP,SAAAA,UACjBX,aAAe,OACfmB,UAAW,OACXC,YAAa,OACbC,uBAAwB,OACxBC,QAAU,IAAIvD,SAAe,SAACwD,EAAcC,GAC/CZ,EAAiBW,EACjBV,EAAgBW,SAGZxD,EAAU,SAAC7E,GACVrB,EAAKsJ,aACRtJ,EAAKsJ,YAAa,QAClBX,EAAOgB,WAAPhB,EAAOgB,UAAYtI,SACnBwH,GAAAA,IACAC,EAAezH,KAIbuI,EAAS,SAACvI,GACTrB,EAAKsJ,aACRtJ,EAAKsJ,YAAa,QAClBX,EAAOkB,SAAPlB,EAAOkB,QAAUxI,SACjBwH,GAAAA,IACAE,EAAc1H,MAiBN,SAANyI,QAEA9J,EAAKsJ,gBAILS,MAIFA,EAAiBpB,EAAOqB,KACxB,MAAO3D,GACP0D,EAAiB9D,QAAQ2D,OAAOvD,GAIlCuC,EAAW,SAAAM,OACJlJ,EAAKsJ,aACRM,EAAO,IAAItB,EAAeY,UAE1BlJ,EAAKiJ,OAALjJ,EAAKiJ,QAGDb,EAAa2B,QAEbA,EAAe1B,SACf,YAMRrI,EAAKuJ,sBAAwBnB,EAAa2B,GAE1C9D,QAAQC,QAAQ6D,GACb5D,KAAKD,GACLE,OAAM,SAAAC,eAEDrG,EAAKsJ,gBHuQGW,EGlQNC,WAAQvB,EAAOuB,SAAS,EACxBC,WAAaxB,EAAOwB,cAAclC,EAClCmC,EACkB,mBAAfD,EACHA,EAAWnK,EAAKkI,aAAc7B,GAC9B8D,EACAE,GACM,IAAVH,GACkB,iBAAVA,GAAsBlK,EAAKkI,aAAegC,GAChC,mBAAVA,GAAwBA,EAAMlK,EAAKkI,aAAc7B,OAEvD2C,GAAgBqB,EAMpBrK,EAAKkI,qBAGLS,EAAO2B,QAAP3B,EAAO2B,OAAStK,EAAKkI,aAAc7B,IH8OvB4D,EG3ONG,EH4OP,IAAInE,SAAQ,SAAAC,GACjBI,WAAWJ,EAAS+D,OG3OX9D,MAAK,eACCM,EAAaa,cAAgBI,EAAcI,kBAjFjD,IAAI7B,SAAQ,SAAAsE,GACjB1B,EAAa0B,EACbvK,EAAKqJ,UAAW,QAChBV,EAAO6B,SAAP7B,EAAO6B,aACNrE,MAAK,WACN0C,OAAa5B,EACbjH,EAAKqJ,UAAW,QAChBV,EAAO8B,YAAP9B,EAAO8B,mBA8EFtE,MAAK,WACA6C,EACFY,EAAOvD,GAEPyD,YArBJF,EAAOvD,QA4BfyD,IClHSY,EAAgB,iCAhFpBC,MAAQ,QACRC,aAAe,OAEfC,SAAW,SAAChL,GACfA,UAGGiL,cAAgB,SAACjL,GACpBA,gCAIJkL,MAAA,SAASlL,OACHkF,OACC6F,mBAEH7F,EAASlF,iBAEJ+K,eACAvK,KAAKuK,mBACHI,eAGFjG,KAGTkG,SAAA,SAASpL,cACHQ,KAAKuK,kBACFD,MAAM7K,KAAKD,GAEhBmG,GAAkB,WAChBhG,EAAK6K,SAAShL,SAQpBqL,WAAA,SAA+BrL,qBACrB,sCAAIsL,2BAAAA,kBACVhE,EAAK8D,UAAS,WACZpL,eAAYsL,UAKlBH,MAAA,sBACQL,EAAQtK,KAAKsK,WACdA,MAAQ,GACTA,EAAMrK,QACR0F,GAAkB,WAChBoF,EAAKN,eAAc,WACjBH,EAAMtD,SAAQ,SAAAxH,GACZuL,EAAKP,SAAShL,eAWxBwL,kBAAA,SAAkBrB,QACXa,SAAWb,KAOlBsB,uBAAA,SAAuBtB,QAChBc,cAAgBd,SCjFrBuB,EAAiBC,QAEd,SAASC,WACPF,MCgIIG,wBAwBC/C,QACLgD,qBAAsB,OACtBC,cAAe,OACfC,eAAiBlD,EAAOkD,oBACxBC,WAAWnD,EAAOnF,cAClBuI,UAAY,QACZC,MAAQrD,EAAOqD,WACftJ,SAAWiG,EAAOjG,cAClBY,UAAYqF,EAAOrF,eACnB2I,aAAetD,EAAOxE,OAAS9D,KAAK6L,gBAAgB7L,KAAKmD,cACzDW,MAAQ9D,KAAK4L,kBACbE,KAAOxD,EAAOwD,UACdC,wCAGCN,WAAR,SACEtI,cAEKA,aAAenD,KAAKwL,eAAmBrI,QAEvC2I,WAAO3I,SAAAA,EAAS2I,UAGhBE,UAAYpK,KAAKC,IACpB7B,KAAKgM,WAAa,WAClBhM,KAAKmD,QAAQ6I,aAAa,QAI9BC,kBAAA,SACE9I,QAEKqI,eAAiBrI,KAGhB4I,WAAR,2BACOG,iBAEDnL,EAAef,KAAKgM,kBACjBG,UAAYlG,YAAW,WAC1BtG,EAAKyM,mBACJpM,KAAKgM,eAIJE,eAAR,WACEG,aAAarM,KAAKmM,gBACbA,eAAYvF,KAGXwF,eAAR,WACOpM,KAAK0L,UAAUzL,SACdD,KAAK8D,MAAML,WACTzD,KAAKuL,mBACFQ,kBAGFJ,MAAMW,OAAOtM,UAKxBuM,QAAA,SACEC,EACArJ,WAEMsJ,EAAWzM,KAAK8D,MAAM4I,KAGxBA,EN5JD,SACLF,EACAG,SAE0B,mBAAZH,EACTA,EAAgDG,GACjDH,EMsJSI,CAAiBJ,EAASC,2BAG5BtJ,SAAQ0J,oBAAbC,SAA2BL,EAAUC,IACvCA,EAAOD,GACqC,IAAnCzM,KAAKmD,QAAQ4J,oBAEtBL,EAAO3H,EAAiB0H,EAAUC,SAI/BM,SAAS,CACZN,KAAAA,EACAO,KAAM,UACNC,oBAAe/J,SAAAA,EAASzB,YAGnBgL,KAGTS,SAAA,SACErJ,EACAsJ,QAEKJ,SAAS,CAAEC,KAAM,WAAYnJ,MAAAA,EAAOsJ,gBAAAA,OAG3CpF,OAAA,SAAO7E,SACCgG,EAAUnJ,KAAKmJ,6BAChBkE,YAASrF,OAAO7E,GACdgG,EAAUA,EAAQrD,KAAKhF,GAAMiF,MAAMjF,GAAQ8E,QAAQC,aAG5DyH,QAAA,gBACOpB,sBACAlE,OAAO,CAAEG,QAAQ,OAGxBoF,MAAA,gBACOD,eACAH,SAASnN,KAAK4L,iBAGrBrI,SAAA,kBACSvD,KAAK0L,UAAU5G,MAAK,SAAA0I,UAAyC,IAA7BA,EAASrK,QAAQsK,cAG1DhK,WAAA,kBACSzD,KAAK8D,MAAML,cAGpBD,QAAA,kBAEIxD,KAAK8D,MAAM4J,gBACV1N,KAAK8D,MAAMoJ,eACZlN,KAAK0L,UAAU5G,MAAK,SAAA0I,UAAYA,EAASG,mBAAmBnK,cAIhEoK,cAAA,SAAcjM,mBAAAA,IAAAA,EAAY,GAEtB3B,KAAK8D,MAAM4J,gBACV1N,KAAK8D,MAAMoJ,gBACXzL,EAAezB,KAAK8D,MAAMoJ,cAAevL,MAI9C2E,QAAA,iBACQkH,EAAWxN,KAAK0L,UAAUmC,MAAK,SAAAhO,UAAKA,EAAEiO,8BAExCN,GACFA,EAASO,wBAINV,YAAStE,cAGhBzB,SAAA,iBACQkG,EAAWxN,KAAK0L,UAAUmC,MAAK,SAAAhO,UAAKA,EAAEmO,4BAExCR,GACFA,EAASO,wBAINV,YAAStE,cAGhBkF,YAAA,SAAYT,IACgC,IAAtCxN,KAAK0L,UAAUlK,QAAQgM,UACpB9B,UAAUjM,KAAK+N,QACfjC,cAAe,OAGfW,sBAEAP,MAAMuC,OAAO,CAAEjB,KAAM,gBAAiBvK,MAAO1C,KAAMwN,SAAAA,QAI5DW,eAAA,SAAeX,IAC6B,IAAtCxN,KAAK0L,UAAUlK,QAAQgM,UACpB9B,UAAY1L,KAAK0L,UAAU9L,QAAO,SAAAC,UAAKA,IAAM2N,KAE7CxN,KAAK0L,UAAUzL,SAGdD,KAAKqN,UACHrN,KAAKqN,QAAQnE,uBAAyBlJ,KAAKsL,yBACxC+B,QAAQrF,OAAO,CAAEE,QAAQ,SAEzBmF,QAAQ1E,eAIb3I,KAAKgM,eACFD,kBAEAJ,MAAMW,OAAOtM,YAIjB2L,MAAMuC,OAAO,CAAEjB,KAAM,kBAAmBvK,MAAO1C,KAAMwN,SAAAA,QAI9DY,kBAAA,kBACSpO,KAAK0L,UAAUzL,UAGxBoO,WAAA,WACOrO,KAAK8D,MAAM4J,oBACTV,SAAS,CAAEC,KAAM,kBAI1BqB,MAAA,SACEnL,EACAoL,uBAEIvO,KAAK8D,MAAML,cACTzD,KAAK8D,MAAMoJ,sBAAiBqB,SAAAA,EAAcC,oBAEvCxG,OAAO,CAAEG,QAAQ,SACjB,GAAInI,KAAKmJ,QAAS,4BAElBkE,YAASvE,gBAEP9I,KAAKmJ,WAKZhG,QACGsI,WAAWtI,IAKbnD,KAAKmD,QAAQb,QAAS,KACnBkL,EAAWxN,KAAK0L,UAAUmC,MAAK,SAAAhO,UAAKA,EAAEsD,QAAQb,WAChDkL,QACG/B,WAAW+B,EAASrK,aAIvBd,EAAWnB,EAAoBlB,KAAKqC,UACpCoM,EAAkBvI,IAGlBwI,EAAkD,CACtDrM,SAAAA,EACAsM,eAAW/H,EACXkF,KAAM9L,KAAK8L,MAGb9M,OAAO4P,eAAeF,EAAgB,SAAU,CAC9CG,YAAY,EACZC,IAAK,cACCL,SACF3H,EAAKwE,qBAAsB,EACpBmD,EAAgBM,kBAgBvBC,EAAgE,CACpET,aAAAA,EACApL,QAASnD,KAAKmD,QACdd,SAAUA,EACVyB,MAAO9D,KAAK8D,MACZmL,QAdc,kBACTnI,EAAK3D,QAAQb,SAGlBwE,EAAKwE,qBAAsB,EACpBxE,EAAK3D,QAAQb,QAAQoM,IAHnB9I,QAAQ2D,OAAO,oBAaxBuC,KAAM9L,KAAK8L,gBAGT9L,KAAKmD,QAAQ+L,iBAAbC,EAAuBC,yBACpBjM,QAAQ+L,aAAUE,QAAQJ,UAI5BK,YAAcrP,KAAK8D,MAIrB9D,KAAK8D,MAAML,YACZzD,KAAK8D,MAAMwL,sBAAcN,EAAQT,qBAARgB,EAAsBzD,aAE1CkB,SAAS,CAAEC,KAAM,QAASnB,cAAMkD,EAAQT,qBAARiB,EAAsB1D,mBAIxDuB,QAAU,IAAIhF,EAAQ,CACzBsB,GAAIqF,EAAQC,QACZrG,YAAO6F,YAAAA,EAAiB7F,cAAjB6G,EAAwBC,KAAKjB,GACpCnF,UAAW,SAAAoD,GACT5F,EAAKyF,QAAQG,SAGb5F,EAAK6E,MAAMrD,OAAOgB,WAAlBxC,EAAK6E,MAAMrD,OAAOgB,UAAYoD,EAAM5F,GAGb,IAAnBA,EAAKkF,WACPlF,EAAKsF,kBAGT5C,QAAS,SAACxD,GAEFoC,EAAiBpC,IAAUA,EAAMmC,QACrCrB,EAAKkG,SAAS,CACZC,KAAM,QACNjH,MAAOA,IAINoC,EAAiBpC,WAEpBc,EAAK6E,MAAMrD,OAAOkB,SAAlB1C,EAAK6E,MAAMrD,OAAOkB,QAAUxD,EAAOc,GAGnCsE,IAAYpF,MAAMA,IAIG,IAAnBc,EAAKkF,WACPlF,EAAKsF,kBAGTnC,OAAQ,WACNnD,EAAKkG,SAAS,CAAEC,KAAM,YAExB9C,QAAS,WACPrD,EAAKkG,SAAS,CAAEC,KAAM,WAExB7C,WAAY,WACVtD,EAAKkG,SAAS,CAAEC,KAAM,cAExBpD,MAAOmF,EAAQ7L,QAAQ0G,MACvBC,WAAYkF,EAAQ7L,QAAQ2G,kBAGzBX,QAAUnJ,KAAKqN,QAAQlE,QAErBnJ,KAAKmJ,WAGN6D,SAAR,SAAiB2C,mBACV7L,MAAQ9D,KAAK4P,QAAQ5P,KAAK8D,MAAO6L,GAEtCtF,EAAcK,OAAM,WAClBK,EAAKW,UAAU1E,SAAQ,SAAAwG,GACrBA,EAASqC,cAAcF,MAGzB5E,EAAKY,MAAMuC,OAAO,CAAExL,MAAOqI,EAAMkC,KAAM,eAAgB0C,OAAAA,UAIjD9D,gBAAV,SACE1I,OAEMuJ,EAC2B,mBAAxBvJ,EAAQ2M,YACV3M,EAAQ2M,cACT3M,EAAQ2M,YAIRC,OAFgD,IAAxB5M,EAAQ2M,YAGM,mBAAjC3M,EAAQ4M,qBACZ5M,EAAQ4M,uBACT5M,EAAQ4M,qBACV,EAEEC,OAA0B,IAATtD,QAEhB,CACLA,KAAAA,EACAuD,gBAAiB,EACjB/C,cAAe8C,QAAUD,EAAAA,EAAwBjO,KAAKC,MAAQ,EAC9DiE,MAAO,KACPkK,iBAAkB,EAClBC,eAAgB,EAChBC,kBAAmB,EACnBd,UAAW,KACX7L,YAAY,EACZiK,eAAe,EACf1E,UAAU,EACVjF,OAAQiM,EAAU,UAAY,WAIxBJ,QAAV,SACE9L,EACA6L,kBAEQA,EAAO1C,UACR,qBAEEnJ,GACHsM,kBAAmBtM,EAAMsM,kBAAoB,QAE5C,oBAEEtM,GACHkF,UAAU,QAET,uBAEElF,GACHkF,UAAU,QAET,oBAEElF,GACHsM,kBAAmB,EACnBd,mBAAWK,EAAO7D,QAAQ,KAC1BrI,YAAY,EACZuF,UAAU,IACLlF,EAAMoJ,eAAiB,CAC1BlH,MAAO,KACPjC,OAAQ,gBAGT,sBAEED,GACH4I,KAAMiD,EAAOjD,KACbuD,gBAAiBnM,EAAMmM,gBAAkB,EACzC/C,uBAAeyC,EAAOzC,iBAAiBpL,KAAKC,MAC5CiE,MAAO,KACPoK,kBAAmB,EACnB3M,YAAY,EACZiK,eAAe,EACf1E,UAAU,EACVjF,OAAQ,gBAEP,YACGiC,EAAQ2J,EAAO3J,aAEjBoC,EAAiBpC,IAAUA,EAAMkC,QAAUlI,KAAKqP,iBACtCrP,KAAKqP,kBAIdvL,GACHkC,MAAOA,EACPkK,iBAAkBpM,EAAMoM,iBAAmB,EAC3CC,eAAgBrO,KAAKC,MACrBqO,kBAAmBtM,EAAMsM,kBAAoB,EAC7C3M,YAAY,EACZuF,UAAU,EACVjF,OAAQ,cAEP,yBAEED,GACH4J,eAAe,QAEd,uBAEE5J,EACA6L,EAAO7L,sBAGLA,SCnjBFuM,yBAMC/H,sCAELA,OAASA,GAAU,KACnBgI,QAAU,KACVC,WAAa,uCAGpBC,MAAA,SACEC,EACAtN,EACAW,SAEMzB,EAAWc,EAAQd,SACnBY,WACJE,EAAQF,aAAaC,EAAsBb,EAAUc,GACnDT,EAAQ1C,KAAK8O,IAA4C7L,UAExDP,IACHA,EAAQ,IAAI2I,EAAM,CAChBM,MAAO3L,KACPqC,SAAAA,EACAY,UAAAA,EACAE,QAASsN,EAAOC,oBAAoBvN,GACpCW,MAAAA,EACA0H,eAAgBiF,EAAOE,iBAAiBtO,GACxCyJ,KAAM3I,EAAQ2I,YAEX8E,IAAIlO,IAGJA,KAGTkO,IAAA,SAAIlO,GACG1C,KAAKuQ,WAAW7N,EAAMO,kBACpBsN,WAAW7N,EAAMO,WAAaP,OAC9B4N,QAAQ7Q,KAAKiD,QACbwL,OAAO,CACVjB,KAAM,aACNvK,MAAAA,QAKN4J,OAAA,SAAO5J,OACCmO,EAAa7Q,KAAKuQ,WAAW7N,EAAMO,WAErC4N,IACFnO,EAAM4K,eAEDgD,QAAUtQ,KAAKsQ,QAAQ1Q,QAAO,SAAAC,UAAKA,IAAM6C,KAE1CmO,IAAenO,UACV1C,KAAKuQ,WAAW7N,EAAMO,gBAG1BiL,OAAO,CAAEjB,KAAM,eAAgBvK,MAAAA,QAIxCoO,MAAA,sBACEzG,EAAcK,OAAM,WAClB5D,EAAKwJ,QAAQtJ,SAAQ,SAAAtE,GACnBoE,EAAKwF,OAAO5J,YAKlBoM,IAAA,SAME7L,UAEOjD,KAAKuQ,WAAWtN,MAGzB8N,OAAA,kBACS/Q,KAAKsQ,WAGdzC,KAAA,SACE5L,EACAC,OAEOO,EAAWF,EAAgBN,EAAMC,kBAEX,IAAlBO,EAAQG,QACjBH,EAAQG,OAAQ,GAGX5C,KAAKsQ,QAAQzC,MAAK,SAAAnL,UAASF,EAAWC,EAASC,SAMxDsO,QAAA,SAAQ/O,EAAgCC,OAC/BO,EAAWF,EAAgBN,EAAMC,aACjClD,OAAOuF,KAAK9B,GAASxC,OAAS,EACjCD,KAAKsQ,QAAQ1Q,QAAO,SAAA8C,UAASF,EAAWC,EAASC,MACjD1C,KAAKsQ,WAGXpC,OAAA,SAAO+C,cACL5G,EAAcK,OAAM,WAClBK,EAAK1L,UAAU2H,SAAQ,SAAAzH,GACrBA,EAAS0R,YAKf3K,QAAA,sBACE+D,EAAcK,OAAM,WAClBwG,EAAKZ,QAAQtJ,SAAQ,SAAAtE,GACnBA,EAAM4D,mBAKZgB,SAAA,sBACE+C,EAAcK,OAAM,WAClByG,EAAKb,QAAQtJ,SAAQ,SAAAtE,GACnBA,EAAM4E,qBAnIkBlI,GCSnBgS,wBAeC9I,QACLnF,aACAmF,EAAOkD,eACPlD,EAAOnF,cAEPkO,WAAa/I,EAAO+I,gBACpBC,cAAgBhJ,EAAOgJ,mBACvB5F,UAAY,QACZ5H,MAAQwE,EAAOxE,OAAS+H,SACxBC,KAAOxD,EAAOwD,gCAGrBqB,SAAA,SAASrJ,QACFkJ,SAAS,CAAEC,KAAM,WAAYnJ,MAAAA,OAGpCmK,YAAA,SAAYT,IACgC,IAAtCxN,KAAK0L,UAAUlK,QAAQgM,SACpB9B,UAAUjM,KAAK+N,MAIxBW,eAAA,SAAeX,QACR9B,UAAY1L,KAAK0L,UAAU9L,QAAO,SAAAC,UAAKA,IAAM2N,QAGpDxF,OAAA,kBACMhI,KAAKqN,cACFA,QAAQrF,SACNhI,KAAKqN,QAAQlE,QAAQrD,KAAKhF,GAAMiF,MAAMjF,IAExC8E,QAAQC,aAGjBkD,SAAA,kBACM/I,KAAKqN,cACFA,QAAQtE,WACN/I,KAAKqN,QAAQlE,SAEfnJ,KAAKuR,aAGdA,QAAA,eACM7E,SAEE8E,EAAiC,YAAtBxR,KAAK8D,MAAMC,OAExBoF,EAAUvD,QAAQC,iBAEjB2L,SACExE,SAAS,CAAEC,KAAM,UAAWwE,UAAWzR,KAAKmD,QAAQsO,YACzDtI,EAAUA,EACPrD,MAAK,iBAEJnG,EAAK2R,cAAchJ,OAAOoJ,UAA1B/R,EAAK2R,cAAchJ,OAAOoJ,SACxB/R,EAAKmE,MAAM2N,UACX9R,MAGHmG,MAAK,wBAAMnG,EAAKwD,QAAQuO,gBAAb/R,EAAKwD,QAAQuO,SAAW/R,EAAKmE,MAAM2N,cAC9C3L,MAAK,SAAAkJ,GACAA,IAAYrP,EAAKmE,MAAMkL,SACzBrP,EAAKqN,SAAS,CACZC,KAAM,UACN+B,QAAAA,EACAyC,UAAW9R,EAAKmE,MAAM2N,gBAMzBtI,EACJrD,MAAK,kBAAMnG,EAAKgS,qBAChB7L,MAAK,SAAApB,GACJgI,EAAOhI,QAEP/E,EAAK2R,cAAchJ,OAAOgB,WAA1B3J,EAAK2R,cAAchJ,OAAOgB,UACxBoD,EACA/M,EAAKmE,MAAM2N,UACX9R,EAAKmE,MAAMkL,QACXrP,MAGHmG,MAAK,wBACJnG,EAAKwD,QAAQmG,iBAAb3J,EAAKwD,QAAQmG,UACXoD,EACA/M,EAAKmE,MAAM2N,UACX9R,EAAKmE,MAAMkL,YAGdlJ,MAAK,wBACJnG,EAAKwD,QAAQyO,iBAAbjS,EAAKwD,QAAQyO,UACXlF,EACA,KACA/M,EAAKmE,MAAM2N,UACX9R,EAAKmE,MAAMkL,YAGdlJ,MAAK,kBACJnG,EAAKqN,SAAS,CAAEC,KAAM,UAAWP,KAAAA,IAC1BA,KAER3G,OAAM,SAAAC,gBAELrG,EAAK2R,cAAchJ,OAAOkB,SAA1B7J,EAAK2R,cAAchJ,OAAOkB,QACxBxD,EACArG,EAAKmE,MAAM2N,UACX9R,EAAKmE,MAAMkL,QACXrP,GAIFyL,IAAYpF,MAAMA,GAEXJ,QAAQC,UACZC,MAAK,wBACJnG,EAAKwD,QAAQqG,eAAb7J,EAAKwD,QAAQqG,QACXxD,EACArG,EAAKmE,MAAM2N,UACX9R,EAAKmE,MAAMkL,YAGdlJ,MAAK,wBACJnG,EAAKwD,QAAQyO,iBAAbjS,EAAKwD,QAAQyO,eACXhL,EACAZ,EACArG,EAAKmE,MAAM2N,UACX9R,EAAKmE,MAAMkL,YAGdlJ,MAAK,iBACJnG,EAAKqN,SAAS,CAAEC,KAAM,QAASjH,MAAAA,IACzBA,WAKR2L,gBAAR,oCACOtE,QAAU,IAAIhF,EAAQ,CACzBsB,GAAI,kBACG7C,EAAK3D,QAAQ0O,WAGX/K,EAAK3D,QAAQ0O,WAAW/K,EAAKhD,MAAM2N,WAFjC7L,QAAQ2D,OAAO,wBAI1BU,OAAQ,WACNnD,EAAKkG,SAAS,CAAEC,KAAM,YAExB9C,QAAS,WACPrD,EAAKkG,SAAS,CAAEC,KAAM,WAExB7C,WAAY,WACVtD,EAAKkG,SAAS,CAAEC,KAAM,cAExBpD,eAAO7J,KAAKmD,QAAQ0G,SAAS,EAC7BC,WAAY9J,KAAKmD,QAAQ2G,aAGpB9J,KAAKqN,QAAQlE,WAGd6D,SAAR,SAAiB2C,mBACV7L,MA4BT,SACEA,EACA6L,UAEQA,EAAO1C,UACR,qBAEEnJ,GACH+D,aAAc/D,EAAM+D,aAAe,QAElC,oBAEE/D,GACHkF,UAAU,QAET,uBAEElF,GACHkF,UAAU,QAET,sBAEElF,GACHkL,QAASW,EAAOX,QAChBtC,UAAM9F,EACNZ,MAAO,KACPgD,UAAU,EACVjF,OAAQ,UACR0N,UAAW9B,EAAO8B,gBAEjB,sBAEE3N,GACH4I,KAAMiD,EAAOjD,KACb1G,MAAO,KACPjC,OAAQ,UACRiF,UAAU,QAET,oBAEElF,GACH4I,UAAM9F,EACNZ,MAAO2J,EAAO3J,MACd6B,aAAc/D,EAAM+D,aAAe,EACnCmB,UAAU,EACVjF,OAAQ,cAEP,uBAEED,EACA6L,EAAO7L,sBAGLA,GAjFI8L,CAAQ5P,KAAK8D,MAAO6L,GAEjCtF,EAAcK,OAAM,WAClBK,EAAKW,UAAU1E,SAAQ,SAAAwG,GACrBA,EAASsE,iBAAiBnC,MAE5B5E,EAAKuG,cAAcpD,OAAOnD,YAKzB,SAASc,UAMP,CACLmD,aAASpI,EACT8F,UAAM9F,EACNZ,MAAO,KACP6B,aAAc,EACdmB,UAAU,EACVjF,OAAQ,OACR0N,eAAW7K,OCvPFmL,yBAMCzJ,sCAELA,OAASA,GAAU,KACnB0J,UAAY,KACZX,WAAa,sCAGpBb,MAAA,SACEC,EACAtN,EACAW,OAEMH,EAAW,IAAIyN,EAAS,CAC5BE,cAAetR,KACfqR,aAAcrR,KAAKqR,WACnBlO,QAASsN,EAAOwB,uBAAuB9O,GACvCW,MAAAA,EACA0H,eAAgBrI,EAAQS,YACpB6M,EAAOyB,oBAAoB/O,EAAQS,kBACnCgD,EACJkF,KAAM3I,EAAQ2I,mBAGX8E,IAAIjN,GAEFA,KAGTiN,IAAA,SAAIjN,QACGqO,UAAUvS,KAAKkE,QACfuK,OAAOvK,MAGd2I,OAAA,SAAO3I,QACAqO,UAAYhS,KAAKgS,UAAUpS,QAAO,SAAAC,UAAKA,IAAM8D,KAClDA,EAASqE,cACJkG,OAAOvK,MAGdmN,MAAA,sBACEzG,EAAcK,OAAM,WAClB5D,EAAKkL,UAAUhL,SAAQ,SAAArD,GACrBmD,EAAKwF,OAAO3I,YAKlBoN,OAAA,kBACS/Q,KAAKgS,aAGdnE,KAAA,SACEpL,eAE6B,IAAlBA,EAAQG,QACjBH,EAAQG,OAAQ,GAGX5C,KAAKgS,UAAUnE,MAAK,SAAAlK,UAAYD,EAAcjB,EAASkB,SAGhEqN,QAAA,SAAQvO,UACCzC,KAAKgS,UAAUpS,QAAO,SAAA+D,UAAYD,EAAcjB,EAASkB,SAGlEuK,OAAA,SAAOvK,cACL0G,EAAcK,OAAM,WAClBK,EAAK1L,UAAU2H,SAAQ,SAAAzH,GACrBA,EAASoE,YAKf2C,QAAA,gBACO6L,2BAGP7K,SAAA,gBACO6K,2BAGPA,sBAAA,eACQC,EAAkBpS,KAAKgS,UAAUpS,QAAO,SAAAC,UAAKA,EAAEiE,MAAMkF,mBACpDqB,EAAcK,OAAM,kBACzB0H,EAAgB3N,QACd,SAAC0E,EAASxF,UACRwF,EAAQrD,MAAK,kBAAMnC,EAASoF,WAAWhD,MAAMjF,QAC/C8E,QAAQC,kBA7FmBzG,GCtB5B,SAASiT,UAKP,CACLjD,QAAS,SAAAJ,GACPA,EAAQC,QAAU,2BAkEZ9F,EAjEEmJ,WACJtD,EAAQT,wBAARgB,EAAsBzD,aAAtB0D,EAA4B8C,YACxBC,WAAYvD,EAAQT,wBAARiE,EAAsB1G,aAAtB2G,EAA4BF,UACxC5D,QAAY4D,SAAAA,EAAW5D,UACvB+D,EAA8C,mBAAzBH,SAAAA,EAAWI,WAChCC,EAAkD,oBAAzBL,SAAAA,EAAWI,WACpCE,YAAW7D,EAAQlL,MAAM4I,eAAMoG,QAAS,GACxCC,YAAgB/D,EAAQlL,MAAM4I,eAAMsG,aAAc,GAClDvE,EAAkBvI,IAClB+M,QAAcxE,SAAAA,EAAiBM,OACjCmE,EAAgBH,EAChBI,GAAY,EAGV7Q,EACJ0M,EAAQ7L,QAAQb,SAAY,kBAAMsD,QAAQ2D,OAAO,oBAE7C6J,EAAgB,SACpBN,EACAO,EACAC,EACAC,UAEAL,EAAgBK,GACXF,UAAUH,aACPA,GAAeG,IAChBE,GAAYD,UAASR,aAAaA,GAAOQ,KAI5CE,EAAY,SAChBV,EACAW,EACAJ,EACAE,MAEIJ,SACKvN,QAAQ2D,OAAO,qBAGH,IAAV8J,IAA0BI,GAAUX,EAAM7S,cAC5C2F,QAAQC,QAAQiN,OAGnBpE,EAAuC,CAC3CrM,SAAU2M,EAAQ3M,SAClB0M,OAAQkE,EACRtE,UAAW0E,EACXvH,KAAMkD,EAAQlD,MAGV4H,EAAgBpR,EAAQoM,GAExBvF,EAAUvD,QAAQC,QAAQ6N,GAAe5N,MAAK,SAAAwN,UAClDF,EAAcN,EAAOO,EAAOC,EAAMC,MAGhCxL,EAAa2L,KACMvK,EACRnB,OAAS0L,EAAc1L,eAG/BmB,MAMJ0J,EAAS5S,OAKT,GAAIyS,EAAoB,KACrBe,OAA8B,IAAd9E,EAChB0E,EAAQI,EACV9E,EACAgF,EAAiB3E,EAAQ7L,QAAS0P,GACtC1J,EAAUqK,EAAUX,EAAUY,EAAQJ,QAInC,GAAIT,EAAwB,KACzBa,OAA8B,IAAd9E,EAChB0E,EAAQI,EACV9E,EACAiF,EAAqB5E,EAAQ7L,QAAS0P,GAC1C1J,EAAUqK,EAAUX,EAAUY,EAAQJ,GAAO,mBAK7CH,EAAgB,OAEVO,OAAqD,IAArCzE,EAAQ7L,QAAQwQ,iBAEhCE,GACJvB,IAAeO,EAAS,IACpBP,EAAYO,EAAS,GAAI,EAAGA,GAIlC1J,EAAU0K,EACNL,EAAU,GAAIC,EAAQV,EAAc,IACpCnN,QAAQC,QAAQuN,EAAc,GAAIL,EAAc,GAAIF,EAAS,wBAGxDxS,GACP8I,EAAUA,EAAQrD,MAAK,SAAAgN,OAEnBR,IAAeO,EAASxS,IACpBiS,EAAYO,EAASxS,GAAIA,EAAGwS,GAGT,KACjBQ,EAAQI,EACVV,EAAc1S,GACdsT,EAAiB3E,EAAQ7L,QAAS2P,UAC/BU,EAAUV,EAAOW,EAAQJ,UAE3BzN,QAAQC,QACbuN,EAAcN,EAAOC,EAAc1S,GAAIwS,EAASxS,SAd7CA,EAAI,EAAGA,EAAIwS,EAAS5S,OAAQI,MAA5BA,WAtCT8I,EAAUqK,EAAU,QA0DhBM,EAAe3K,EAAQrD,MAAK,SAAAgN,SAAU,CAC1CA,MAAAA,EACAE,WAAYE,aAGYY,EAER9L,OAAS,WACzBmL,GAAY,QACZ1E,GAAAA,EAAiB7F,QACbb,EAAaoB,IACfA,EAAQnB,UAIL8L,KAMR,SAASH,EACdxQ,EACA2P,gBAEO3P,EAAQwQ,wBAARxQ,EAAQwQ,iBAAmBb,EAAMA,EAAM7S,OAAS,GAAI6S,GAGtD,SAASc,EACdzQ,EACA2P,gBAEO3P,EAAQyQ,4BAARzQ,EAAQyQ,qBAAuBd,EAAM,GAAIA,GAO3C,SAASiB,EACd5Q,EACA2P,MAEI3P,EAAQwQ,kBAAoBxS,MAAMC,QAAQ0R,GAAQ,KAC9CkB,EAAgBL,EAAiBxQ,EAAS2P,UAE9C,MAAOkB,IAEW,IAAlBA,GASC,SAASC,EACd9Q,EACA2P,MAEI3P,EAAQyQ,sBAAwBzS,MAAMC,QAAQ0R,GAAQ,KAClDoB,EAAoBN,EAAqBzQ,EAAS2P,UAEtD,MAAOoB,IAEe,IAAtBA,OC5JOC,wBASC7L,YAAAA,IAAAA,EAA4B,SACjC8L,WAAa9L,EAAO8L,YAAc,IAAI/D,OACtCiB,cAAgBhJ,EAAOgJ,eAAiB,IAAIS,OAC5CvG,eAAiBlD,EAAOkD,gBAAkB,QAC1C6I,cAAgB,QAChBC,iBAAmB,8BAG1BC,MAAA,2BACOC,iBAAmBpO,EAAa9G,WAAU,WACzC8G,EAAaa,aAAeI,EAAcI,aAC5C9H,EAAK2R,cAAchL,UACnB3G,EAAKyU,WAAW9N,mBAGfmO,kBAAoBpN,EAAc/H,WAAU,WAC3C8G,EAAaa,aAAeI,EAAcI,aAC5C9H,EAAK2R,cAAchK,WACnB3H,EAAKyU,WAAW9M,kBAKtBoN,QAAA,iCACOF,8CACAC,oCAKPhR,WAAA,SAAWxB,EAAgCC,OAClCO,EAAWF,EAAgBN,EAAMC,aACxCO,EAAQI,UAAW,EACZ7C,KAAKoU,WAAWpD,QAAQvO,GAASxC,UAG1C0U,WAAA,SAAWlS,UACFzC,KAAKsR,cAAcN,aAAavO,GAASI,UAAU,KAAQ5C,UAGpE2U,aAAA,SACEvS,EACAI,yBAEOzC,KAAKoU,WAAWvG,KAAYxL,EAAUI,WAAtCoS,EAAgD/Q,MAAM4I,QAK/DoI,eAAA,SACEC,UAEO/U,KAAKgV,gBACThE,QAAQ+D,GACRE,KAAI,kBAEI,GAFD5S,WAAUyB,MACG4I,YAKzBwI,aAAA,SACE7S,EACAmK,EACArJ,OAEMgS,EAAgBnT,EAAeK,GAC/B+S,EAAmBpV,KAAK0Q,oBAAoByE,UAC3CnV,KAAKoU,WACT5D,MAAMxQ,KAAMoV,GACZ7I,QAAQC,EAASrJ,MAetBkS,eAAA,SACEN,EACAvI,EACArJ,qBAEOkH,EAAcK,OAAM,kBACzB5D,EAAKkO,gBACFhE,QAAQ+D,GACRE,KAAI,gBAAG5S,IAAAA,eAAe,CACrBA,EACAyE,EAAKoO,aAAoB7S,EAAUmK,EAASrJ,aAKpDmS,cAAA,SACEjT,EACAI,yBAEOzC,KAAKoU,WAAWvG,KAAoBxL,EAAUI,WAA9C8S,EAAwDzR,SAKjE0R,cAAA,SAAcvT,EAAgCC,OACrCO,EAAWF,EAAgBN,EAAMC,MAClCkS,EAAapU,KAAKoU,WACxB/J,EAAcK,OAAM,WAClB0J,EAAWpD,QAAQvO,GAASuE,SAAQ,SAAAtE,GAClC0R,EAAW9H,OAAO5J,YAcxB+S,aAAA,SACExT,EACAC,EACAC,gBAE2BI,EAAgBN,EAAMC,EAAMC,GAAhDM,OAASU,OACViR,EAAapU,KAAKoU,WAElBsB,OACDjT,GACHE,QAAQ,WAGH0H,EAAcK,OAAM,kBACzB0J,EAAWpD,QAAQvO,GAASuE,SAAQ,SAAAtE,GAClCA,EAAM6K,WAEDxC,EAAK4K,eAAeD,EAAgBvS,SAU/CyS,cAAA,SACE3T,EACAC,EACAC,gBAEsCI,EAAgBN,EAAMC,EAAMC,GAA3DM,cAASoG,aAAgB,UAEI,IAAzBA,EAAcX,SACvBW,EAAcX,QAAS,OAGnB2N,EAAWxL,EAAcK,OAAM,kBACnCwG,EAAKkD,WAAWpD,QAAQvO,GAASwS,KAAI,SAAAvS,UAASA,EAAMsF,OAAOa,gBAGtDjD,QAAQkQ,IAAID,GAAU/P,KAAKhF,GAAMiF,MAAMjF,MAYhDiV,kBAAA,SACE9T,EACAC,EACAC,sBAE2BI,EAAgBN,EAAMC,EAAMC,GAAhDM,OAASU,OAEVuS,OACDjT,GAGHE,yBAAQF,EAAQuT,iBAAiBvT,EAAQE,WACzCG,kBAAUL,EAAQwT,6BAGb5L,EAAcK,OAAM,kBACzByG,EAAKiD,WAAWpD,QAAQvO,GAASuE,SAAQ,SAAAtE,GACvCA,EAAM2L,gBAED8C,EAAKwE,eAAeD,EAAgBvS,SAa/CwS,eAAA,SACE1T,EACAC,EACAC,gBAE2BI,EAAgBN,EAAMC,EAAMC,GAAhDM,OAASU,OAEV0S,EAAWxL,EAAcK,OAAM,kBACnCwL,EAAK9B,WAAWpD,QAAQvO,GAASwS,KAAI,SAAAvS,UACnCA,EAAM4L,WAAM1H,OACPzD,GACH2I,KAAM,CAAEwG,kBAAa7P,SAAAA,EAAS6P,sBAKhCnJ,EAAUvD,QAAQkQ,IAAID,GAAU/P,KAAKhF,gBAEpCqC,SAAAA,EAASgT,gBACZhN,EAAUA,EAAQpD,MAAMjF,IAGnBqI,KA8BTiN,WAAA,SAMEnU,EACAC,EAGAC,OAEMgT,EAAgBnT,EAAeC,EAAMC,EAAMC,GAC3CiT,EAAmBpV,KAAK0Q,oBAAoByE,QAGZ,IAA3BC,EAAiBvL,QAC1BuL,EAAiBvL,OAAQ,OAGrBnH,EAAQ1C,KAAKoU,WAAW5D,MAAMxQ,KAAMoV,UAEnC1S,EAAMkL,cAAcwH,EAAiBzT,WACxCe,EAAM4L,MAAM8G,GACZxP,QAAQC,QAAQnD,EAAMoB,MAAM4I,SA8BlC2J,cAAA,SAMEpU,EACAC,EAGAC,UAEOnC,KAAKoW,WAAWnU,EAAaC,EAAaC,GAC9C2D,KAAKhF,GACLiF,MAAMjF,MA8BXwV,mBAAA,SAMErU,EAGAC,EAGAC,OAEMgT,EAAgBnT,EAAeC,EAAMC,EAAMC,UACjDgT,EAAcjG,SAAWmD,IAKlBrS,KAAKoW,WAAWjB,MA8BzBoB,sBAAA,SAMEtU,EAGAC,EAGAC,UAEOnC,KAAKsW,mBAAmBrU,EAAaC,EAAaC,GACtD2D,KAAKhF,GACLiF,MAAMjF,MAGX0V,gBAAA,sBACQX,EAAWxL,EAAcK,OAAM,kBACnC+L,EAAKnF,cAAcP,SAASkE,KAAI,SAAAtR,UAAYA,EAASqE,sBAEhDpC,QAAQkQ,IAAID,GAAU/P,KAAKhF,GAAMiF,MAAMjF,MAGhDqR,sBAAA,kBACSnS,KAAK0W,mBAAmBvE,2BAGjCR,gBAAA,SAMExO,UAEOnD,KAAKsR,cAAcd,MAAMxQ,KAAMmD,GAASoO,aAGjDyD,cAAA,kBACShV,KAAKoU,cAGdsC,iBAAA,kBACS1W,KAAKsR,iBAGdqF,kBAAA,kBACS3W,KAAKwL,kBAGdS,kBAAA,SAAkB9I,QACXqI,eAAiBrI,KAGxByT,iBAAA,SACEvU,EACAc,OAEMuB,EAAS1E,KAAKqU,cAAcxG,MAChC,SAAAhO,UAAKgE,EAAaxB,KAAcwB,EAAahE,EAAEwC,aAE7CqC,EACFA,EAAO8G,eAAiBrI,OAEnBkR,cAAc5U,KAAK,CAAE4C,SAAAA,EAAUmJ,eAAgBrI,OAIxDwN,iBAAA,SACEtO,gBAEOA,WACHrC,KAAKqU,cAAcxG,MAAK,SAAAhO,UAAKuD,EAAgBf,EAAUxC,EAAEwC,qBAAzDwU,EACIrL,oBACJ5E,KAGNkQ,oBAAA,SACElT,EACAT,OAEMuB,EAAS1E,KAAKsU,iBAAiBzG,MACnC,SAAAhO,UAAKgE,EAAaD,KAAiBC,EAAahE,EAAE+D,gBAEhDc,EACFA,EAAO8G,eAAiBrI,OAEnBmR,iBAAiB7U,KAAK,CAAEmE,YAAAA,EAAa4H,eAAgBrI,OAI9D+O,oBAAA,SACEtO,gBAEOA,WACH5D,KAAKsU,iBAAiBzG,MAAK,SAAAhO,UACzBuD,EAAgBQ,EAAa/D,EAAE+D,wBADjCmT,EAEGvL,oBACH5E,KAGN8J,oBAAA,SAOEvN,YAQIA,SAAAA,EAAS6T,kBACJ7T,MAGHiS,OACDpV,KAAKwL,eAAe8E,QACpBtQ,KAAK2Q,uBAAiBxN,SAAAA,EAASd,UAC/Bc,GACH6T,YAAY,WAST5B,EAAiBnS,WAAamS,EAAiB/S,WAClD+S,EAAiBnS,UAAYC,EAC3BkS,EAAiB/S,SACjB+S,IAIGA,KAGT6B,4BAAA,SAOE9T,UAQOnD,KAAK0Q,oBAAoBvN,MAGlC8O,uBAAA,SACE9O,gBAEIA,SAAAA,EAAS6T,YACJ7T,OAGJnD,KAAKwL,eAAewG,UACpBhS,KAAKkS,0BAAoB/O,SAAAA,EAASS,aAClCT,GACH6T,YAAY,OAIhBlG,MAAA,gBACOsD,WAAWtD,aACXQ,cAAcR,cCvmBVoG,yBAqCTzG,EACAtN,sCAUKsN,OAASA,IACTtN,QAAUA,IACVgU,aAAe,KACfC,YAAc,OACdC,gBACA5L,WAAWtI,uCAGRkU,YAAV,gBACO/K,OAAStM,KAAKsM,OAAOoD,KAAK1P,WAC1B+N,QAAU/N,KAAK+N,QAAQ2B,KAAK1P,SAGzBN,YAAV,WACgC,IAA1BM,KAAKX,UAAUY,cACZqX,aAAarJ,YAAYjO,MAE1BuX,EAAmBvX,KAAKsX,aAActX,KAAKmD,eACxCqU,oBAGFC,mBAIC3X,cAAV,WACOE,KAAKX,UAAUY,aACbqN,aAITU,uBAAA,kBACS0J,EACL1X,KAAKsX,aACLtX,KAAKmD,QACLnD,KAAKmD,QAAQwU,uBAIjB7J,yBAAA,kBACS4J,EACL1X,KAAKsX,aACLtX,KAAKmD,QACLnD,KAAKmD,QAAQyU,yBAIjBtK,QAAA,gBACOjO,UAAY,QACZwY,mBACAP,aAAanJ,eAAenO,SAGnCyL,WAAA,SACEtI,EAOA2U,OAEMC,EAAc/X,KAAKmD,QACnB6U,EAAYhY,KAAKsX,qBAElBnU,QAAUnD,KAAKyQ,OAAOwG,4BAA4B9T,QAGrB,IAAzBnD,KAAKmD,QAAQsK,SACY,kBAAzBzN,KAAKmD,QAAQsK,cAEd,IAAIwK,MAAM,oCAIbjY,KAAKmD,QAAQd,gBACXc,QAAQd,SAAW0V,EAAY1V,eAGjC6V,kBAECC,EAAUnY,KAAKD,eAInBoY,GACAC,EACEpY,KAAKsX,aACLU,EACAhY,KAAKmD,QACL4U,SAGGP,oBAIFa,aAAaP,IAIhBK,GACCnY,KAAKsX,eAAiBU,GACrBhY,KAAKmD,QAAQsK,UAAYsK,EAAYtK,SACrCzN,KAAKmD,QAAQxB,YAAcoW,EAAYpW,gBAEpC2W,yBAGDC,EAAsBvY,KAAKwY,0BAI/BL,GACCnY,KAAKsX,eAAiBU,GACrBhY,KAAKmD,QAAQsK,UAAYsK,EAAYtK,SACrC8K,IAAwBvY,KAAKyY,6BAE1BC,sBAAsBH,MAI/BI,oBAAA,SACExV,OAQMiS,EAAmBpV,KAAKyQ,OAAOwG,4BAA4B9T,GAE3DT,EAAQ1C,KAAKyQ,OAChBuE,gBACAxE,MACCxQ,KAAKyQ,OACL2E,UAQGpV,KAAK4Y,aAAalW,EAAO0S,MAGlCzH,iBAAA,kBACS3N,KAAK6Y,iBAGdC,YAAA,SACEpU,EACA0Q,cAQM2D,EAAgB,GAEhBC,EAAY,SAACxY,GACZsG,EAAKqQ,aAAahQ,SAAS3G,IAC9BsG,EAAKqQ,aAAa1X,KAAKe,WAI3BxB,OAAOuF,KAAKG,GAAQsC,SAAQ,SAAAxG,GAC1BxB,OAAO4P,eAAemK,EAAevY,EAAK,CACxCyY,cAAc,EACdpK,YAAY,EACZC,IAAK,kBACHkK,EAAUxY,GACHkE,EAAOlE,UAKhB4U,EAAiB8D,kBAAoB9D,EAAiB+D,WACxDH,EAAU,SAGLD,KAGTK,cAAA,SACEjW,qBAEO,IAAIyC,SAAQ,SAACC,EAAS0D,OACrB8P,EAActO,EAAKzL,WAAU,SAAAoF,GAC5BA,EAAOjB,aACV4V,IACI3U,EAAO4U,gBAAWnW,SAAAA,EAASgT,cAC7B5M,EAAO7E,EAAOsB,OAEdH,EAAQnB,aAOlB6U,gBAAA,kBACSvZ,KAAKsX,gBAGdhL,OAAA,gBACOmE,OAAOuE,gBAAgB1I,OAAOtM,KAAKsX,iBAG1CvJ,QAAA,SACE5K,UAEOnD,KAAKsO,WACPnL,GACH2I,KAAM,CAAEwG,kBAAanP,SAAAA,EAASmP,mBAIlCkH,gBAAA,SACErW,cAQMiS,EAAmBpV,KAAKyQ,OAAOwG,4BAA4B9T,GAE3DT,EAAQ1C,KAAKyQ,OAChBuE,gBACAxE,MACCxQ,KAAKyQ,OACL2E,UAQG1S,EAAM4L,QAAQxI,MAAK,kBAAMoL,EAAK0H,aAAalW,EAAO0S,SAGjD9G,MAAV,SACEC,qBAEOvO,KAAKwX,aAAajJ,GAAczI,MAAK,kBAC1CqL,EAAKkH,eACElH,EAAK0H,oBAIRrB,aAAR,SACEjJ,QAGK2J,kBAGD/O,EAA2CnJ,KAAKsX,aAAahJ,MAC/DtO,KAAKmD,QACLoL,gBAGGA,SAAAA,EAAc4H,gBACjBhN,EAAUA,EAAQpD,MAAMjF,IAGnBqI,KAGDmP,mBAAR,8BACOmB,qBAGH7Y,IACAZ,KAAK6Y,cAAcrV,SAClBzC,EAAef,KAAKmD,QAAQxB,gBAYzBiI,EAPOnI,EACXzB,KAAK6Y,cAAc3L,cACnBlN,KAAKmD,QAAQxB,WAKQ,OAElB+X,eAAiBzT,YAAW,WAC1BiQ,EAAK2C,cAAcrV,SACtB0S,EAAKmC,iBAENzO,OAGG4O,uBAAR,uBACiD,mBAAjCxY,KAAKmD,QAAQwW,gBACvB3Z,KAAKmD,QAAQwW,gBAAgB3Z,KAAK6Y,cAAcnM,KAAM1M,KAAKsX,uBAC3DtX,KAAKmD,QAAQwW,uBAGXjB,sBAAR,SAA8BkB,mBACvBC,4BAEApB,uBAAyBmB,GAG5BhZ,IACyB,IAAzBZ,KAAKmD,QAAQsK,SACZ1M,EAAef,KAAKyY,yBACW,IAAhCzY,KAAKyY,8BAKFqB,kBAAoBC,aAAY,YAEjCtD,EAAKtT,QAAQ6W,6BACb5T,EAAaa,cAEbwP,EAAKe,iBAENxX,KAAKyY,4BAGFhB,aAAR,gBACOa,0BACAI,sBAAsB1Y,KAAKwY,6BAG1BX,YAAR,gBACO4B,yBACAI,0BAGCJ,kBAAR,WACEpN,aAAarM,KAAK0Z,qBACbA,oBAAiB9S,KAGhBiT,qBAAR,WACEI,cAAcja,KAAK8Z,wBACdA,uBAAoBlT,KAGjBgS,aAAV,SACElW,EACAS,OAyBIuJ,EAjBEsL,EAAYhY,KAAKsX,aACjBS,EAAc/X,KAAKmD,QACnB+W,EAAala,KAAK6Y,cAClBsB,EAAkBna,KAAKoa,mBACvBC,EAAoBra,KAAKsa,qBACzBC,EAAc7X,IAAUsV,EACxBwC,EAAoBD,EACtB7X,EAAMoB,MACN9D,KAAKya,yBACHC,EAAkBH,EACpBva,KAAK6Y,cACL7Y,KAAK2a,oBAED7W,EAAUpB,EAAVoB,MACFoJ,EAA6DpJ,EAA7DoJ,cAAelH,EAA8ClC,EAA9CkC,MAAOmK,EAAuCrM,EAAvCqM,eAAgB1M,EAAuBK,EAAvBL,WAAYM,EAAWD,EAAXC,OACpD6W,GAAiB,EACjBC,GAAoB,KAIpB1X,EAAQ2X,kBAAmB,KACvB3C,EAAUnY,KAAKD,eAEfgb,GAAgB5C,GAAWZ,EAAmB7U,EAAOS,GAErD6X,EACJ7C,GAAWC,EAAsB1V,EAAOsV,EAAW7U,EAAS4U,IAE1DgD,GAAgBC,KAClBvX,GAAa,EACRyJ,IACHnJ,EAAS,eAObZ,EAAQ8X,mBACPnX,EAAMmM,wBACPyK,SAAAA,EAAiBQ,YACN,UAAXnX,EAEA2I,EAAOgO,EAAgBhO,KACvBQ,EAAgBwN,EAAgBxN,cAChCnJ,EAAS2W,EAAgB3W,OACzB6W,GAAiB,OAGd,GAAIzX,EAAQgY,aAAgC,IAAfrX,EAAM4I,QAGpCwN,GACApW,EAAM4I,cAASyN,SAAAA,EAAiBzN,OAChCvJ,EAAQgY,SAAWnb,KAAKob,SAExB1O,EAAO1M,KAAKqb,2BAGLD,SAAWjY,EAAQgY,OACxBzO,EAAOvJ,EAAQgY,OAAOrX,EAAM4I,OACM,IAA9BvJ,EAAQ4J,oBACVL,EAAO3H,QAAiBmV,SAAAA,EAAYxN,KAAMA,SAEvC2O,aAAe3O,OACf0K,YAAc,KACnB,MAAOA,GACPhM,IAAYpF,MAAMoR,QACbA,YAAcA,OAMvB1K,EAAQ5I,EAAM4I,aAKqB,IAA5BvJ,EAAQmY,sBACC,IAAT5O,IACK,YAAX3I,GAAmC,SAAXA,GACzB,KACIuX,YAIFpB,SAAAA,EAAYW,oBACZ1X,EAAQmY,yBAAoBjB,SAAAA,EAAmBiB,iBAE/CA,EAAkBpB,EAAWxN,aAE7B4O,EACqC,mBAA5BnY,EAAQmY,gBACVnY,EAAQmY,kBACTnY,EAAQmY,gBACVnY,EAAQgY,aAAqC,IAApBG,MAEzBA,EAAkBnY,EAAQgY,OAAOG,IACC,IAA9BnY,EAAQ4J,oBACVuO,EAAkBvW,QAChBmV,SAAAA,EAAYxN,KACZ4O,SAGClE,YAAc,KACnB,MAAOA,GACPhM,IAAYpF,MAAMoR,QACbA,YAAcA,OAKM,IAApBkE,IACTvX,EAAS,UACT2I,EAAO4O,EACPT,GAAoB,UAIpB7a,KAAKoX,cACPpR,EAAQhG,KAAKoX,YACb1K,EAAO1M,KAAKqb,aACZlL,EAAiBrO,KAAKC,MACtBgC,EAAS,SAG4C,CACrDA,OAAAA,EACAwX,UAAsB,YAAXxX,EACXmX,UAAsB,YAAXnX,EACXuV,QAAoB,UAAXvV,EACTyX,OAAmB,SAAXzX,EACR2I,KAAAA,EACAQ,cAAAA,EACAlH,MAAAA,EACAmK,eAAAA,EACAtI,aAAc/D,EAAMsM,kBACpBF,iBAAkBpM,EAAMoM,iBACxBuL,UAAW3X,EAAMmM,gBAAkB,GAAKnM,EAAMoM,iBAAmB,EACjEwL,oBACE5X,EAAMmM,gBAAkBuK,EAAkBvK,iBAC1CnM,EAAMoM,iBAAmBsK,EAAkBtK,iBAC7CzM,WAAAA,EACAkY,aAAclY,GAAyB,YAAXM,EAC5B6X,eAA2B,UAAX7X,GAA8C,IAAxBD,EAAMoJ,cAC5C2N,kBAAAA,EACAD,eAAAA,EACAiB,eAA2B,UAAX9X,GAA8C,IAAxBD,EAAMoJ,cAC5C1J,QAASA,EAAQd,EAAOS,GACxB4K,QAAS/N,KAAK+N,QACdzB,OAAQtM,KAAKsM,WAMTwP,sBAAR,SACEpX,EACAwV,OAEKA,SACI,QAGsDla,KAAKmD,QAA5D4Y,IAAAA,oBAAqBC,IAAAA,kCAExBD,IAAwBC,SACpB,KAGmB,YAAxBD,IAAsC/b,KAAKmX,aAAalX,cACnD,MAGHgc,EACoB,YAAxBF,EACI/b,KAAKmX,aACL4E,SAEC/c,OAAOuF,KAAKG,GAAQI,MAAK,SAAAtE,OACxB0b,EAAW1b,EACX2b,EAAUzX,EAAOwX,KAAchC,EAAWgC,GAC1CE,QAAaH,SAAAA,EAAenX,MAAK,SAAAjF,UAAKA,IAAMW,KAC5C6b,QAAaL,SAAAA,EAA+BlX,MAAK,SAAAjF,UAAKA,IAAMW,YAC3D2b,IAAYE,KAAgBJ,GAAiBG,SAIxD/D,aAAA,SAAaP,OACLoC,EAAala,KAAK6Y,sBAInBA,cAAgB7Y,KAAK4Y,aAAa5Y,KAAKsX,aAActX,KAAKmD,cAC1DiX,mBAAqBpa,KAAKsX,aAAaxT,WACvCwW,qBAAuBta,KAAKmD,SZjR9B,SAAgCwB,EAAMC,MACtCD,IAAMC,GAAOA,IAAMD,SACf,MAGJ,IAAMnE,KAAOmE,KACZA,EAAEnE,KAASoE,EAAEpE,UACR,SAIJ,EYyQD8b,CAAoBtc,KAAK6Y,cAAeqB,QAKtCqC,EAAsC,CAAE5Q,OAAO,IAGtB,WAA7BmM,SAAAA,EAAezY,YACfW,KAAK8b,sBAAsB9b,KAAK6Y,cAAeqB,KAE/CqC,EAAqBld,WAAY,QAG9B6O,YAAYqO,EAAyBzE,QAGpCI,YAAR,eACQxV,EAAQ1C,KAAKyQ,OAChBuE,gBACAxE,MACCxQ,KAAKyQ,OACLzQ,KAAKmD,YAQLT,IAAU1C,KAAKsX,kBAIbU,EAAYhY,KAAKsX,kBAClBA,aAAe5U,OACf+X,yBAA2B/X,EAAMoB,WACjC6W,oBAAsB3a,KAAK6Y,cAE5B7Y,KAAKD,uBACPiY,GAAAA,EAAW7J,eAAenO,MAC1B0C,EAAMuL,YAAYjO,WAItB6P,cAAA,SAAcF,OACNmI,EAA+B,GAEjB,YAAhBnI,EAAO1C,KACT6K,EAAcxO,WAAY,EACD,UAAhBqG,EAAO1C,MAAqB7E,EAAiBuH,EAAO3J,SAC7D8R,EAActO,SAAU,QAGrB6O,aAAaP,GAEd9X,KAAKD,qBACF0X,kBAIDvJ,OAAR,SAAe4J,cACbzN,EAAcK,OAAM,WAEdoN,EAAcxO,iBAChBkT,EAAKrZ,QAAQmG,WAAbkT,EAAKrZ,QAAQmG,UAAYkT,EAAK3D,cAAcnM,YAC5C8P,EAAKrZ,QAAQyO,WAAb4K,EAAKrZ,QAAQyO,UAAY4K,EAAK3D,cAAcnM,KAAO,OAC1CoL,EAActO,gBACvBgT,EAAKrZ,QAAQqG,SAAbgT,EAAKrZ,QAAQqG,QAAUgT,EAAK3D,cAAc7S,aAC1CwW,EAAKrZ,QAAQyO,WAAb4K,EAAKrZ,QAAQyO,eAAYhL,EAAW4V,EAAK3D,cAAc7S,QAIrD8R,EAAczY,WAChBmd,EAAKnd,UAAU2H,SAAQ,SAAAzH,GACrBA,EAASid,EAAK3D,kBAKdf,EAAcnM,OAChB6Q,EAAK/L,OACFuE,gBACA9G,OAAO,CAAExL,MAAO8Z,EAAKlF,aAAcrK,KAAM,kCAhrB1C7N,GAisBV,SAASmY,EACP7U,EACAS,UAbF,SACET,EACAS,YAGsB,IAApBA,EAAQsK,SACP/K,EAAMoB,MAAMoJ,eACY,UAAvBxK,EAAMoB,MAAMC,SAA+C,IAAzBZ,EAAQsZ,cAS5CC,CAAkBha,EAAOS,IACxBT,EAAMoB,MAAMoJ,cAAgB,GAC3BwK,EAAchV,EAAOS,EAASA,EAAQwZ,gBAI5C,SAASjF,EACPhV,EACAS,EACAyZ,OAIwB,IAApBzZ,EAAQsK,QAAmB,KACvBzM,EAAyB,mBAAV4b,EAAuBA,EAAMla,GAASka,QAE1C,WAAV5b,IAAiC,IAAVA,GAAmBwC,EAAQd,EAAOS,UAE3D,EAGT,SAASiV,EACP1V,EACAsV,EACA7U,EACA4U,UAGsB,IAApB5U,EAAQsK,UACP/K,IAAUsV,IAAqC,IAAxBD,EAAYtK,YAClCtK,EAAQgW,UAAmC,UAAvBzW,EAAMoB,MAAMC,SAClCP,EAAQd,EAAOS,GAInB,SAASK,EACPd,EACAS,UAEOT,EAAMkL,cAAczK,EAAQxB,eCpxBxBkb,yBAOCpM,EAAqBH,sCAG1BG,OAASA,IACTH,QAAU,KACV5L,OAAS,KACTgH,UAAY,KACZoR,aAAe,GAEhBxM,KACGyM,WAAWzM,uCAIV5Q,YAAV,sBACgC,IAA1BM,KAAKX,UAAUY,aACZyL,UAAU1E,SAAQ,SAAAwG,GACrBA,EAASlO,WAAU,SAAAoF,GACjBoC,EAAKkW,SAASxP,EAAU9I,YAMtB5E,cAAV,WACOE,KAAKX,UAAUY,aACbqN,aAITA,QAAA,gBACOjO,UAAY,QACZqM,UAAU1E,SAAQ,SAAAwG,GACrBA,EAASF,gBAIbyP,WAAA,SACEzM,EACAwH,QAEKxH,QAAUA,OACV2M,gBAAgBnF,MAGvBnK,iBAAA,kBACS3N,KAAK0E,UAGdiU,oBAAA,SAAoBrI,UACXtQ,KAAKkd,sBAAsB5M,GAAS2E,KAAI,SAAAkI,UAC7CA,EAAM3P,SAASmL,oBAAoBwE,EAAMC,6BAIrCF,sBAAR,SACE5M,cAEM+M,EAAgBrd,KAAK0L,UACrB0R,EAAwB9M,EAAQ2E,KAAI,SAAA9R,UACxC4H,EAAK0F,OAAOwG,4BAA4B9T,MAGpCma,EAA0CF,EAAsBG,SACpE,SAAAnI,OACQ+H,EAAQE,EAAcxP,MAC1B,SAAAL,UAAYA,EAASrK,QAAQF,YAAcmS,EAAiBnS,oBAEjD,MAATka,EACK,CAAC,CAAEC,sBAAuBhI,EAAkB5H,SAAU2P,IAExD,MAILK,EAAqBF,EAAkBrI,KAC3C,SAAAkI,UAASA,EAAMC,sBAAsBna,aAEjCwa,EAAmBL,EAAsBxd,QAC7C,SAAAwV,UACGoI,EAAmBrW,SAASiO,EAAiBnS,cAG5Cya,EAAqBL,EAAczd,QACvC,SAAA+d,UACGL,EAAkBxY,MAAK,SAAAqY,UAASA,EAAM3P,WAAamQ,QAGlDC,EAA6CH,EAAiBxI,KAClE,SAAC9R,EAAS0a,MACJ1a,EAAQ8X,iBAAkB,KAEtB6C,EAAyBJ,EAAmBG,WACnBjX,IAA3BkX,QACK,CACLV,sBAAuBja,EACvBqK,SAAUsQ,SAIT,CACLV,sBAAuBja,EACvBqK,SAAUzC,EAAKgT,YAAY5a,cAY1Bma,EACJU,OAAOJ,GACPpZ,MATiC,SAClCG,EACAC,UAEAwY,EAAsB5b,QAAQmD,EAAEyY,uBAChCA,EAAsB5b,QAAQoD,EAAEwY,6BAO5BW,YAAR,SAAoB5a,OACZiS,EAAmBpV,KAAKyQ,OAAOwG,4BAA4B9T,GAC3D8a,EAAkBje,KAAK8c,aAAa1H,EAAiBnS,wBACpDgb,EAAAA,EAAmB,IAAI/G,EAAclX,KAAKyQ,OAAQ2E,MAGnD6H,gBAAR,SAAwBnF,cACtBzN,EAAcK,OAAM,eACZ2S,EAAgBnM,EAAKxF,UAErBwS,EAAqBhN,EAAKgM,sBAAsBhM,EAAKZ,SAG3D4N,EAAmBlX,SAAQ,SAAAmW,UACzBA,EAAM3P,SAAS/B,WAAW0R,EAAMC,sBAAuBtF,UAGnDqG,EAAeD,EAAmBjJ,KAAI,SAAAkI,UAASA,EAAM3P,YACrD4Q,EAAkBpf,OAAOqf,YAC7BF,EAAalJ,KAAI,SAAAzH,SAAY,CAACA,EAASrK,QAAQF,UAAWuK,OAEtD8Q,EAAYH,EAAalJ,KAAI,SAAAzH,UACjCA,EAASG,sBAGL4Q,EAAiBJ,EAAarZ,MAClC,SAAC0I,EAAUqQ,UAAUrQ,IAAa6P,EAAcQ,OAE9CR,EAAcpd,SAAWke,EAAale,QAAWse,KAIrDrN,EAAKxF,UAAYyS,EACjBjN,EAAK4L,aAAesB,EACpBlN,EAAKxM,OAAS4Z,EAETpN,EAAKnR,iBAIVsB,EAAWgc,EAAec,GAAcnX,SAAQ,SAAAwG,GAC9CA,EAASF,aAGXjM,EAAW8c,EAAcd,GAAerW,SAAQ,SAAAwG,GAC9CA,EAASlO,WAAU,SAAAoF,GACjBwM,EAAK8L,SAASxP,EAAU9I,SAI5BwM,EAAKhD,iBAID8O,SAAR,SAAiBxP,EAAyB9I,OAClCmZ,EAAQ7d,KAAK0L,UAAUlK,QAAQgM,IACtB,IAAXqQ,SACGnZ,ObvFJ,SAAsBM,EAAY6Y,EAAe7c,OAChDoE,EAAOJ,EAAMwZ,MAAM,UACzBpZ,EAAKyY,GAAS7c,EACPoE,EaoFWqZ,CAAUze,KAAK0E,OAAQmZ,EAAOnZ,QACvCwJ,aAIDA,OAAR,sBACE7D,EAAcK,OAAM,WAClByG,EAAK9R,UAAU2H,SAAQ,SAAAzH,GACrBA,EAAS4R,EAAKzM,kBA/LetF,GCexBsf,yBA0BTjO,EACAtN,UAOAwb,YAAMlO,EAAQtN,2CAGNkU,YAAV,uBACQA,4BACDuH,cAAgB5e,KAAK4e,cAAclP,KAAK1P,WACxC6e,kBAAoB7e,KAAK6e,kBAAkBnP,KAAK1P,SAGvDyL,WAAA,SACEtI,EAMA2U,eAEMrM,0BAECtI,GACH+L,SAAUmD,MAEZyF,MAIJa,oBAAA,SACExV,UAOAA,EAAQ+L,SAAWmD,gBACNsG,8BAAoBxV,MAMnCyb,cAAA,SACEzb,gBAEOnD,KAAKsO,MAAM,CAEhBE,6BAAerL,SAAAA,EAASqL,kBACxB2H,mBAAchT,SAAAA,EAASgT,aACvBrK,KAAM,CACJyG,UAAW,CAAEI,UAAW,UAAWhE,gBAAWxL,SAAAA,EAASwL,iBAK7DkQ,kBAAA,SACE1b,gBAEOnD,KAAKsO,MAAM,CAEhBE,6BAAerL,SAAAA,EAASqL,kBACxB2H,mBAAchT,SAAAA,EAASgT,aACvBrK,KAAM,CACJyG,UAAW,CAAEI,UAAW,WAAYhE,gBAAWxL,SAAAA,EAASwL,iBAKpDiK,aAAV,SACElW,EACAS,mBAOQW,EAAUpB,EAAVoB,8BACa8U,uBAAalW,EAAOS,IAGvCyb,cAAe5e,KAAK4e,cACpBC,kBAAmB7e,KAAK6e,kBACxB9K,YAAaA,EAAY5Q,WAASW,EAAM4I,aAANoS,EAAYhM,OAC9CmB,gBAAiBA,EAAgB9Q,WAASW,EAAM4I,aAANqS,EAAYjM,OACtDJ,mBACE5O,EAAML,YAAwD,sBAA1CK,EAAMwL,uBAAWiD,oBAAWI,WAClDC,uBACE9O,EAAML,YACoC,uBAA1CK,EAAMwL,uBAAWiD,oBAAWI,iBAtH1BuE,GCJG8H,yBAqBTvO,EACAtN,sCAIKsN,OAASA,IACThF,WAAWtI,KACXkU,gBACAgB,mDAGGhB,YAAV,gBACO4H,OAASjf,KAAKif,OAAOvP,KAAK1P,WAC1BuN,MAAQvN,KAAKuN,MAAMmC,KAAK1P,SAG/ByL,WAAA,SACEtI,QAEKA,QAAUnD,KAAKyQ,OAAOwB,uBAAuB9O,MAG1CrD,cAAV,iBACOE,KAAKX,UAAUY,uBACbif,oBAAiB/Q,eAAenO,UAIzC8R,iBAAA,SAAiBnC,QACV0I,mBAGCP,EAA+B,CACnCzY,WAAW,GAGO,YAAhBsQ,EAAO1C,KACT6K,EAAcxO,WAAY,EACD,UAAhBqG,EAAO1C,OAChB6K,EAActO,SAAU,QAGrB0E,OAAO4J,MAGdnK,iBAAA,kBAMS3N,KAAK6Y,iBAGdtL,MAAA,gBACO2R,qBAAkBtY,OAClByR,oBACAnK,OAAO,CAAE7O,WAAW,OAG3B4f,OAAA,SACExN,EACAtO,eAEKgc,cAAgBhc,EAEjBnD,KAAKkf,sBACFA,gBAAgB/Q,eAAenO,WAGjCkf,gBAAkBlf,KAAKyQ,OAAOiG,mBAAmBlG,MAAMxQ,KAAKyQ,YAC5DzQ,KAAKmD,SACRsO,eACuB,IAAdA,EAA4BA,EAAYzR,KAAKmD,QAAQsO,kBAG3DyN,gBAAgBjR,YAAYjO,MAE1BA,KAAKkf,gBAAgB3N,aAGtB8G,aAAR,eACQvU,EAAQ9D,KAAKkf,gBACflf,KAAKkf,gBAAgBpb,MP+IpB,CACLkL,aAASpI,EACT8F,UAAM9F,EACNZ,MAAO,KACP6B,aAAc,EACdmB,UAAU,EACVjF,OAAQ,OACR0N,eAAW7K,GOnJLlC,OAMDZ,GACHyX,UAA4B,YAAjBzX,EAAMC,OACjBmX,UAA4B,YAAjBpX,EAAMC,OACjBuV,QAA0B,UAAjBxV,EAAMC,OACfyX,OAAyB,SAAjB1X,EAAMC,OACdkb,OAAQjf,KAAKif,OACb1R,MAAOvN,KAAKuN,aAGTsL,cAAgBnU,KAQfwJ,OAAR,SAAe/K,cACbkH,EAAcK,OAAM,WAEd5D,EAAKqY,gBACHhc,EAAQmG,iBACVxC,EAAKqY,cAAc7V,WAAnBxC,EAAKqY,cAAc7V,UACjBxC,EAAK+R,cAAcnM,KACnB5F,EAAK+R,cAAcpH,UACnB3K,EAAK+R,cAAc7J,eAErBlI,EAAKqY,cAAcvN,WAAnB9K,EAAKqY,cAAcvN,UACjB9K,EAAK+R,cAAcnM,KACnB,KACA5F,EAAK+R,cAAcpH,UACnB3K,EAAK+R,cAAc7J,UAEZ7L,EAAQqG,gBACjB1C,EAAKqY,cAAc3V,SAAnB1C,EAAKqY,cAAc3V,QACjB1C,EAAK+R,cAAc7S,MACnBc,EAAK+R,cAAcpH,UACnB3K,EAAK+R,cAAc7J,eAErBlI,EAAKqY,cAAcvN,WAAnB9K,EAAKqY,cAAcvN,eACjBhL,EACAE,EAAK+R,cAAc7S,MACnBc,EAAK+R,cAAcpH,UACnB3K,EAAK+R,cAAc7J,WAMrB7L,EAAQ9D,WACVyH,EAAKzH,UAAU2H,SAAQ,SAAAzH,GACrBA,EAASuH,EAAK+R,yBA/JdzZ,GCqCV,SAASggB,GAA+Bzb,UAC/BA,EAASG,MAAMkF,SAGxB,SAASqW,GAA4B3c,SACL,YAAvBA,EAAMoB,MAAMC,OA2Cd,SAASub,GACd7O,EACA8O,EACApc,MAE+B,iBAApBoc,GAAoD,OAApBA,OAIrCjO,EAAgBb,EAAOiG,mBACvBtC,EAAa3D,EAAOuE,gBAEpBhD,EAAauN,EAAoCvN,WAAa,GAC9D1B,EAAWiP,EAAoCjP,SAAW,GAEhE0B,EAAUhL,SAAQ,SAAAwY,SAChBlO,EAAcd,MACZC,aAEKtN,YAAAA,EAASqI,uBAATiU,EAAyBzN,WAC5BpO,YAAa4b,EAAmB5b,cAElC4b,EAAmB1b,UAIvBwM,EAAQtJ,SAAQ,SAAA0Y,SACRhd,EAAQ0R,EAAWtF,IAAI4Q,EAAgBzc,WAGzCP,EACEA,EAAMoB,MAAMoJ,cAAgBwS,EAAgB5b,MAAMoJ,eACpDxK,EAAMyK,SAASuS,EAAgB5b,OAMnCsQ,EAAW5D,MACTC,aAEKtN,YAAAA,EAASqI,uBAATmU,EAAyBrP,SAC5BjO,SAAUqd,EAAgBrd,SAC1BY,UAAWyc,EAAgBzc,YAE7Byc,EAAgB5b,WCtJtB,IAAM8b,GAAiBC,EAAMC,mBAAuClZ,GAC9DmZ,GAA4BF,EAAMC,eAAuB,GAQ/D,SAASE,GAAsBC,UACzBA,GAAoC,oBAAXpf,QACtBA,OAAOqf,0BACVrf,OAAOqf,wBAA0BN,IAG5B/e,OAAOqf,yBAGTN,OAGIO,GAAiB,eACtBC,EAAcP,EAAMQ,WACxBL,GAAsBH,EAAMQ,WAAWN,UAGpCK,QACG,IAAInI,MAAM,iEAGXmI,GC9BT,SAASE,SACHC,GAAU,QACP,CACLC,WAAY,WACVD,GAAU,GAEZhT,MAAO,WACLgT,GAAU,GAEZA,QAAS,kBACAA,IAKb,IAAME,GAAiCZ,EAAMC,cAAcQ,MAI9CI,GAA6B,kBACxCb,EAAMQ,WAAWI,KCtBbE,GAAkB,SACtBP,EACA3d,EACAgB,EACAmd,OAEMC,EAAgBT,EAAY3c,WAAWhB,GACzCgB,IAAeod,GACjBD,EAAcC,IChBX,SAASC,GACd3H,EACA4H,EACAC,SAGiC,mBAAtBD,EACFA,eAAqBC,GAIG,kBAAtBD,EAAwCA,IAG1C5H,ECJJ,SAAS8H,GAOd9d,EAOA+d,OAEMC,EAAatB,EAAMuB,QAAO,GACvBC,EAAexB,EAAMyB,SAAS,MAEjClB,EAAcD,KACdoB,EAAqBb,KACrBtL,EAAmBgL,EAAYnJ,4BAA4B9T,GAGjEiS,EAAiB0F,mBAAoB,EAGjC1F,EAAiB5L,UACnB4L,EAAiB5L,QAAUa,EAAcQ,WACvCuK,EAAiB5L,UAIjB4L,EAAiB9L,YACnB8L,EAAiB9L,UAAYe,EAAcQ,WACzCuK,EAAiB9L,YAIjB8L,EAAiBxD,YACnBwD,EAAiBxD,UAAYvH,EAAcQ,WACzCuK,EAAiBxD,YAIjBwD,EAAiB+D,WAGuB,iBAA/B/D,EAAiBzT,YAC1ByT,EAAiBzT,UAAY,KAKI,IAA/ByT,EAAiBpJ,YACnBoJ,EAAiBpJ,UAAY,KAI7BoJ,EAAiB+D,UAAY/D,EAAiB8D,oBAE3CqI,EAAmBhB,YACtBnL,EAAiBqH,cAAe,QAI7BjP,EAAYqS,EAAMyB,UACvB,kBACE,IAAIJ,EACFd,EACAhL,SAIF1Q,EAAS8I,EAASmL,oBAAoBvD,MAE1CyK,EAAM2B,WAAU,WACdL,EAAWM,SAAU,EAErBF,EAAmBf,iBAEbnH,EAAc7L,EAASlO,UAC3B+K,EAAcQ,YAAW,WACnBsW,EAAWM,SACbJ,GAAY,SAAAxhB,UAAKA,EAAI,gBAO3B2N,EAAS6K,eAEF,WACL8I,EAAWM,SAAU,EACrBpI,OAED,CAACkI,EAAoB/T,IAExBqS,EAAM2B,WAAU,WAGdhU,EAAS/B,WAAW2J,EAAkB,CAAE/V,WAAW,MAClD,CAAC+V,EAAkB5H,IAGlB4H,EAAiB+D,UAAYzU,EAAO6W,gBAChC/N,EACHgM,gBAAgBpE,GAChBtP,MAAK,gBAAG4G,IAAAA,WACP0I,EAAiB9L,WAAjB8L,EAAiB9L,UAAYoD,SAC7B0I,EAAiBxD,WAAjBwD,EAAiBxD,UAAYlF,EAAM,SAEpC3G,OAAM,SAAAC,GACLub,EAAmBf,mBACnBpL,EAAiB5L,SAAjB4L,EAAiB5L,QAAUxD,SAC3BoP,EAAiBxD,WAAjBwD,EAAiBxD,eAAYhL,EAAWZ,SAM5CtB,EAAO4U,UACNiI,EAAmBhB,YACnB7b,EAAOjB,YACRqd,GACE1L,EAAiB+D,SACjB/D,EAAiB8D,iBACjB,CAACxU,EAAOsB,MAAOwH,EAAS+L,0BAGpB7U,EAAOsB,YAI8B,YAAzCoP,EAAiB2G,sBACnBrX,EAAS8I,EAASsL,YAAYpU,EAAQ0Q,IAGjC1Q,EChJF,SAASgd,GAAW5d,EAAgBX,OACnCid,EAAcD,KAEdwB,EAAa9B,EAAMuB,OAAOje,GAChCwe,EAAWF,QAAUte,EAMrB0c,EAAM+B,SAAQ,WACR9d,GACFwb,GAAQc,EAAatc,EAAO6d,EAAWF,WAExC,CAACrB,EAAatc,iCASI,gBAAG+d,IAAAA,SAAU1e,IAAAA,eAClCue,KAD2C5d,MACzBX,GACX0e,6ILmB0B,gBACjCpR,IAAAA,WACAwP,eAAAA,gBACA4B,IAAAA,SAEAhC,EAAM2B,WAAU,kBACd/Q,EAAO8D,QACA,WACL9D,EAAOiE,aAER,CAACjE,QAEEqR,EAAU9B,GAAsBC,UAGpCJ,gBAACE,GAA0BgC,UAAS/gB,MAAOif,GACzCJ,gBAACiC,EAAQC,UAAS/gB,MAAOyP,GAASoR,+BCzBD,gBACrCA,IAAAA,SAEM7gB,EAAQ6e,EAAM+B,SAAQ,kBAAMtB,OAAe,WAE/CT,gBAACY,GAA+BsB,UAAS/gB,MAAOA,GACzB,mBAAb6gB,EACHA,EAAsB7gB,GACvB6gB,kCF2BH,SACLpR,EACAtN,WAIM6O,EAAkC,GAClC1B,EAA6B,OAEC,cALpCnN,EAAUA,GAAW,aAKR6e,oBAA8B,KACnCC,EACJ9e,EAAQ8e,yBAA2B7C,GAErC3O,EACGiG,mBACA3F,SACA/J,SAAQ,SAAArD,GACHse,EAAwBte,IAC1BqO,EAAUvS,KA7CpB,SAA2BkE,SAClB,CACLC,YAAaD,EAASR,QAAQS,YAC9BE,MAAOH,EAASG,OA0CKoe,CAAkBve,WAKP,cAA9BR,YAASgf,kBAA4B,KACjCC,EACJjf,EAAQif,sBAAwB/C,GAElC5O,EACGuE,gBACAjE,SACA/J,SAAQ,SAAAtE,GACH0f,EAAqB1f,IACvB4N,EAAQ7Q,KAhDlB,SAAwBiD,SACf,CACLoB,MAAOpB,EAAMoB,MACbzB,SAAUK,EAAML,SAChBY,UAAWP,EAAMO,WA4CEof,CAAe3f,aAK7B,CAAEsP,UAAAA,EAAW1B,QAAAA,kFhB0Tf,SAAiBtP,UACfA,aAAiBiX,uDKzZnB,SAAmBqK,GACxBpX,EAASoX,sCkByCJ,SAMLrgB,EASAC,EASAC,UASO8e,GADSjf,EAAeC,EAAMC,EAAMC,GAGzCuc,oBJtEG,SACLzc,EACAC,OAEMif,EAAatB,EAAMuB,QAAO,GAE1BhB,EAAcD,KAEb1d,EAAWF,EAAgBN,EAAMC,QACJ2d,EAAMyB,SACxClB,EAAY3c,WAAWhB,IADlBgB,OAAYmd,OAIb2B,EAAa1C,EAAMuB,OAAO3e,GAChC8f,EAAWd,QAAUhf,MACf+f,EAAgB3C,EAAMuB,OAAO3d,UACnC+e,EAAcf,QAAUhe,EAExBoc,EAAM2B,WAAU,WACdL,EAAWM,SAAU,EAErBd,GACEP,EACAmC,EAAWd,QACXe,EAAcf,QACdb,OAGIvH,EAAc+G,EAAYpL,gBAAgB1V,UAC9C+K,EAAcQ,YAAW,WACnBsW,EAAWM,SACbd,GACEP,EACAmC,EAAWd,QACXe,EAAcf,QACdb,cAMD,WACLO,EAAWM,SAAU,EACrBpI,OAED,CAAC+G,IAEG3c,mBK5DF,SACLxB,EACAC,OAEMif,EAAatB,EAAMuB,QAAO,GAC1B3e,ExBuJD,SACLR,EACAC,UAEOE,EAAWH,QAAaC,GAAM0B,YAAa3B,IAASA,EwB3J3CwgB,CAAwBxgB,EAAMC,GAExCke,EAAcD,OAEgBN,EAAMyB,SACxClB,EAAYzL,WAAWlS,IADlBkS,OAAY+N,OAIbH,EAAa1C,EAAMuB,OAAO3e,GAChC8f,EAAWd,QAAUhf,MACfkgB,EAAgB9C,EAAMuB,OAAOzM,UACnCgO,EAAclB,QAAU9M,EAExBkL,EAAM2B,WAAU,WACdL,EAAWM,SAAU,MAEfpI,EAAc+G,EAAY1J,mBAAmBpX,UACjD+K,EAAcQ,YAAW,cACnBsW,EAAWM,QAAS,KAChBmB,EAAgBxC,EAAYzL,WAAW4N,EAAWd,SACpDkB,EAAclB,UAAYmB,GAC5BF,EAAcE,eAMf,WACLzB,EAAWM,SAAU,EACrBpI,OAED,CAAC+G,IAEGzL,iBCWF,SAML1S,EAIAC,EAGAC,OAEMgf,EAAatB,EAAMuB,QAAO,GACvBC,EAAexB,EAAMyB,SAAS,MAEjCne,EzBuDD,SAGLlB,EACAC,EACAC,UAEIC,EAAWH,GACO,mBAATC,OACGC,GAAMyB,YAAa3B,EAAM4P,WAAY3P,SAEvCA,GAAM0B,YAAa3B,IAGb,mBAATA,OACGC,GAAM2P,WAAY5P,SAGpBA,GyBzEI4gB,CAAkB5gB,EAAMC,EAAMC,GACxCie,EAAcD,KAEd2C,EAASjD,EAAMuB,SAIhB0B,EAAOrB,QAGVqB,EAAOrB,QAAQhW,WAAWtI,GAF1B2f,EAAOrB,QAAU,IAAIzC,EAAiBoB,EAAajd,OAK/C0V,EAAgBiK,EAAOrB,QAAQ9T,mBAErCkS,EAAM2B,WAAU,WACdL,EAAWM,SAAU,MAEfpI,EAAcyJ,EAAOrB,QAASniB,UAClC+K,EAAcQ,YAAW,WACnBsW,EAAWM,SACbJ,GAAY,SAAAxhB,UAAKA,EAAI,gBAIpB,WACLshB,EAAWM,SAAU,EACrBpI,OAED,QAEG4F,EAASY,EAAMkD,aAEnB,SAACtR,EAAW0N,GACZ2D,EAAOrB,QAASxC,OAAOxN,EAAW0N,GAAepZ,MAAMjF,KACtD,OAGD+X,EAAc7S,OACd8a,QAAiBla,EAAWkc,EAAOrB,QAAQte,QAAQ+V,iBAAkB,CACnEL,EAAc7S,cAGV6S,EAAc7S,kBAGV6S,GAAeoG,OAAAA,EAAQ+D,YAAanK,EAAcoG,uBCHzD,SACL3O,OAEM6Q,EAAatB,EAAMuB,QAAO,GACvBC,EAAexB,EAAMyB,SAAS,MAEjClB,EAAcD,KAEd8C,EAAmBrB,WACvB,kBACEtR,EAAQ2E,KAAI,SAAA9R,OACJiS,EAAmBgL,EAAYnJ,4BACnC9T,UAIFiS,EAAiB0F,mBAAoB,EAE9B1F,OAEX,CAAC9E,EAAS8P,IAGL5S,EAAYqS,EAAMyB,UACvB,kBAAM,IAAIzE,EAAgBuD,EAAa6C,SAGnCve,EAAS8I,EAASmL,oBAAoBsK,UAE5CpD,EAAM2B,WAAU,WACdL,EAAWM,SAAU,MAEfpI,EAAc7L,EAASlO,UAC3B+K,EAAcQ,YAAW,WACnBsW,EAAWM,SACbJ,GAAY,SAAAxhB,UAAKA,EAAI,gBAKpB,WACLshB,EAAWM,SAAU,EACrBpI,OAED,CAAC7L,IAEJqS,EAAM2B,WAAU,WAGdhU,EAASuP,WAAWkG,EAAkB,CAAE5jB,WAAW,MAClD,CAAC4jB,EAAkBzV,IAEf9I,cCrIF,SAMLzC,EACAC,EAGAC,UAGO8e,GADejf,EAAeC,EAAMC,EAAMC,GACd+U"}