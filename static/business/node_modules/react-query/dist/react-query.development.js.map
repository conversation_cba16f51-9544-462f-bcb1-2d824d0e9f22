{"version": 3, "file": "react-query.development.js", "sources": ["../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "../src/core/subscribable.ts", "../node_modules/@babel/runtime/helpers/esm/extends.js", "../src/core/utils.ts", "../src/core/focusManager.ts", "../src/core/onlineManager.ts", "../src/core/retryer.ts", "../src/core/notifyManager.ts", "../src/core/logger.ts", "../src/core/query.ts", "../src/core/queryCache.ts", "../src/core/mutation.ts", "../src/core/mutationCache.ts", "../src/core/infiniteQueryBehavior.ts", "../src/core/queryClient.ts", "../src/core/queryObserver.ts", "../src/core/queriesObserver.ts", "../src/core/infiniteQueryObserver.ts", "../src/core/mutationObserver.ts", "../src/core/hydration.ts", "../src/react/QueryClientProvider.tsx", "../src/react/QueryErrorResetBoundary.tsx", "../src/react/useIsFetching.ts", "../src/react/useIsMutating.ts", "../src/react/utils.ts", "../src/react/useMutation.ts", "../src/react/useBaseQuery.ts", "../src/react/useQuery.ts", "../src/react/useQueries.ts", "../src/react/useInfiniteQuery.ts", "../src/react/Hydrate.tsx"], "sourcesContent": ["export default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}", "type Listener = () => void\n\nexport class Subscribable<TListener extends Function = Listener> {\n  protected listeners: TListener[]\n\n  constructor() {\n    this.listeners = []\n  }\n\n  subscribe(listener?: TListener): () => void {\n    const callback = listener || (() => undefined)\n\n    this.listeners.push(callback as TListener)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners = this.listeners.filter(x => x !== callback)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.length > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n", "export default function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}", "import type { Mutation } from './mutation'\nimport type { Query } from './query'\nimport { EnsuredQueryKey } from './types'\nimport type {\n  MutationFunction,\n  MutationKey,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n} from './types'\n\n// TYPES\n\nexport interface QueryFilters {\n  /**\n   * Include or exclude active queries\n   */\n  active?: boolean\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include or exclude inactive queries\n   */\n  inactive?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: QueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include or exclude fetching queries\n   */\n  fetching?: boolean\n}\n\nexport interface MutationFilters {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (mutation: Mutation<any, any, any>) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: Mutation<PERSON>ey\n  /**\n   * Include or exclude fetching mutations\n   */\n  fetching?: boolean\n}\n\nexport type DataUpdateFunction<TInput, TOutput> = (input: TInput) => TOutput\n\nexport type Updater<TInput, TOutput> =\n  | TOutput\n  | DataUpdateFunction<TInput, TOutput>\n\nexport type QueryStatusFilter = 'all' | 'active' | 'inactive' | 'none'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined'\n\nexport function noop(): undefined {\n  return undefined\n}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as DataUpdateFunction<TInput, TOutput>)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function ensureQueryKeyArray<T extends QueryKey>(\n  value: T\n): EnsuredQueryKey<T> {\n  return (Array.isArray(value)\n    ? value\n    : ([value] as unknown)) as EnsuredQueryKey<T>\n}\n\nexport function difference<T>(array1: T[], array2: T[]): T[] {\n  return array1.filter(x => array2.indexOf(x) === -1)\n}\n\nexport function replaceAt<T>(array: T[], index: number, value: T): T[] {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function parseQueryArgs<\n  TOptions extends QueryOptions<any, any, any, TQueryKey>,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  arg1: TQueryKey | TOptions,\n  arg2?: QueryFunction<any, TQueryKey> | TOptions,\n  arg3?: TOptions\n): TOptions {\n  if (!isQueryKey(arg1)) {\n    return arg1 as TOptions\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3, queryKey: arg1, queryFn: arg2 } as TOptions\n  }\n\n  return { ...arg2, queryKey: arg1 } as TOptions\n}\n\nexport function parseMutationArgs<\n  TOptions extends MutationOptions<any, any, any, any>\n>(\n  arg1: MutationKey | MutationFunction<any, any> | TOptions,\n  arg2?: MutationFunction<any, any> | TOptions,\n  arg3?: TOptions\n): TOptions {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3, mutationKey: arg1, mutationFn: arg2 } as TOptions\n    }\n    return { ...arg2, mutationKey: arg1 } as TOptions\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2, mutationFn: arg1 } as TOptions\n  }\n\n  return { ...arg1 } as TOptions\n}\n\nexport function parseFilterArgs<\n  TFilters extends QueryFilters,\n  TOptions = unknown\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions\n): [TFilters, TOptions | undefined] {\n  return (isQueryKey(arg1)\n    ? [{ ...arg2, queryKey: arg1 }, arg3]\n    : [arg1 || {}, arg2]) as [TFilters, TOptions]\n}\n\nexport function parseMutationFilterArgs(\n  arg1?: QueryKey | MutationFilters,\n  arg2?: MutationFilters\n): MutationFilters | undefined {\n  return isQueryKey(arg1) ? { ...arg2, mutationKey: arg1 } : arg1\n}\n\nexport function mapQueryStatusFilter(\n  active?: boolean,\n  inactive?: boolean\n): QueryStatusFilter {\n  if (\n    (active === true && inactive === true) ||\n    (active == null && inactive == null)\n  ) {\n    return 'all'\n  } else if (active === false && inactive === false) {\n    return 'none'\n  } else {\n    // At this point, active|inactive can only be true|false or false|true\n    // so, when only one value is provided, the missing one has to be the negated value\n    const isActive = active ?? !inactive\n    return isActive ? 'active' : 'inactive'\n  }\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>\n): boolean {\n  const {\n    active,\n    exact,\n    fetching,\n    inactive,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  const queryStatusFilter = mapQueryStatusFilter(active, inactive)\n\n  if (queryStatusFilter === 'none') {\n    return false\n  } else if (queryStatusFilter !== 'all') {\n    const isActive = query.isActive()\n    if (queryStatusFilter === 'active' && !isActive) {\n      return false\n    }\n    if (queryStatusFilter === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (typeof fetching === 'boolean' && query.isFetching() !== fetching) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>\n): boolean {\n  const { exact, fetching, predicate, mutationKey } = filters\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (\n        hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)\n      ) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (\n    typeof fetching === 'boolean' &&\n    (mutation.state.status === 'loading') !== fetching\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: QueryOptions<any, any, any, TQueryKey>\n): string {\n  const hashFn = options?.queryKeyHashFn || hashQueryKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query keys hash function.\n */\nexport function hashQueryKey(queryKey: QueryKey): string {\n  const asArray = ensureQueryKeyArray(queryKey)\n  return stableValueHash(asArray)\n}\n\n/**\n * Hashes the value into a stable hash.\n */\nexport function stableValueHash(value: any): string {\n  return JSON.stringify(value, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean {\n  return partialDeepEqual(ensureQueryKeyArray(a), ensureQueryKeyArray(b))\n}\n\n/**\n * Checks if `b` partially matches with `a`.\n */\nexport function partialDeepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(key => !partialDeepEqual(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = Array.isArray(a) && Array.isArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aSize = array ? a.length : Object.keys(a).length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      copy[key] = replaceEqualDeep(a[key], b[key])\n      if (copy[key] === a[key]) {\n        equalItems++\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\nexport function shallowEqualObjects<T>(a: T, b: T): boolean {\n  if ((a && !b) || (b && !a)) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isQueryKey(value: any): value is QueryKey {\n  return typeof value === 'string' || Array.isArray(value)\n}\n\nexport function isError(value: any): value is Error {\n  return value instanceof Error\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise(resolve => {\n    setTimeout(resolve, timeout)\n  })\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nexport function scheduleMicrotask(callback: () => void): void {\n  Promise.resolve()\n    .then(callback)\n    .catch(error =>\n      setTimeout(() => {\n        throw error\n      })\n    )\n}\n\nexport function getAbortController(): AbortController | undefined {\n  if (typeof AbortController === 'function') {\n    return new AbortController()\n  }\n}\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable {\n  private focused?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = onFocus => {\n      if (!isServer && window?.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibillitychange and focus\n        window.addEventListener('visibilitychange', listener, false)\n        window.addEventListener('focus', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n          window.removeEventListener('focus', listener)\n        }\n      }\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup(focused => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    this.focused = focused\n\n    if (focused) {\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    this.listeners.forEach(listener => {\n      listener()\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.focused === 'boolean') {\n      return this.focused\n    }\n\n    // document global can be unavailable in react native\n    if (typeof document === 'undefined') {\n      return true\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(\n      document.visibilityState\n    )\n  }\n}\n\nexport const focusManager = new FocusManager()\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setOnline: (online?: boolean) => void\n) => (() => void) | undefined\n\nexport class OnlineManager extends Subscribable {\n  private online?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = onOnline => {\n      if (!isServer && window?.addEventListener) {\n        const listener = () => onOnline()\n        // Listen to online\n        window.addEventListener('online', listener, false)\n        window.addEventListener('offline', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', listener)\n          window.removeEventListener('offline', listener)\n        }\n      }\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((online?: boolean) => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online)\n      } else {\n        this.onOnline()\n      }\n    })\n  }\n\n  setOnline(online?: boolean): void {\n    this.online = online\n\n    if (online) {\n      this.onOnline()\n    }\n  }\n\n  onOnline(): void {\n    this.listeners.forEach(listener => {\n      listener()\n    })\n  }\n\n  isOnline(): boolean {\n    if (typeof this.online === 'boolean') {\n      return this.online\n    }\n\n    if (\n      typeof navigator === 'undefined' ||\n      typeof navigator.onLine === 'undefined'\n    ) {\n      return true\n    }\n\n    return navigator.onLine\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n", "import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { sleep } from './utils'\nimport { CancelOptions } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = unknown> {\n  fn: () => TData | Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError = unknown> = (\n  failureCount: number,\n  error: TError\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = unknown> = (\n  failureCount: number,\n  error: TError\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\ninterface Cancelable {\n  cancel(): void\n}\n\nexport function isCancelable(value: any): value is Cancelable {\n  return typeof value?.cancel === 'function'\n}\n\nexport class CancelledError {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\n// CLASS\n\nexport class Retryer<TData = unknown, TError = unknown> {\n  cancel: (options?: CancelOptions) => void\n  cancelRetry: () => void\n  continueRetry: () => void\n  continue: () => void\n  failureCount: number\n  isPaused: boolean\n  isResolved: boolean\n  isTransportCancelable: boolean\n  promise: Promise<TData>\n\n  private abort?: () => void\n\n  constructor(config: RetryerConfig<TData, TError>) {\n    let cancelRetry = false\n    let cancelFn: ((options?: CancelOptions) => void) | undefined\n    let continueFn: ((value?: unknown) => void) | undefined\n    let promiseResolve: (data: TData) => void\n    let promiseReject: (error: TError) => void\n\n    this.abort = config.abort\n    this.cancel = cancelOptions => cancelFn?.(cancelOptions)\n    this.cancelRetry = () => {\n      cancelRetry = true\n    }\n    this.continueRetry = () => {\n      cancelRetry = false\n    }\n    this.continue = () => continueFn?.()\n    this.failureCount = 0\n    this.isPaused = false\n    this.isResolved = false\n    this.isTransportCancelable = false\n    this.promise = new Promise<TData>((outerResolve, outerReject) => {\n      promiseResolve = outerResolve\n      promiseReject = outerReject\n    })\n\n    const resolve = (value: any) => {\n      if (!this.isResolved) {\n        this.isResolved = true\n        config.onSuccess?.(value)\n        continueFn?.()\n        promiseResolve(value)\n      }\n    }\n\n    const reject = (value: any) => {\n      if (!this.isResolved) {\n        this.isResolved = true\n        config.onError?.(value)\n        continueFn?.()\n        promiseReject(value)\n      }\n    }\n\n    const pause = () => {\n      return new Promise(continueResolve => {\n        continueFn = continueResolve\n        this.isPaused = true\n        config.onPause?.()\n      }).then(() => {\n        continueFn = undefined\n        this.isPaused = false\n        config.onContinue?.()\n      })\n    }\n\n    // Create loop function\n    const run = () => {\n      // Do nothing if already resolved\n      if (this.isResolved) {\n        return\n      }\n\n      let promiseOrValue: any\n\n      // Execute query\n      try {\n        promiseOrValue = config.fn()\n      } catch (error) {\n        promiseOrValue = Promise.reject(error)\n      }\n\n      // Create callback to cancel this fetch\n      cancelFn = cancelOptions => {\n        if (!this.isResolved) {\n          reject(new CancelledError(cancelOptions))\n\n          this.abort?.()\n\n          // Cancel transport if supported\n          if (isCancelable(promiseOrValue)) {\n            try {\n              promiseOrValue.cancel()\n            } catch {}\n          }\n        }\n      }\n\n      // Check if the transport layer support cancellation\n      this.isTransportCancelable = isCancelable(promiseOrValue)\n\n      Promise.resolve(promiseOrValue)\n        .then(resolve)\n        .catch(error => {\n          // Stop if the fetch is already resolved\n          if (this.isResolved) {\n            return\n          }\n\n          // Do we need to retry the request?\n          const retry = config.retry ?? 3\n          const retryDelay = config.retryDelay ?? defaultRetryDelay\n          const delay =\n            typeof retryDelay === 'function'\n              ? retryDelay(this.failureCount, error)\n              : retryDelay\n          const shouldRetry =\n            retry === true ||\n            (typeof retry === 'number' && this.failureCount < retry) ||\n            (typeof retry === 'function' && retry(this.failureCount, error))\n\n          if (cancelRetry || !shouldRetry) {\n            // We are done if the query does not need to be retried\n            reject(error)\n            return\n          }\n\n          this.failureCount++\n\n          // Notify on fail\n          config.onFail?.(this.failureCount, error)\n\n          // Delay\n          sleep(delay)\n            // Pause if the document is not visible or when the device is offline\n            .then(() => {\n              if (!focusManager.isFocused() || !onlineManager.isOnline()) {\n                return pause()\n              }\n            })\n            .then(() => {\n              if (cancelRetry) {\n                reject(error)\n              } else {\n                run()\n              }\n            })\n        })\n    }\n\n    // Start loop\n    run()\n  }\n}\n", "import { scheduleMicrotask } from './utils'\n\n// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\n// CLASS\n\nexport class NotifyManager {\n  private queue: NotifyCallback[]\n  private transactions: number\n  private notifyFn: NotifyFunction\n  private batchNotifyFn: BatchNotifyFunction\n\n  constructor() {\n    this.queue = []\n    this.transactions = 0\n\n    this.notifyFn = (callback: () => void) => {\n      callback()\n    }\n\n    this.batchNotifyFn = (callback: () => void) => {\n      callback()\n    }\n  }\n\n  batch<T>(callback: () => T): T {\n    let result\n    this.transactions++\n    try {\n      result = callback()\n    } finally {\n      this.transactions--\n      if (!this.transactions) {\n        this.flush()\n      }\n    }\n    return result\n  }\n\n  schedule(callback: NotifyCallback): void {\n    if (this.transactions) {\n      this.queue.push(callback)\n    } else {\n      scheduleMicrotask(() => {\n        this.notifyFn(callback)\n      })\n    }\n  }\n\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  batchCalls<T extends Function>(callback: T): T {\n    return ((...args: any[]) => {\n      this.schedule(() => {\n        callback(...args)\n      })\n    }) as any\n  }\n\n  flush(): void {\n    const queue = this.queue\n    this.queue = []\n    if (queue.length) {\n      scheduleMicrotask(() => {\n        this.batchNotifyFn(() => {\n          queue.forEach(callback => {\n            this.notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  setNotifyFunction(fn: NotifyFunction) {\n    this.notifyFn = fn\n  }\n\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  setBatchNotifyFunction(fn: BatchNotifyFunction) {\n    this.batchNotifyFn = fn\n  }\n}\n\n// SINGLETON\n\nexport const notifyManager = new NotifyManager()\n", "// TYPES\n\nexport interface Logger {\n  log: LogFunction\n  warn: LogFunction\n  error: LogFunction\n}\n\ntype LogFunction = (...args: any[]) => void\n\n// FUNCTIONS\n\nlet logger: Logger = console\n\nexport function getLogger(): Logger {\n  return logger\n}\n\nexport function setLogger(newLogger: Logger) {\n  logger = newLogger\n}\n", "import {\n  getAbort<PERSON><PERSON>roller,\n  Updater,\n  functionalUpdate,\n  isValidTimeout,\n  noop,\n  replaceEqual<PERSON>eep,\n  timeUntilStale,\n  ensureQueryKeyArray,\n} from './utils'\nimport type {\n  InitialDataFunction,\n  QueryKey,\n  QueryOptions,\n  QueryStatus,\n  QueryFunctionContext,\n  EnsuredQueryKey,\n  QueryMeta,\n  CancelOptions,\n  SetDataOptions,\n} from './types'\nimport type { QueryCache } from './queryCache'\nimport type { QueryObserver } from './queryObserver'\nimport { notifyManager } from './notifyManager'\nimport { getLogger } from './logger'\nimport { Retryer, isCancelledError } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey\n> {\n  cache: QueryCache\n  queryKey: TQueryKey\n  queryHash: string\n  options?: QueryOptions<TQueryFnData, TE<PERSON>r, TData, TQ<PERSON>yKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n  meta: QueryMeta | undefined\n}\n\nexport interface QueryState<TData = unknown, TError = unknown> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchMeta: any\n  isFetching: boolean\n  isInvalidated: boolean\n  isPaused: boolean\n  status: QueryStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  queryKey: EnsuredQueryKey<TQueryKey>\n  state: QueryState<TData, TError>\n  meta: QueryMeta | undefined\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>\n  ) => void\n}\n\nexport interface FetchOptions {\n  cancelRefetch?: boolean\n  meta?: any\n}\n\ninterface FailedAction {\n  type: 'failed'\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: any\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: QueryState<TData, TError>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n> {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  initialState: QueryState<TData, TError>\n  revertState?: QueryState<TData, TError>\n  state: QueryState<TData, TError>\n  cacheTime!: number\n  meta: QueryMeta | undefined\n\n  private cache: QueryCache\n  private promise?: Promise<TData>\n  private gcTimeout?: number\n  private retryer?: Retryer<TData, TError>\n  private observers: QueryObserver<any, any, any, any, any>[]\n  private defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  private abortSignalConsumed: boolean\n  private hadObservers: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    this.abortSignalConsumed = false\n    this.hadObservers = false\n    this.defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.cache = config.cache\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.initialState = config.state || this.getDefaultState(this.options)\n    this.state = this.initialState\n    this.meta = config.meta\n    this.scheduleGc()\n  }\n\n  private setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.meta = options?.meta\n\n    // Default to 5 minutes if not cache time is set\n    this.cacheTime = Math.max(\n      this.cacheTime || 0,\n      this.options.cacheTime ?? 5 * 60 * 1000\n    )\n  }\n\n  setDefaultOptions(\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): void {\n    this.defaultOptions = options\n  }\n\n  private scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.cacheTime)\n    }\n  }\n\n  private clearGcTimeout() {\n    clearTimeout(this.gcTimeout)\n    this.gcTimeout = undefined\n  }\n\n  private optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.isFetching) {\n        if (this.hadObservers) {\n          this.scheduleGc()\n        }\n      } else {\n        this.cache.remove(this)\n      }\n    }\n  }\n\n  setData(\n    updater: Updater<TData | undefined, TData>,\n    options?: SetDataOptions\n  ): TData {\n    const prevData = this.state.data\n\n    // Get the new data\n    let data = functionalUpdate(updater, prevData)\n\n    // Use prev data if an isDataEqual function is defined and returns `true`\n    if (this.options.isDataEqual?.(prevData, data)) {\n      data = prevData as TData\n    } else if (this.options.structuralSharing !== false) {\n      // Structurally share data between prev and new data if needed\n      data = replaceEqualDeep(prevData, data)\n    }\n\n    // Set data and mark it as cached\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n    })\n\n    return data\n  }\n\n  setState(\n    state: QueryState<TData, TError>,\n    setStateOptions?: SetStateOptions\n  ): void {\n    this.dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.promise\n    this.retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    this.clearGcTimeout()\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some(observer => observer.options.enabled !== false)\n  }\n\n  isFetching(): boolean {\n    return this.state.isFetching\n  }\n\n  isStale(): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      this.observers.some(observer => observer.getCurrentResult().isStale)\n    )\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus())\n\n    if (observer) {\n      observer.refetch()\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect())\n\n    if (observer) {\n      observer.refetch()\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer)\n      this.hadObservers = true\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.indexOf(observer) !== -1) {\n      this.observers = this.observers.filter(x => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.retryer.isTransportCancelable || this.abortSignalConsumed) {\n            this.retryer.cancel({ revert: true })\n          } else {\n            this.retryer.cancelRetry()\n          }\n        }\n\n        if (this.cacheTime) {\n          this.scheduleGc()\n        } else {\n          this.cache.remove(this)\n        }\n      }\n\n      this.cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions\n  ): Promise<TData> {\n    if (this.state.isFetching) {\n      if (this.state.dataUpdatedAt && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({ silent: true })\n      } else if (this.promise) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.retryer?.continueRetry()\n        // Return current promise if we are already fetching\n        return this.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    const queryKey = ensureQueryKeyArray(this.queryKey)\n    const abortController = getAbortController()\n\n    // Create query function context\n    const queryFnContext: QueryFunctionContext<TQueryKey> = {\n      queryKey,\n      pageParam: undefined,\n      meta: this.meta,\n    }\n\n    Object.defineProperty(queryFnContext, 'signal', {\n      enumerable: true,\n      get: () => {\n        if (abortController) {\n          this.abortSignalConsumed = true\n          return abortController.signal\n        }\n        return undefined\n      },\n    })\n\n    // Create fetch function\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject('Missing queryFn')\n      }\n      this.abortSignalConsumed = false\n      return this.options.queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const context: FetchContext<TQueryFnData, TError, TData, TQueryKey> = {\n      fetchOptions,\n      options: this.options,\n      queryKey: queryKey,\n      state: this.state,\n      fetchFn,\n      meta: this.meta,\n    }\n\n    if (this.options.behavior?.onFetch) {\n      this.options.behavior?.onFetch(context)\n    }\n\n    // Store state in case the current fetch needs to be reverted\n    this.revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      !this.state.isFetching ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    // Try to fetch the data\n    this.retryer = new Retryer({\n      fn: context.fetchFn as () => TData,\n      abort: abortController?.abort?.bind(abortController),\n      onSuccess: data => {\n        this.setData(data as TData)\n\n        // Notify cache callback\n        this.cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n\n        // Remove query after fetching if cache time is 0\n        if (this.cacheTime === 0) {\n          this.optionalRemove()\n        }\n      },\n      onError: (error: TError | { silent?: boolean }) => {\n        // Optimistically update state if needed\n        if (!(isCancelledError(error) && error.silent)) {\n          this.dispatch({\n            type: 'error',\n            error: error as TError,\n          })\n        }\n\n        if (!isCancelledError(error)) {\n          // Notify cache callback\n          this.cache.config.onError?.(error, this as Query<any, any, any, any>)\n\n          // Log error\n          getLogger().error(error)\n        }\n\n        // Remove query after fetching if cache time is 0\n        if (this.cacheTime === 0) {\n          this.optionalRemove()\n        }\n      },\n      onFail: () => {\n        this.dispatch({ type: 'failed' })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n    })\n\n    this.promise = this.retryer.promise\n\n    return this.promise\n  }\n\n  private dispatch(action: Action<TData, TError>): void {\n    this.state = this.reducer(this.state, action)\n\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate(action)\n      })\n\n      this.cache.notify({ query: this, type: 'queryUpdated', action })\n    })\n  }\n\n  protected getDefaultState(\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): QueryState<TData, TError> {\n    const data =\n      typeof options.initialData === 'function'\n        ? (options.initialData as InitialDataFunction<TData>)()\n        : options.initialData\n\n    const hasInitialData = typeof options.initialData !== 'undefined'\n\n    const initialDataUpdatedAt = hasInitialData\n      ? typeof options.initialDataUpdatedAt === 'function'\n        ? (options.initialDataUpdatedAt as () => number | undefined)()\n        : options.initialDataUpdatedAt\n      : 0\n\n    const hasData = typeof data !== 'undefined'\n\n    return {\n      data,\n      dataUpdateCount: 0,\n      dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n      error: null,\n      errorUpdateCount: 0,\n      errorUpdatedAt: 0,\n      fetchFailureCount: 0,\n      fetchMeta: null,\n      isFetching: false,\n      isInvalidated: false,\n      isPaused: false,\n      status: hasData ? 'success' : 'idle',\n    }\n  }\n\n  protected reducer(\n    state: QueryState<TData, TError>,\n    action: Action<TData, TError>\n  ): QueryState<TData, TError> {\n    switch (action.type) {\n      case 'failed':\n        return {\n          ...state,\n          fetchFailureCount: state.fetchFailureCount + 1,\n        }\n      case 'pause':\n        return {\n          ...state,\n          isPaused: true,\n        }\n      case 'continue':\n        return {\n          ...state,\n          isPaused: false,\n        }\n      case 'fetch':\n        return {\n          ...state,\n          fetchFailureCount: 0,\n          fetchMeta: action.meta ?? null,\n          isFetching: true,\n          isPaused: false,\n          ...(!state.dataUpdatedAt && {\n            error: null,\n            status: 'loading',\n          }),\n        }\n      case 'success':\n        return {\n          ...state,\n          data: action.data,\n          dataUpdateCount: state.dataUpdateCount + 1,\n          dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n          error: null,\n          fetchFailureCount: 0,\n          isFetching: false,\n          isInvalidated: false,\n          isPaused: false,\n          status: 'success',\n        }\n      case 'error':\n        const error = action.error as unknown\n\n        if (isCancelledError(error) && error.revert && this.revertState) {\n          return { ...this.revertState }\n        }\n\n        return {\n          ...state,\n          error: error as TError,\n          errorUpdateCount: state.errorUpdateCount + 1,\n          errorUpdatedAt: Date.now(),\n          fetchFailureCount: state.fetchFailureCount + 1,\n          isFetching: false,\n          isPaused: false,\n          status: 'error',\n        }\n      case 'invalidate':\n        return {\n          ...state,\n          isInvalidated: true,\n        }\n      case 'setState':\n        return {\n          ...state,\n          ...action.state,\n        }\n      default:\n        return state\n    }\n  }\n}\n", "import {\n  QueryFilters,\n  hashQueryKeyByOptions,\n  matchQuery,\n  parseFilterArgs,\n} from './utils'\nimport { Action, Query, QueryState } from './query'\nimport type { QueryKey, QueryOptions } from './types'\nimport { notifyManager } from './notifyManager'\nimport type { QueryClient } from './queryClient'\nimport { Subscribable } from './subscribable'\nimport { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (error: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n}\n\ninterface QueryHashMap {\n  [hash: string]: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryAdded {\n  type: 'queryAdded'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved {\n  type: 'queryRemoved'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated {\n  type: 'queryUpdated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventObserverAdded {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventObserverRemoved {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventObserverResultsUpdated {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ntype QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventObserverAdded\n  | NotifyEventObserverRemoved\n  | NotifyEventObserverResultsUpdated\n\ntype QueryCacheListener = (event?: QueryCacheNotifyEvent) => void\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  config: QueryCacheConfig\n\n  private queries: Query<any, any, any, any>[]\n  private queriesMap: QueryHashMap\n\n  constructor(config?: QueryCacheConfig) {\n    super()\n    this.config = config || {}\n    this.queries = []\n    this.queriesMap = {}\n  }\n\n  build<TQueryFnData, TError, TData, TQueryKey extends QueryKey>(\n    client: QueryClient,\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    state?: QueryState<TData, TError>\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey!\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n        meta: options.meta,\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query\n      this.queries.push(query)\n      this.notify({\n        type: 'queryAdded',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.queriesMap[query.queryHash]\n\n    if (queryInMap) {\n      query.destroy()\n\n      this.queries = this.queries.filter(x => x !== query)\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash]\n      }\n\n      this.notify({ type: 'queryRemoved', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueyKey extends QueryKey = QueryKey\n  >(\n    queryHash: string\n  ): Query<TQueryFnData, TError, TData, TQueyKey> | undefined {\n    return this.queriesMap[queryHash]\n  }\n\n  getAll(): Query[] {\n    return this.queries\n  }\n\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    arg1: QueryKey,\n    arg2?: QueryFilters\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const [filters] = parseFilterArgs(arg1, arg2)\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.queries.find(query => matchQuery(filters, query))\n  }\n\n  findAll(queryKey?: QueryKey, filters?: QueryFilters): Query[]\n  findAll(filters?: QueryFilters): Query[]\n  findAll(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): Query[]\n  findAll(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): Query[] {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    return Object.keys(filters).length > 0\n      ? this.queries.filter(query => matchQuery(filters, query))\n      : this.queries\n  }\n\n  notify(event: QueryCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(listener => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onOnline()\n      })\n    })\n  }\n}\n", "import type { MutationOptions, MutationStatus, MutationMeta } from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport { getLogger } from './logger'\nimport { notify<PERSON>anager } from './notifyManager'\nimport { <PERSON>try<PERSON> } from './retryer'\nimport { noop } from './utils'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n  meta?: MutationMeta\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n}\n\ninterface FailedAction {\n  type: 'failed'\n}\n\ninterface LoadingAction<TVariables, TContext> {\n  type: 'loading'\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError, TVariables, TContext> {\n  type: 'setState'\n  state: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction\n  | LoadingAction<TVariables, TContext>\n  | PauseAction\n  | SetStateAction<TData, TError, TVariables, TContext>\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n> {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  mutationId: number\n  meta: MutationMeta | undefined\n\n  private observers: MutationObserver<TData, TError, TVariables, TContext>[]\n  private mutationCache: MutationCache\n  private retryer?: Retryer<TData, TError>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    this.options = {\n      ...config.defaultOptions,\n      ...config.options,\n    }\n    this.mutationId = config.mutationId\n    this.mutationCache = config.mutationCache\n    this.observers = []\n    this.state = config.state || getDefaultState()\n    this.meta = config.meta\n  }\n\n  setState(state: MutationState<TData, TError, TVariables, TContext>): void {\n    this.dispatch({ type: 'setState', state })\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer)\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.observers = this.observers.filter(x => x !== observer)\n  }\n\n  cancel(): Promise<void> {\n    if (this.retryer) {\n      this.retryer.cancel()\n      return this.retryer.promise.then(noop).catch(noop)\n    }\n    return Promise.resolve()\n  }\n\n  continue(): Promise<TData> {\n    if (this.retryer) {\n      this.retryer.continue()\n      return this.retryer.promise\n    }\n    return this.execute()\n  }\n\n  execute(): Promise<TData> {\n    let data: TData\n\n    const restored = this.state.status === 'loading'\n\n    let promise = Promise.resolve()\n\n    if (!restored) {\n      this.dispatch({ type: 'loading', variables: this.options.variables! })\n      promise = promise\n        .then(() => {\n          // Notify cache callback\n          this.mutationCache.config.onMutate?.(\n            this.state.variables,\n            this as Mutation<unknown, unknown, unknown, unknown>\n          )\n        })\n        .then(() => this.options.onMutate?.(this.state.variables!))\n        .then(context => {\n          if (context !== this.state.context) {\n            this.dispatch({\n              type: 'loading',\n              context,\n              variables: this.state.variables,\n            })\n          }\n        })\n    }\n\n    return promise\n      .then(() => this.executeMutation())\n      .then(result => {\n        data = result\n        // Notify cache callback\n        this.mutationCache.config.onSuccess?.(\n          data,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>\n        )\n      })\n      .then(() =>\n        this.options.onSuccess?.(\n          data,\n          this.state.variables!,\n          this.state.context!\n        )\n      )\n      .then(() =>\n        this.options.onSettled?.(\n          data,\n          null,\n          this.state.variables!,\n          this.state.context\n        )\n      )\n      .then(() => {\n        this.dispatch({ type: 'success', data })\n        return data\n      })\n      .catch(error => {\n        // Notify cache callback\n        this.mutationCache.config.onError?.(\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>\n        )\n\n        // Log error\n        getLogger().error(error)\n\n        return Promise.resolve()\n          .then(() =>\n            this.options.onError?.(\n              error,\n              this.state.variables!,\n              this.state.context\n            )\n          )\n          .then(() =>\n            this.options.onSettled?.(\n              undefined,\n              error,\n              this.state.variables!,\n              this.state.context\n            )\n          )\n          .then(() => {\n            this.dispatch({ type: 'error', error })\n            throw error\n          })\n      })\n  }\n\n  private executeMutation(): Promise<TData> {\n    this.retryer = new Retryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject('No mutationFn found')\n        }\n        return this.options.mutationFn(this.state.variables!)\n      },\n      onFail: () => {\n        this.dispatch({ type: 'failed' })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n    })\n\n    return this.retryer.promise\n  }\n\n  private dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    this.state = reducer(this.state, action)\n\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onMutationUpdate(action)\n      })\n      this.mutationCache.notify(this)\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n  }\n}\n\nfunction reducer<TData, TError, TVariables, TContext>(\n  state: MutationState<TData, TError, TVariables, TContext>,\n  action: Action<TData, TError, TVariables, TContext>\n): MutationState<TData, TError, TVariables, TContext> {\n  switch (action.type) {\n    case 'failed':\n      return {\n        ...state,\n        failureCount: state.failureCount + 1,\n      }\n    case 'pause':\n      return {\n        ...state,\n        isPaused: true,\n      }\n    case 'continue':\n      return {\n        ...state,\n        isPaused: false,\n      }\n    case 'loading':\n      return {\n        ...state,\n        context: action.context,\n        data: undefined,\n        error: null,\n        isPaused: false,\n        status: 'loading',\n        variables: action.variables,\n      }\n    case 'success':\n      return {\n        ...state,\n        data: action.data,\n        error: null,\n        status: 'success',\n        isPaused: false,\n      }\n    case 'error':\n      return {\n        ...state,\n        data: undefined,\n        error: action.error,\n        failureCount: state.failureCount + 1,\n        isPaused: false,\n        status: 'error',\n      }\n    case 'setState':\n      return {\n        ...state,\n        ...action.state,\n      }\n    default:\n      return state\n  }\n}\n", "import type { MutationOptions } from './types'\nimport type { QueryClient } from './queryClient'\nimport { notifyManager } from './notifyManager'\nimport { Mutation, MutationState } from './mutation'\nimport { matchMutation, MutationFilters, noop } from './utils'\nimport { Subscribable } from './subscribable'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown, unknown>\n  ) => void\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown, unknown>\n  ) => void\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown, unknown>\n  ) => void\n}\n\ntype MutationCacheListener = (mutation?: Mutation) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  config: MutationCacheConfig\n\n  private mutations: Mutation<any, any, any, any>[]\n  private mutationId: number\n\n  constructor(config?: MutationCacheConfig) {\n    super()\n    this.config = config || {}\n    this.mutations = []\n    this.mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey\n        ? client.getMutationDefaults(options.mutationKey)\n        : undefined,\n      meta: options.meta,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.mutations.push(mutation)\n    this.notify(mutation)\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    this.mutations = this.mutations.filter(x => x !== mutation)\n    mutation.cancel()\n    this.notify(mutation)\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.mutations.forEach(mutation => {\n        this.remove(mutation)\n      })\n    })\n  }\n\n  getAll(): Mutation[] {\n    return this.mutations\n  }\n\n  find<TData = unknown, TError = unknown, TVariables = any, TContext = unknown>(\n    filters: MutationFilters\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.mutations.find(mutation => matchMutation(filters, mutation))\n  }\n\n  findAll(filters: MutationFilters): Mutation[] {\n    return this.mutations.filter(mutation => matchMutation(filters, mutation))\n  }\n\n  notify(mutation?: Mutation<any, any, any, any>) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(listener => {\n        listener(mutation)\n      })\n    })\n  }\n\n  onFocus(): void {\n    this.resumePausedMutations()\n  }\n\n  onOnline(): void {\n    this.resumePausedMutations()\n  }\n\n  resumePausedMutations(): Promise<void> {\n    const pausedMutations = this.mutations.filter(x => x.state.isPaused)\n    return notifyManager.batch(() =>\n      pausedMutations.reduce(\n        (promise, mutation) =>\n          promise.then(() => mutation.continue().catch(noop)),\n        Promise.resolve()\n      )\n    )\n  }\n}\n", "import type { QueryBehavior } from './query'\nimport { isCancelable } from './retryer'\nimport type {\n  InfiniteData,\n  QueryFunctionContext,\n  QueryOptions,\n  RefetchQueryFilters,\n} from './types'\nimport { getAbortController } from './utils'\n\nexport function infiniteQueryBehavior<\n  TQueryFnData,\n  TError,\n  TData\n>(): QueryBehavior<TQueryFnData, TError, InfiniteData<TData>> {\n  return {\n    onFetch: context => {\n      context.fetchFn = () => {\n        const refetchPage: RefetchQueryFilters['refetchPage'] | undefined =\n          context.fetchOptions?.meta?.refetchPage\n        const fetchMore = context.fetchOptions?.meta?.fetchMore\n        const pageParam = fetchMore?.pageParam\n        const isFetchingNextPage = fetchMore?.direction === 'forward'\n        const isFetchingPreviousPage = fetchMore?.direction === 'backward'\n        const oldPages = context.state.data?.pages || []\n        const oldPageParams = context.state.data?.pageParams || []\n        const abortController = getAbortController()\n        const abortSignal = abortController?.signal\n        let newPageParams = oldPageParams\n        let cancelled = false\n\n        // Get query function\n        const queryFn =\n          context.options.queryFn || (() => Promise.reject('Missing queryFn'))\n\n        const buildNewPages = (\n          pages: unknown[],\n          param: unknown,\n          page: unknown,\n          previous?: boolean\n        ) => {\n          newPageParams = previous\n            ? [param, ...newPageParams]\n            : [...newPageParams, param]\n          return previous ? [page, ...pages] : [...pages, page]\n        }\n\n        // Create function to fetch a page\n        const fetchPage = (\n          pages: unknown[],\n          manual?: boolean,\n          param?: unknown,\n          previous?: boolean\n        ): Promise<unknown[]> => {\n          if (cancelled) {\n            return Promise.reject('Cancelled')\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages)\n          }\n\n          const queryFnContext: QueryFunctionContext = {\n            queryKey: context.queryKey,\n            signal: abortSignal,\n            pageParam: param,\n            meta: context.meta,\n          }\n\n          const queryFnResult = queryFn(queryFnContext)\n\n          const promise = Promise.resolve(queryFnResult).then(page =>\n            buildNewPages(pages, param, page, previous)\n          )\n\n          if (isCancelable(queryFnResult)) {\n            const promiseAsAny = promise as any\n            promiseAsAny.cancel = queryFnResult.cancel\n          }\n\n          return promise\n        }\n\n        let promise: Promise<unknown[]>\n\n        // Fetch first page?\n        if (!oldPages.length) {\n          promise = fetchPage([])\n        }\n\n        // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getNextPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param)\n        }\n\n        // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getPreviousPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param, true)\n        }\n\n        // Refetch pages\n        else {\n          newPageParams = []\n\n          const manual = typeof context.options.getNextPageParam === 'undefined'\n\n          const shouldFetchFirstPage =\n            refetchPage && oldPages[0]\n              ? refetchPage(oldPages[0], 0, oldPages)\n              : true\n\n          // Fetch first page\n          promise = shouldFetchFirstPage\n            ? fetchPage([], manual, oldPageParams[0])\n            : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0]))\n\n          // Fetch remaining pages\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then(pages => {\n              const shouldFetchNextPage =\n                refetchPage && oldPages[i]\n                  ? refetchPage(oldPages[i], i, oldPages)\n                  : true\n\n              if (shouldFetchNextPage) {\n                const param = manual\n                  ? oldPageParams[i]\n                  : getNextPageParam(context.options, pages)\n                return fetchPage(pages, manual, param)\n              }\n              return Promise.resolve(\n                buildNewPages(pages, oldPageParams[i], oldPages[i])\n              )\n            })\n          }\n        }\n\n        const finalPromise = promise.then(pages => ({\n          pages,\n          pageParams: newPageParams,\n        }))\n\n        const finalPromiseAsAny = finalPromise as any\n\n        finalPromiseAsAny.cancel = () => {\n          cancelled = true\n          abortController?.abort()\n          if (isCancelable(promise)) {\n            promise.cancel()\n          }\n        }\n\n        return finalPromise\n      }\n    },\n  }\n}\n\nexport function getNextPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[]\n): unknown | undefined {\n  return options.getNextPageParam?.(pages[pages.length - 1], pages)\n}\n\nexport function getPreviousPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[]\n): unknown | undefined {\n  return options.getPreviousPageParam?.(pages[0], pages)\n}\n\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasNextPage(\n  options: QueryOptions<any, any>,\n  pages?: unknown\n): boolean | undefined {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages)\n    return (\n      typeof nextPageParam !== 'undefined' &&\n      nextPageParam !== null &&\n      nextPageParam !== false\n    )\n  }\n}\n\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasPreviousPage(\n  options: QueryOptions<any, any>,\n  pages?: unknown\n): boolean | undefined {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages)\n    return (\n      typeof previousPageParam !== 'undefined' &&\n      previousPageParam !== null &&\n      previousPageParam !== false\n    )\n  }\n}\n", "import {\n  QueryFilters,\n  Updater,\n  hashQueryKey,\n  noop,\n  parseFilterArgs,\n  parseQueryArgs,\n  partialMatchKey,\n  hashQueryKeyByOptions,\n  MutationFilters,\n} from './utils'\nimport type {\n  QueryClientConfig,\n  DefaultOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  ResetQueryFilters,\n  SetDataOptions,\n} from './types'\nimport type { QueryState } from './query'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport { CancelOptions } from './types'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: QueryOptions<any, any, any>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  private queryCache: QueryCache\n  private mutationCache: MutationCache\n  private defaultOptions: DefaultOptions\n  private queryDefaults: QueryDefaults[]\n  private mutationDefaults: MutationDefaults[]\n  private unsubscribeFocus?: () => void\n  private unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.queryCache = config.queryCache || new QueryCache()\n    this.mutationCache = config.mutationCache || new MutationCache()\n    this.defaultOptions = config.defaultOptions || {}\n    this.queryDefaults = []\n    this.mutationDefaults = []\n  }\n\n  mount(): void {\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        this.mutationCache.onFocus()\n        this.queryCache.onFocus()\n      }\n    })\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        this.mutationCache.onOnline()\n        this.queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.unsubscribeFocus?.()\n    this.unsubscribeOnline?.()\n  }\n\n  isFetching(filters?: QueryFilters): number\n  isFetching(queryKey?: QueryKey, filters?: QueryFilters): number\n  isFetching(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): number {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    filters.fetching = true\n    return this.queryCache.findAll(filters).length\n  }\n\n  isMutating(filters?: MutationFilters): number {\n    return this.mutationCache.findAll({ ...filters, fetching: true }).length\n  }\n\n  getQueryData<TData = unknown>(\n    queryKey: QueryKey,\n    filters?: QueryFilters\n  ): TData | undefined {\n    return this.queryCache.find<TData>(queryKey, filters)?.state.data\n  }\n\n  getQueriesData<TData = unknown>(queryKey: QueryKey): [QueryKey, TData][]\n  getQueriesData<TData = unknown>(filters: QueryFilters): [QueryKey, TData][]\n  getQueriesData<TData = unknown>(\n    queryKeyOrFilters: QueryKey | QueryFilters\n  ): [QueryKey, TData][] {\n    return this.getQueryCache()\n      .findAll(queryKeyOrFilters)\n      .map(({ queryKey, state }) => {\n        const data = state.data as TData\n        return [queryKey, data]\n      })\n  }\n\n  setQueryData<TData>(\n    queryKey: QueryKey,\n    updater: Updater<TData | undefined, TData>,\n    options?: SetDataOptions\n  ): TData {\n    const parsedOptions = parseQueryArgs(queryKey)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n    return this.queryCache\n      .build(this, defaultedOptions)\n      .setData(updater, options)\n  }\n\n  setQueriesData<TData>(\n    queryKey: QueryKey,\n    updater: Updater<TData | undefined, TData>,\n    options?: SetDataOptions\n  ): [QueryKey, TData][]\n\n  setQueriesData<TData>(\n    filters: QueryFilters,\n    updater: Updater<TData | undefined, TData>,\n    options?: SetDataOptions\n  ): [QueryKey, TData][]\n\n  setQueriesData<TData>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n    updater: Updater<TData | undefined, TData>,\n    options?: SetDataOptions\n  ): [QueryKey, TData][] {\n    return notifyManager.batch(() =>\n      this.getQueryCache()\n        .findAll(queryKeyOrFilters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TData>(queryKey, updater, options),\n        ])\n    )\n  }\n\n  getQueryState<TData = unknown, TError = undefined>(\n    queryKey: QueryKey,\n    filters?: QueryFilters\n  ): QueryState<TData, TError> | undefined {\n    return this.queryCache.find<TData, TError>(queryKey, filters)?.state\n  }\n\n  removeQueries(filters?: QueryFilters): void\n  removeQueries(queryKey?: QueryKey, filters?: QueryFilters): void\n  removeQueries(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): void {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    const queryCache = this.queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TPageData = unknown>(\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions\n  ): Promise<void>\n  resetQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions\n  ): Promise<void>\n  resetQueries(\n    arg1?: QueryKey | ResetQueryFilters,\n    arg2?: ResetQueryFilters | ResetOptions,\n    arg3?: ResetOptions\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n    const queryCache = this.queryCache\n\n    const refetchFilters: RefetchQueryFilters = {\n      ...filters,\n      active: true,\n    }\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        query.reset()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  cancelQueries(filters?: QueryFilters, options?: CancelOptions): Promise<void>\n  cancelQueries(\n    queryKey?: QueryKey,\n    filters?: QueryFilters,\n    options?: CancelOptions\n  ): Promise<void>\n  cancelQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: QueryFilters | CancelOptions,\n    arg3?: CancelOptions\n  ): Promise<void> {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3)\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true\n    }\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache.findAll(filters).map(query => query.cancel(cancelOptions))\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TPageData = unknown>(\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions\n  ): Promise<void>\n  invalidateQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions\n  ): Promise<void>\n  invalidateQueries(\n    arg1?: QueryKey | InvalidateQueryFilters,\n    arg2?: InvalidateQueryFilters | InvalidateOptions,\n    arg3?: InvalidateOptions\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    const refetchFilters: RefetchQueryFilters = {\n      ...filters,\n      // if filters.refetchActive is not provided and filters.active is explicitly false,\n      // e.g. invalidateQueries({ active: false }), we don't want to refetch active queries\n      active: filters.refetchActive ?? filters.active ?? true,\n      inactive: filters.refetchInactive ?? false,\n    }\n\n    return notifyManager.batch(() => {\n      this.queryCache.findAll(filters).forEach(query => {\n        query.invalidate()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  refetchQueries<TPageData = unknown>(\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions\n  ): Promise<void>\n  refetchQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions\n  ): Promise<void>\n  refetchQueries(\n    arg1?: QueryKey | RefetchQueryFilters,\n    arg2?: RefetchQueryFilters | RefetchOptions,\n    arg3?: RefetchOptions\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache.findAll(filters).map(query =>\n        query.fetch(undefined, {\n          ...options,\n          meta: { refetchPage: filters?.refetchPage },\n        })\n      )\n    )\n\n    let promise = Promise.all(promises).then(noop)\n\n    if (!options?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(defaultedOptions.staleTime)\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void> {\n    return this.fetchQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<InfiniteData<TData>> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    parsedOptions.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData\n    >()\n    return this.fetchQuery(parsedOptions)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  cancelMutations(): Promise<void> {\n    const promises = notifyManager.batch(() =>\n      this.mutationCache.getAll().map(mutation => mutation.cancel())\n    )\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  resumePausedMutations(): Promise<void> {\n    return this.getMutationCache().resumePausedMutations()\n  }\n\n  executeMutation<\n    TData = unknown,\n    TError = unknown,\n    TVariables = void,\n    TContext = unknown\n  >(\n    options: MutationOptions<TData, TError, TVariables, TContext>\n  ): Promise<TData> {\n    return this.mutationCache.build(this, options).execute()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.mutationCache\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.defaultOptions = options\n  }\n\n  setQueryDefaults(\n    queryKey: QueryKey,\n    options: QueryObserverOptions<any, any, any, any>\n  ): void {\n    const result = this.queryDefaults.find(\n      x => hashQueryKey(queryKey) === hashQueryKey(x.queryKey)\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.queryDefaults.push({ queryKey, defaultOptions: options })\n    }\n  }\n\n  getQueryDefaults(\n    queryKey?: QueryKey\n  ): QueryObserverOptions<any, any, any, any, any> | undefined {\n    return queryKey\n      ? this.queryDefaults.find(x => partialMatchKey(queryKey, x.queryKey))\n          ?.defaultOptions\n      : undefined\n  }\n\n  setMutationDefaults(\n    mutationKey: MutationKey,\n    options: MutationObserverOptions<any, any, any, any>\n  ): void {\n    const result = this.mutationDefaults.find(\n      x => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey)\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.mutationDefaults.push({ mutationKey, defaultOptions: options })\n    }\n  }\n\n  getMutationDefaults(\n    mutationKey?: MutationKey\n  ): MutationObserverOptions<any, any, any, any> | undefined {\n    return mutationKey\n      ? this.mutationDefaults.find(x =>\n          partialMatchKey(mutationKey, x.mutationKey)\n        )?.defaultOptions\n      : undefined\n  }\n\n  defaultQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey extends QueryKey\n  >(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey> {\n    if (options?._defaulted) {\n      return options\n    }\n\n    const defaultedOptions = {\n      ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options?.queryKey),\n      ...options,\n      _defaulted: true,\n    } as QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      )\n    }\n\n    return defaultedOptions\n  }\n\n  defaultQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey extends QueryKey\n  >(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey> {\n    return this.defaultQueryOptions(options)\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options?.mutationKey),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.queryCache.clear()\n    this.mutationCache.clear()\n  }\n}\n", "import { RefetchQueryFilters } from './types'\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceEqualDeep,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport type {\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n  ResultOptions,\n} from './types'\nimport type { Query, QueryState, Action, FetchOptions } from './query'\nimport type { QueryClient } from './queryClient'\nimport { focusManager } from './focusManager'\nimport { Subscribable } from './subscribable'\nimport { getLogger } from './logger'\nimport { isCancelledError } from './retryer'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>\n) => void\n\nexport interface NotifyOptions {\n  cache?: boolean\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\nexport interface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  options: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n\n  private client: QueryClient\n  private currentQuery!: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  private currentQueryInitialState!: QueryState<TQueryData, TError>\n  private currentResult!: QueryObserverResult<TData, TError>\n  private currentResultState?: QueryState<TQueryData, TError>\n  private currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  private previousQueryResult?: QueryObserverResult<TData, TError>\n  private selectError: Error | null\n  private selectFn?: (data: TQueryData) => TData\n  private selectResult?: TData\n  private staleTimeoutId?: number\n  private refetchIntervalId?: number\n  private currentRefetchInterval?: number | false\n  private trackedProps!: Array<keyof QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ) {\n    super()\n\n    this.client = client\n    this.options = options\n    this.trackedProps = []\n    this.selectError = null\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.remove = this.remove.bind(this)\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.length === 1) {\n      this.currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch()\n      }\n\n      this.updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.length) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnReconnect\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus\n    )\n  }\n\n  destroy(): void {\n    this.listeners = []\n    this.clearTimers()\n    this.currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.currentQuery\n\n    this.options = this.client.defaultQueryObserverOptions(options)\n\n    if (\n      typeof this.options.enabled !== 'undefined' &&\n      typeof this.options.enabled !== 'boolean'\n    ) {\n      throw new Error('Expected enabled to be a boolean')\n    }\n\n    // Keep previous query key if the user does not supply one\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey\n    }\n\n    this.updateQuery()\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions\n      )\n    ) {\n      this.executeFetch()\n    }\n\n    // Update result\n    this.updateResult(notifyOptions)\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        this.options.staleTime !== prevOptions.staleTime)\n    ) {\n      this.updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        nextRefetchInterval !== this.currentRefetchInterval)\n    ) {\n      this.updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): QueryObserverResult<TData, TError> {\n    const defaultedOptions = this.client.defaultQueryObserverOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(\n        this.client,\n        defaultedOptions as QueryOptions<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >\n      )\n\n    return this.createResult(query, defaultedOptions)\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n    defaultedOptions: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): QueryObserverResult<TData, TError> {\n    const trackedResult = {} as QueryObserverResult<TData, TError>\n\n    const trackProp = (key: keyof QueryObserverResult) => {\n      if (!this.trackedProps.includes(key)) {\n        this.trackedProps.push(key)\n      }\n    }\n\n    Object.keys(result).forEach(key => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          trackProp(key as keyof QueryObserverResult)\n          return result[key as keyof QueryObserverResult]\n        },\n      })\n    })\n\n    if (defaultedOptions.useErrorBoundary || defaultedOptions.suspense) {\n      trackProp('error')\n    }\n\n    return trackedResult\n  }\n\n  getNextResult(\n    options?: ResultOptions\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return new Promise((resolve, reject) => {\n      const unsubscribe = this.subscribe(result => {\n        if (!result.isFetching) {\n          unsubscribe()\n          if (result.isError && options?.throwOnError) {\n            reject(result.error)\n          } else {\n            resolve(result)\n          }\n        }\n      })\n    })\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.currentQuery\n  }\n\n  remove(): void {\n    this.client.getQueryCache().remove(this.currentQuery)\n  }\n\n  refetch<TPageData>(\n    options?: RefetchOptions & RefetchQueryFilters<TPageData>\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.fetch({\n      ...options,\n      meta: { refetchPage: options?.refetchPage },\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.client.defaultQueryObserverOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(\n        this.client,\n        defaultedOptions as QueryOptions<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >\n      )\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions?: ObserverFetchOptions\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.executeFetch(fetchOptions).then(() => {\n      this.updateResult()\n      return this.currentResult\n    })\n  }\n\n  private executeFetch(\n    fetchOptions?: ObserverFetchOptions\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  private updateStaleTimeout(): void {\n    this.clearStaleTimeout()\n\n    if (\n      isServer ||\n      this.currentResult.isStale ||\n      !isValidTimeout(this.options.staleTime)\n    ) {\n      return\n    }\n\n    const time = timeUntilStale(\n      this.currentResult.dataUpdatedAt,\n      this.options.staleTime\n    )\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  private computeRefetchInterval() {\n    return typeof this.options.refetchInterval === 'function'\n      ? this.options.refetchInterval(this.currentResult.data, this.currentQuery)\n      : this.options.refetchInterval ?? false\n  }\n\n  private updateRefetchInterval(nextInterval: number | false): void {\n    this.clearRefetchInterval()\n\n    this.currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      this.options.enabled === false ||\n      !isValidTimeout(this.currentRefetchInterval) ||\n      this.currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.executeFetch()\n      }\n    }, this.currentRefetchInterval)\n  }\n\n  private updateTimers(): void {\n    this.updateStaleTimeout()\n    this.updateRefetchInterval(this.computeRefetchInterval())\n  }\n\n  private clearTimers(): void {\n    this.clearStaleTimeout()\n    this.clearRefetchInterval()\n  }\n\n  private clearStaleTimeout(): void {\n    clearTimeout(this.staleTimeoutId)\n    this.staleTimeoutId = undefined\n  }\n\n  private clearRefetchInterval(): void {\n    clearInterval(this.refetchIntervalId)\n    this.refetchIntervalId = undefined\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.currentQuery\n    const prevOptions = this.options\n    const prevResult = this.currentResult\n    const prevResultState = this.currentResultState\n    const prevResultOptions = this.currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.currentQueryInitialState\n    const prevQueryResult = queryChange\n      ? this.currentResult\n      : this.previousQueryResult\n\n    const { state } = query\n    let { dataUpdatedAt, error, errorUpdatedAt, isFetching, status } = state\n    let isPreviousData = false\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options.optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        isFetching = true\n        if (!dataUpdatedAt) {\n          status = 'loading'\n        }\n      }\n    }\n\n    // Keep previous data if needed\n    if (\n      options.keepPreviousData &&\n      !state.dataUpdateCount &&\n      prevQueryResult?.isSuccess &&\n      status !== 'error'\n    ) {\n      data = prevQueryResult.data\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt\n      status = prevQueryResult.status\n      isPreviousData = true\n    }\n    // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (\n        prevResult &&\n        state.data === prevResultState?.data &&\n        options.select === this.selectFn\n      ) {\n        data = this.selectResult\n      } else {\n        try {\n          this.selectFn = options.select\n          data = options.select(state.data)\n          if (options.structuralSharing !== false) {\n            data = replaceEqualDeep(prevResult?.data, data)\n          }\n          this.selectResult = data\n          this.selectError = null\n        } catch (selectError) {\n          getLogger().error(selectError)\n          this.selectError = selectError\n        }\n      }\n    }\n    // Use query data\n    else {\n      data = (state.data as unknown) as TData\n    }\n\n    // Show placeholder data if needed\n    if (\n      typeof options.placeholderData !== 'undefined' &&\n      typeof data === 'undefined' &&\n      (status === 'loading' || status === 'idle')\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n      } else {\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (options.placeholderData as PlaceholderDataFunction<TQueryData>)()\n            : options.placeholderData\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData)\n            if (options.structuralSharing !== false) {\n              placeholderData = replaceEqualDeep(\n                prevResult?.data,\n                placeholderData\n              )\n            }\n            this.selectError = null\n          } catch (selectError) {\n            getLogger().error(selectError)\n            this.selectError = selectError\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success'\n        data = placeholderData as TData\n        isPlaceholderData = true\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError as any\n      data = this.selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      isLoading: status === 'loading',\n      isSuccess: status === 'success',\n      isError: status === 'error',\n      isIdle: status === 'idle',\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        state.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && status !== 'loading',\n      isLoadingError: status === 'error' && state.dataUpdatedAt === 0,\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: status === 'error' && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove,\n    }\n\n    return result as QueryObserverResult<TData, TError>\n  }\n\n  private shouldNotifyListeners(\n    result: QueryObserverResult,\n    prevResult?: QueryObserverResult\n  ): boolean {\n    if (!prevResult) {\n      return true\n    }\n\n    const { notifyOnChangeProps, notifyOnChangePropsExclusions } = this.options\n\n    if (!notifyOnChangeProps && !notifyOnChangePropsExclusions) {\n      return true\n    }\n\n    if (notifyOnChangeProps === 'tracked' && !this.trackedProps.length) {\n      return true\n    }\n\n    const includedProps =\n      notifyOnChangeProps === 'tracked'\n        ? this.trackedProps\n        : notifyOnChangeProps\n\n    return Object.keys(result).some(key => {\n      const typedKey = key as keyof QueryObserverResult\n      const changed = result[typedKey] !== prevResult[typedKey]\n      const isIncluded = includedProps?.some(x => x === key)\n      const isExcluded = notifyOnChangePropsExclusions?.some(x => x === key)\n      return changed && !isExcluded && (!includedProps || isIncluded)\n    })\n  }\n\n  updateResult(notifyOptions?: NotifyOptions): void {\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    this.currentResult = this.createResult(this.currentQuery, this.options)\n    this.currentResultState = this.currentQuery.state\n    this.currentResultOptions = this.options\n\n    // Only notify if something has changed\n    if (shallowEqualObjects(this.currentResult, prevResult)) {\n      return\n    }\n\n    // Determine which callbacks to trigger\n    const defaultNotifyOptions: NotifyOptions = { cache: true }\n\n    if (\n      notifyOptions?.listeners !== false &&\n      this.shouldNotifyListeners(this.currentResult, prevResult)\n    ) {\n      defaultNotifyOptions.listeners = true\n    }\n\n    this.notify({ ...defaultNotifyOptions, ...notifyOptions })\n  }\n\n  private updateQuery(): void {\n    const query = this.client\n      .getQueryCache()\n      .build(\n        this.client,\n        this.options as QueryOptions<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >\n      )\n\n    if (query === this.currentQuery) {\n      return\n    }\n\n    const prevQuery = this.currentQuery\n    this.currentQuery = query\n    this.currentQueryInitialState = query.state\n    this.previousQueryResult = this.currentResult\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(action: Action<TData, TError>): void {\n    const notifyOptions: NotifyOptions = {}\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true\n    }\n\n    this.updateResult(notifyOptions)\n\n    if (this.hasListeners()) {\n      this.updateTimers()\n    }\n  }\n\n  private notify(notifyOptions: NotifyOptions): void {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        this.options.onSuccess?.(this.currentResult.data!)\n        this.options.onSettled?.(this.currentResult.data!, null)\n      } else if (notifyOptions.onError) {\n        this.options.onError?.(this.currentResult.error!)\n        this.options.onSettled?.(undefined, this.currentResult.error!)\n      }\n\n      // Then trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(listener => {\n          listener(this.currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      if (notifyOptions.cache) {\n        this.client\n          .getQueryCache()\n          .notify({ query: this.currentQuery, type: 'observerResultsUpdated' })\n      }\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>\n): boolean {\n  return (\n    options.enabled !== false &&\n    !query.state.dataUpdatedAt &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.dataUpdatedAt > 0 &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: typeof options['refetchOnMount'] &\n    typeof options['refetchOnWindowFocus'] &\n    typeof options['refetchOnReconnect']\n) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>\n): boolean {\n  return (\n    options.enabled !== false &&\n    (query !== prevQuery || prevOptions.enabled === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>\n): boolean {\n  return query.isStaleByTime(options.staleTime)\n}\n", "import { difference, replaceAt } from './utils'\nimport { notifyManager } from './notifyManager'\nimport type { QueryObserverOptions, QueryObserverResult } from './types'\nimport type { QueryClient } from './queryClient'\nimport { NotifyOptions, QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\n\ntype QueriesObserverListener = (result: QueryObserverResult[]) => void\n\nexport class QueriesObserver extends Subscribable<QueriesObserverListener> {\n  private client: QueryClient\n  private result: QueryObserverResult[]\n  private queries: QueryObserverOptions[]\n  private observers: QueryObserver[]\n  private observersMap: Record<string, QueryObserver>\n\n  constructor(client: QueryClient, queries?: QueryObserverOptions[]) {\n    super()\n\n    this.client = client\n    this.queries = []\n    this.result = []\n    this.observers = []\n    this.observersMap = {}\n\n    if (queries) {\n      this.setQueries(queries)\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.length === 1) {\n      this.observers.forEach(observer => {\n        observer.subscribe(result => {\n          this.onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.length) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = []\n    this.observers.forEach(observer => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: QueryObserverOptions[],\n    notifyOptions?: NotifyOptions\n  ): void {\n    this.queries = queries\n    this.updateObservers(notifyOptions)\n  }\n\n  getCurrentResult(): QueryObserverResult[] {\n    return this.result\n  }\n\n  getOptimisticResult(queries: QueryObserverOptions[]): QueryObserverResult[] {\n    return this.findMatchingObservers(queries).map(match =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions)\n    )\n  }\n\n  private findMatchingObservers(\n    queries: QueryObserverOptions[]\n  ): QueryObserverMatch[] {\n    const prevObservers = this.observers\n    const defaultedQueryOptions = queries.map(options =>\n      this.client.defaultQueryObserverOptions(options)\n    )\n\n    const matchingObservers: QueryObserverMatch[] = defaultedQueryOptions.flatMap(\n      defaultedOptions => {\n        const match = prevObservers.find(\n          observer => observer.options.queryHash === defaultedOptions.queryHash\n        )\n        if (match != null) {\n          return [{ defaultedQueryOptions: defaultedOptions, observer: match }]\n        }\n        return []\n      }\n    )\n\n    const matchedQueryHashes = matchingObservers.map(\n      match => match.defaultedQueryOptions.queryHash\n    )\n    const unmatchedQueries = defaultedQueryOptions.filter(\n      defaultedOptions =>\n        !matchedQueryHashes.includes(defaultedOptions.queryHash)\n    )\n\n    const unmatchedObservers = prevObservers.filter(\n      prevObserver =>\n        !matchingObservers.some(match => match.observer === prevObserver)\n    )\n\n    const newOrReusedObservers: QueryObserverMatch[] = unmatchedQueries.map(\n      (options, index) => {\n        if (options.keepPreviousData) {\n          // return previous data from one of the observers that no longer match\n          const previouslyUsedObserver = unmatchedObservers[index]\n          if (previouslyUsedObserver !== undefined) {\n            return {\n              defaultedQueryOptions: options,\n              observer: previouslyUsedObserver,\n            }\n          }\n        }\n        return {\n          defaultedQueryOptions: options,\n          observer: this.getObserver(options),\n        }\n      }\n    )\n\n    const sortMatchesByOrderOfQueries = (\n      a: QueryObserverMatch,\n      b: QueryObserverMatch\n    ): number =>\n      defaultedQueryOptions.indexOf(a.defaultedQueryOptions) -\n      defaultedQueryOptions.indexOf(b.defaultedQueryOptions)\n\n    return matchingObservers\n      .concat(newOrReusedObservers)\n      .sort(sortMatchesByOrderOfQueries)\n  }\n\n  private getObserver(options: QueryObserverOptions): QueryObserver {\n    const defaultedOptions = this.client.defaultQueryObserverOptions(options)\n    const currentObserver = this.observersMap[defaultedOptions.queryHash!]\n    return currentObserver ?? new QueryObserver(this.client, defaultedOptions)\n  }\n\n  private updateObservers(notifyOptions?: NotifyOptions): void {\n    notifyManager.batch(() => {\n      const prevObservers = this.observers\n\n      const newObserverMatches = this.findMatchingObservers(this.queries)\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach(match =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions)\n      )\n\n      const newObservers = newObserverMatches.map(match => match.observer)\n      const newObserversMap = Object.fromEntries(\n        newObservers.map(observer => [observer.options.queryHash, observer])\n      )\n      const newResult = newObservers.map(observer =>\n        observer.getCurrentResult()\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index]\n      )\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.observers = newObservers\n      this.observersMap = newObserversMap\n      this.result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach(observer => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach(observer => {\n        observer.subscribe(result => {\n          this.onUpdate(observer, result)\n        })\n      })\n\n      this.notify()\n    })\n  }\n\n  private onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.observers.indexOf(observer)\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result)\n      this.notify()\n    }\n  }\n\n  private notify(): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach(listener => {\n        listener(this.result)\n      })\n    })\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: QueryObserverOptions\n  observer: QueryObserver\n}\n", "import type {\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport {\n  NotifyOptions,\n  ObserverFetchOptions,\n  QueryObserver,\n} from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  InfiniteData<TData>,\n  InfiniteData<TQueryData>\n> {\n  // Type override\n  subscribe!: (\n    listener?: InfiniteQueryObserverListener<TData, TError>\n  ) => () => void\n\n  // Type override\n  getCurrentResult!: () => InfiniteQueryObserverResult<TData, TError>\n\n  // Type override\n  protected fetch!: (\n    fetchOptions?: ObserverFetchOptions\n  ) => Promise<InfiniteQueryObserverResult<TData, TError>>\n\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData\n    >\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options?: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData\n    >,\n    notifyOptions?: NotifyOptions\n  ): void {\n    super.setOptions(\n      {\n        ...options,\n        behavior: infiniteQueryBehavior(),\n      },\n      notifyOptions\n    )\n  }\n\n  getOptimisticResult(\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData\n    >\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage(\n    options?: FetchNextPageOptions\n  ): Promise<InfiniteQueryObserverResult<TData, TError>> {\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: options?.cancelRefetch ?? true,\n      throwOnError: options?.throwOnError,\n      meta: {\n        fetchMore: { direction: 'forward', pageParam: options?.pageParam },\n      },\n    })\n  }\n\n  fetchPreviousPage(\n    options?: FetchPreviousPageOptions\n  ): Promise<InfiniteQueryObserverResult<TData, TError>> {\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: options?.cancelRefetch ?? true,\n      throwOnError: options?.throwOnError,\n      meta: {\n        fetchMore: { direction: 'backward', pageParam: options?.pageParam },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, InfiniteData<TQueryData>>,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData\n    >\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const result = super.createResult(query, options)\n    return {\n      ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data?.pages),\n      hasPreviousPage: hasPreviousPage(options, state.data?.pages),\n      isFetchingNextPage:\n        state.isFetching && state.fetchMeta?.fetchMore?.direction === 'forward',\n      isFetchingPreviousPage:\n        state.isFetching &&\n        state.fetchMeta?.fetchMore?.direction === 'backward',\n    }\n  }\n}\n", "import { Action, getDefaultState, Mutation } from './mutation'\nimport { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport type { QueryClient } from './queryClient'\nimport { Subscribable } from './subscribable'\nimport type {\n  MutateOptions,\n  MutationObserverBaseResult,\n  MutationObserverResult,\n  MutationObserverOptions,\n} from './types'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>\n) => void\n\ninterface NotifyOptions {\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TE<PERSON>r, TVariables, TContext>\n\n  private client: QueryClient\n  private currentResult!: MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >\n  private currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  private mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>\n  ) {\n    super()\n\n    this.client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options?: MutationObserverOptions<TData, TError, TVariables, TContext>\n  ) {\n    this.options = this.client.defaultMutationOptions(options)\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.length) {\n      this.currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.updateResult()\n\n    // Determine which callbacks to trigger\n    const notifyOptions: NotifyOptions = {\n      listeners: true,\n    }\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true\n    }\n\n    this.notify(notifyOptions)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.currentResult\n  }\n\n  reset(): void {\n    this.currentMutation = undefined\n    this.updateResult()\n    this.notify({ listeners: true })\n  }\n\n  mutate(\n    variables?: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>\n  ): Promise<TData> {\n    this.mutateOptions = options\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this)\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, {\n      ...this.options,\n      variables:\n        typeof variables !== 'undefined' ? variables : this.options.variables,\n    })\n\n    this.currentMutation.addObserver(this)\n\n    return this.currentMutation.execute()\n  }\n\n  private updateResult(): void {\n    const state = this.currentMutation\n      ? this.currentMutation.state\n      : getDefaultState<TData, TError, TVariables, TContext>()\n\n    const result: MutationObserverBaseResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    > = {\n      ...state,\n      isLoading: state.status === 'loading',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    }\n\n    this.currentResult = result as MutationObserverResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    >\n  }\n\n  private notify(options: NotifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions) {\n        if (options.onSuccess) {\n          this.mutateOptions.onSuccess?.(\n            this.currentResult.data!,\n            this.currentResult.variables!,\n            this.currentResult.context!\n          )\n          this.mutateOptions.onSettled?.(\n            this.currentResult.data!,\n            null,\n            this.currentResult.variables!,\n            this.currentResult.context\n          )\n        } else if (options.onError) {\n          this.mutateOptions.onError?.(\n            this.currentResult.error!,\n            this.currentResult.variables!,\n            this.currentResult.context\n          )\n          this.mutateOptions.onSettled?.(\n            undefined,\n            this.currentResult.error,\n            this.currentResult.variables!,\n            this.currentResult.context\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      if (options.listeners) {\n        this.listeners.forEach(listener => {\n          listener(this.currentResult)\n        })\n      }\n    })\n  }\n}\n", "import type { QueryClient } from './queryClient'\nimport type { Query, QueryState } from './query'\nimport type {\n  MutationKey,\n  MutationOptions,\n  QueryKey,\n  QueryOptions,\n} from './types'\nimport type { Mutation, MutationState } from './mutation'\n\n// TYPES\n\nexport interface DehydrateOptions {\n  dehydrateMutations?: boolean\n  dehydrateQueries?: boolean\n  shouldDehydrateMutation?: ShouldDehydrateMutationFunction\n  shouldDehydrateQuery?: ShouldDehydrateQueryFunction\n}\n\nexport interface HydrateOptions {\n  defaultOptions?: {\n    queries?: QueryOptions\n    mutations?: MutationOptions\n  }\n}\n\ninterface DehydratedMutation {\n  mutationKey?: MutationKey\n  state: MutationState\n}\n\ninterface DehydratedQuery {\n  queryHash: string\n  queryKey: QueryKey\n  state: QueryState\n}\n\nexport interface DehydratedState {\n  mutations: DehydratedMutation[]\n  queries: DehydratedQuery[]\n}\n\nexport type ShouldDehydrateQueryFunction = (query: Query) => boolean\n\nexport type ShouldDehydrateMutationFunction = (mutation: Mutation) => boolean\n\n// FUNCTIONS\n\nfunction dehydrateMutation(mutation: Mutation): DehydratedMutation {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state,\n  }\n}\n\n// Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\nfunction dehydrateQuery(query: Query): DehydratedQuery {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash,\n  }\n}\n\nfunction defaultShouldDehydrateMutation(mutation: Mutation) {\n  return mutation.state.isPaused\n}\n\nfunction defaultShouldDehydrateQuery(query: Query) {\n  return query.state.status === 'success'\n}\n\nexport function dehydrate(\n  client: QueryClient,\n  options?: DehydrateOptions\n): DehydratedState {\n  options = options || {}\n\n  const mutations: DehydratedMutation[] = []\n  const queries: DehydratedQuery[] = []\n\n  if (options?.dehydrateMutations !== false) {\n    const shouldDehydrateMutation =\n      options.shouldDehydrateMutation || defaultShouldDehydrateMutation\n\n    client\n      .getMutationCache()\n      .getAll()\n      .forEach(mutation => {\n        if (shouldDehydrateMutation(mutation)) {\n          mutations.push(dehydrateMutation(mutation))\n        }\n      })\n  }\n\n  if (options?.dehydrateQueries !== false) {\n    const shouldDehydrateQuery =\n      options.shouldDehydrateQuery || defaultShouldDehydrateQuery\n\n    client\n      .getQueryCache()\n      .getAll()\n      .forEach(query => {\n        if (shouldDehydrateQuery(query)) {\n          queries.push(dehydrateQuery(query))\n        }\n      })\n  }\n\n  return { mutations, queries }\n}\n\nexport function hydrate(\n  client: QueryClient,\n  dehydratedState: unknown,\n  options?: HydrateOptions\n): void {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return\n  }\n\n  const mutationCache = client.getMutationCache()\n  const queryCache = client.getQueryCache()\n\n  const mutations = (dehydratedState as DehydratedState).mutations || []\n  const queries = (dehydratedState as DehydratedState).queries || []\n\n  mutations.forEach(dehydratedMutation => {\n    mutationCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.mutations,\n        mutationKey: dehydratedMutation.mutationKey,\n      },\n      dehydratedMutation.state\n    )\n  })\n\n  queries.forEach(dehydratedQuery => {\n    const query = queryCache.get(dehydratedQuery.queryHash)\n\n    // Do not hydrate if an existing query exists with newer data\n    if (query) {\n      if (query.state.dataUpdatedAt < dehydratedQuery.state.dataUpdatedAt) {\n        query.setState(dehydratedQuery.state)\n      }\n      return\n    }\n\n    // Restore query\n    queryCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.queries,\n        queryKey: dehydratedQuery.queryKey,\n        queryHash: dehydratedQuery.queryHash,\n      },\n      dehydratedQuery.state\n    )\n  })\n}\n", "import React from 'react'\n\nimport { QueryClient } from '../core'\n\ndeclare global {\n  interface Window {\n    ReactQueryClientContext?: React.Context<QueryClient | undefined>\n  }\n}\n\nconst defaultContext = React.createContext<QueryClient | undefined>(undefined)\nconst QueryClientSharingContext = React.createContext<boolean>(false)\n\n// if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\nfunction getQueryClientContext(contextSharing: boolean) {\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext\n    }\n\n    return window.ReactQueryClientContext\n  }\n\n  return defaultContext\n}\n\nexport const useQueryClient = () => {\n  const queryClient = React.useContext(\n    getQueryClientContext(React.useContext(QueryClientSharingContext))\n  )\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return queryClient\n}\n\nexport interface QueryClientProviderProps {\n  client: QueryClient\n  contextSharing?: boolean\n  children?: React.ReactNode\n}\n\nexport const QueryClientProvider = ({\n  client,\n  contextSharing = false,\n  children,\n}: QueryClientProviderProps): JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  const Context = getQueryClientContext(contextSharing)\n\n  return (\n    <QueryClientSharingContext.Provider value={contextSharing}>\n      <Context.Provider value={client}>{children}</Context.Provider>\n    </QueryClientSharingContext.Provider>\n  )\n}\n", "import React from 'react'\n\n// CONTEXT\n\ninterface QueryErrorResetBoundaryValue {\n  clearReset: () => void\n  isReset: () => boolean\n  reset: () => void\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport interface QueryErrorResetBoundaryProps {\n  children:\n    | ((value: QueryErrorResetBoundaryValue) => React.ReactNode)\n    | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const value = React.useMemo(() => createValue(), [])\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function'\n        ? (children as Function)(value)\n        : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n", "import React from 'react'\n\nimport { notifyManager } from '../core/notifyManager'\nimport { QueryKey } from '../core/types'\nimport { parseFilterArgs, QueryFilters } from '../core/utils'\nimport { QueryClient } from '../core'\nimport { useQueryClient } from './QueryClientProvider'\n\nconst checkIsFetching = (\n  queryClient: QueryClient,\n  filters: QueryFilters,\n  isFetching: number,\n  setIsFetching: React.Dispatch<React.SetStateAction<number>>\n) => {\n  const newIsFetching = queryClient.isFetching(filters)\n  if (isFetching !== newIsFetching) {\n    setIsFetching(newIsFetching)\n  }\n}\n\nexport function useIsFetching(filters?: QueryFilters): number\nexport function useIsFetching(\n  queryKey?: QueryKey,\n  filters?: QueryFilters\n): number\nexport function useIsFetching(\n  arg1?: QueryKey | QueryFilters,\n  arg2?: QueryFilters\n): number {\n  const mountedRef = React.useRef(false)\n\n  const queryClient = useQueryClient()\n\n  const [filters] = parseFilterArgs(arg1, arg2)\n  const [isFetching, setIsFetching] = React.useState(\n    queryClient.isFetching(filters)\n  )\n\n  const filtersRef = React.useRef(filters)\n  filtersRef.current = filters\n  const isFetchingRef = React.useRef(isFetching)\n  isFetchingRef.current = isFetching\n\n  React.useEffect(() => {\n    mountedRef.current = true\n\n    checkIsFetching(\n      queryClient,\n      filtersRef.current,\n      isFetchingRef.current,\n      setIsFetching\n    )\n\n    const unsubscribe = queryClient.getQueryCache().subscribe(\n      notifyManager.batchCalls(() => {\n        if (mountedRef.current) {\n          checkIsFetching(\n            queryClient,\n            filtersRef.current,\n            isFetchingRef.current,\n            setIsFetching\n          )\n        }\n      })\n    )\n\n    return () => {\n      mountedRef.current = false\n      unsubscribe()\n    }\n  }, [queryClient])\n\n  return isFetching\n}\n", "import React from 'react'\n\nimport { notifyManager } from '../core/notifyManager'\nimport { Mutation<PERSON>ey } from '../core/types'\nimport { MutationFilters, parseMutationFilterArgs } from '../core/utils'\nimport { useQueryClient } from './QueryClientProvider'\n\nexport function useIsMutating(filters?: MutationFilters): number\nexport function useIsMutating(\n  mutationKey?: MutationKey,\n  filters?: Omit<MutationFilters, 'mutationKey'>\n): number\nexport function useIsMutating(\n  arg1?: MutationKey | MutationFilters,\n  arg2?: Omit<MutationFilters, 'mutationKey'>\n): number {\n  const mountedRef = React.useRef(false)\n  const filters = parseMutationFilterArgs(arg1, arg2)\n\n  const queryClient = useQueryClient()\n\n  const [isMutating, setIsMutating] = React.useState(\n    queryClient.isMutating(filters)\n  )\n\n  const filtersRef = React.useRef(filters)\n  filtersRef.current = filters\n  const isMutatingRef = React.useRef(isMutating)\n  isMutatingRef.current = isMutating\n\n  React.useEffect(() => {\n    mountedRef.current = true\n\n    const unsubscribe = queryClient.getMutationCache().subscribe(\n      notifyManager.batchCalls(() => {\n        if (mountedRef.current) {\n          const newIsMutating = queryClient.isMutating(filtersRef.current)\n          if (isMutatingRef.current !== newIsMutating) {\n            setIsMutating(newIsMutating)\n          }\n        }\n      })\n    )\n\n    return () => {\n      mountedRef.current = false\n      unsubscribe()\n    }\n  }, [queryClient])\n\n  return isMutating\n}\n", "export function shouldThrowError<T extends (...args: any[]) => boolean>(\n  suspense: boolean | undefined,\n  _useErrorBoundary: boolean | T | undefined,\n  params: Parameters<T>\n): boolean {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params)\n  }\n\n  // Allow useErrorBoundary to override suspense's throwing behavior\n  if (typeof _useErrorBoundary === 'boolean') return _useErrorBoundary\n\n  // If suspense is enabled default to throwing errors\n  return !!suspense\n}\n", "import React from 'react'\n\nimport { notifyManager } from '../core/notifyManager'\nimport { noop, parseMutationArgs } from '../core/utils'\nimport { MutationObserver } from '../core/mutationObserver'\nimport { useQueryClient } from './QueryClientProvider'\nimport {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\nimport { MutationFunction, MutationKey } from '../core/types'\nimport { shouldThrowError } from './utils'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n>(\n  mutationFn: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationFn'\n  >\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n>(\n  mutationKey: MutationKey,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey'\n  >\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n>(\n  mutationKey: MutationKey,\n  mutationFn?: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey' | 'mutationFn'\n  >\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown\n>(\n  arg1:\n    | MutationKey\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg2?:\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg3?: UseMutationOptions<TData, TError, TVariables, TContext>\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const mountedRef = React.useRef(false)\n  const [, forceUpdate] = React.useState(0)\n\n  const options = parseMutationArgs(arg1, arg2, arg3)\n  const queryClient = useQueryClient()\n\n  const obsRef = React.useRef<\n    MutationObserver<TData, TError, TVariables, TContext>\n  >()\n\n  if (!obsRef.current) {\n    obsRef.current = new MutationObserver(queryClient, options)\n  } else {\n    obsRef.current.setOptions(options)\n  }\n\n  const currentResult = obsRef.current.getCurrentResult()\n\n  React.useEffect(() => {\n    mountedRef.current = true\n\n    const unsubscribe = obsRef.current!.subscribe(\n      notifyManager.batchCalls(() => {\n        if (mountedRef.current) {\n          forceUpdate(x => x + 1)\n        }\n      })\n    )\n    return () => {\n      mountedRef.current = false\n      unsubscribe()\n    }\n  }, [])\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >((variables, mutateOptions) => {\n    obsRef.current!.mutate(variables, mutateOptions).catch(noop)\n  }, [])\n\n  if (\n    currentResult.error &&\n    shouldThrowError(undefined, obsRef.current.options.useErrorBoundary, [\n      currentResult.error,\n    ])\n  ) {\n    throw currentResult.error\n  }\n\n  return { ...currentResult, mutate, mutateAsync: currentResult.mutate }\n}\n", "import React from 'react'\n\nimport { QueryKey } from '../core'\nimport { notifyManager } from '../core/notifyManager'\nimport { QueryObserver } from '../core/queryObserver'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport { useQueryClient } from './QueryClientProvider'\nimport { UseBaseQueryOptions } from './types'\nimport { shouldThrowError } from './utils'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  Observer: typeof QueryObserver\n) {\n  const mountedRef = React.useRef(false)\n  const [, forceUpdate] = React.useState(0)\n\n  const queryClient = useQueryClient()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const defaultedOptions = queryClient.defaultQueryObserverOptions(options)\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions.optimisticResults = true\n\n  // Include callbacks in batch renders\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(\n      defaultedOptions.onError\n    )\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(\n      defaultedOptions.onSuccess\n    )\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(\n      defaultedOptions.onSettled\n    )\n  }\n\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000\n    }\n\n    // Set cache time to 1 if the option has been set to 0\n    // when using suspense to prevent infinite loop of fetches\n    if (defaultedOptions.cacheTime === 0) {\n      defaultedOptions.cacheTime = 1\n    }\n  }\n\n  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      defaultedOptions.retryOnMount = false\n    }\n  }\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        queryClient,\n        defaultedOptions\n      )\n  )\n\n  let result = observer.getOptimisticResult(defaultedOptions)\n\n  React.useEffect(() => {\n    mountedRef.current = true\n\n    errorResetBoundary.clearReset()\n\n    const unsubscribe = observer.subscribe(\n      notifyManager.batchCalls(() => {\n        if (mountedRef.current) {\n          forceUpdate(x => x + 1)\n        }\n      })\n    )\n\n    // Update result to make sure we did not miss any query updates\n    // between creating the observer and subscribing to it.\n    observer.updateResult()\n\n    return () => {\n      mountedRef.current = false\n      unsubscribe()\n    }\n  }, [errorResetBoundary, observer])\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, { listeners: false })\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (defaultedOptions.suspense && result.isLoading) {\n    throw observer\n      .fetchOptimistic(defaultedOptions)\n      .then(({ data }) => {\n        defaultedOptions.onSuccess?.(data as TData)\n        defaultedOptions.onSettled?.(data, null)\n      })\n      .catch(error => {\n        errorResetBoundary.clearReset()\n        defaultedOptions.onError?.(error)\n        defaultedOptions.onSettled?.(undefined, error)\n      })\n  }\n\n  // Handle error boundary\n  if (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    shouldThrowError(\n      defaultedOptions.suspense,\n      defaultedOptions.useErrorBoundary,\n      [result.error, observer.getCurrentQuery()]\n    )\n  ) {\n    throw result.error\n  }\n\n  // Handle result property usage tracking\n  if (defaultedOptions.notifyOnChangeProps === 'tracked') {\n    result = observer.trackResult(result, defaultedOptions)\n  }\n\n  return result\n}\n", "import { QueryObserver } from '../core'\nimport { QueryFunction, QueryKey } from '../core/types'\nimport { parseQueryArgs } from '../core/utils'\nimport { UseQueryOptions, UseQueryResult } from './types'\nimport { useBaseQuery } from './useBaseQuery'\n\n// HOOK\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey'\n  >\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn'\n  >\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  arg1: TQueryKey | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg3?: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n): UseQueryResult<TData, TError> {\n  const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(parsedOptions, QueryObserver)\n}\n", "import React, { useMemo } from 'react'\nimport { QueryFunction } from '../core/types'\n\nimport { notifyManager } from '../core/notifyManager'\nimport { QueriesObserver } from '../core/queriesObserver'\nimport { useQueryClient } from './QueryClientProvider'\nimport { UseQueryOptions, UseQueryResult } from './types'\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\ntype GetOptions<T extends any> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptions<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryOptions<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryOptions<unknown, TError, TData>\n    : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n    T extends [infer TQueryFnData, infer TError, infer TData]\n    ? UseQueryOptions<TQueryFnData, TError, TData>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryOptions<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryOptions<TQueryFnData>\n    : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryOptions<TQueryFnData, unknown, TData, TQueryKey>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey> }\n    ? UseQueryOptions<TQueryFnData, unknown, TQueryFnData, TQueryKey>\n    : // Fallback\n      UseQueryOptions\n\ntype GetResults<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryResult<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryResult<TData, TError>\n    : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n    T extends [any, infer TError, infer TData]\n    ? UseQueryResult<TData, TError>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryResult<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryResult<TQueryFnData>\n    : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<any, any>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryResult<TData>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, any> }\n    ? UseQueryResult<TQueryFnData>\n    : // Fallback\n      UseQueryResult\n\n/**\n * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type QueriesOptions<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = []\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryOptions[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetOptions<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesOptions<[...Tail], [...Result, GetOptions<Head>], [...Depth, 1]>\n  : unknown[] extends T\n  ? T\n  : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n  // use this to infer the param types in the case of Array.map() argument\n  T extends UseQueryOptions<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      infer TQueryKey\n    >[]\n  ? UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>[]\n  : // Fallback\n    UseQueryOptions[]\n\n/**\n * QueriesResults reducer recursively maps type param to results\n */\nexport type QueriesResults<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = []\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryResult[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetResults<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesResults<[...Tail], [...Result, GetResults<Head>], [...Depth, 1]>\n  : T extends UseQueryOptions<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      any\n    >[]\n  ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n    UseQueryResult<unknown extends TData ? TQueryFnData : TData, TError>[]\n  : // Fallback\n    UseQueryResult[]\n\nexport function useQueries<T extends any[]>(\n  queries: readonly [...QueriesOptions<T>]\n): QueriesResults<T> {\n  const mountedRef = React.useRef(false)\n  const [, forceUpdate] = React.useState(0)\n\n  const queryClient = useQueryClient()\n\n  const defaultedQueries = useMemo(\n    () =>\n      queries.map(options => {\n        const defaultedOptions = queryClient.defaultQueryObserverOptions(\n          options\n        )\n\n        // Make sure the results are already in fetching state before subscribing or updating options\n        defaultedOptions.optimisticResults = true\n\n        return defaultedOptions\n      }),\n    [queries, queryClient]\n  )\n\n  const [observer] = React.useState(\n    () => new QueriesObserver(queryClient, defaultedQueries)\n  )\n\n  const result = observer.getOptimisticResult(defaultedQueries)\n\n  React.useEffect(() => {\n    mountedRef.current = true\n\n    const unsubscribe = observer.subscribe(\n      notifyManager.batchCalls(() => {\n        if (mountedRef.current) {\n          forceUpdate(x => x + 1)\n        }\n      })\n    )\n\n    return () => {\n      mountedRef.current = false\n      unsubscribe()\n    }\n  }, [observer])\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, { listeners: false })\n  }, [defaultedQueries, observer])\n\n  return result as QueriesResults<T>\n}\n", "import { QueryObserver } from '../core'\nimport { InfiniteQueryObserver } from '../core/infiniteQueryObserver'\nimport { QueryFunction, QueryKey } from '../core/types'\nimport { parseQueryArgs } from '../core/utils'\nimport { UseInfiniteQueryOptions, UseInfiniteQueryResult } from './types'\nimport { useBaseQuery } from './useBaseQuery'\n\n// HOOK\n\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  options: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  T<PERSON><PERSON><PERSON><PERSON>ey extends QueryKey = QueryKey\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey'\n  >\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey' | 'queryFn'\n  >\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  arg1:\n    | TQueryKey\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg3?: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >\n): UseInfiniteQueryResult<TData, TError> {\n  const options = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(\n    options,\n    InfiniteQueryObserver as typeof QueryObserver\n  ) as UseInfiniteQueryResult<TData, TError>\n}\n", "import React from 'react'\n\nimport { hydrate, HydrateOptions } from '../core'\nimport { useQueryClient } from './QueryClientProvider'\n\nexport function useHydrate(state: unknown, options?: HydrateOptions) {\n  const queryClient = useQueryClient()\n\n  const optionsRef = React.useRef(options)\n  optionsRef.current = options\n\n  // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n  React.useMemo(() => {\n    if (state) {\n      hydrate(queryClient, state, optionsRef.current)\n    }\n  }, [queryClient, state])\n}\n\nexport interface HydrateProps {\n  state?: unknown\n  options?: HydrateOptions\n  children?: React.ReactNode\n}\n\nexport const Hydrate = ({ children, options, state }: HydrateProps) => {\n  useHydrate(state, options)\n  return children as React.ReactElement\n}\n"], "names": ["_inherits<PERSON><PERSON>e", "subClass", "superClass", "prototype", "Object", "create", "constructor", "__proto__", "Subscribable", "listeners", "subscribe", "listener", "callback", "undefined", "push", "onSubscribe", "filter", "x", "onUnsubscribe", "hasListeners", "length", "_extends", "assign", "target", "i", "arguments", "source", "key", "hasOwnProperty", "call", "apply", "isServer", "window", "noop", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "Infinity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "difference", "array1", "array2", "indexOf", "replaceAt", "array", "index", "copy", "slice", "timeUntilStale", "updatedAt", "staleTime", "Math", "max", "Date", "now", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg1", "arg2", "arg3", "is<PERSON>uery<PERSON>ey", "query<PERSON><PERSON>", "queryFn", "parseMutationArgs", "<PERSON><PERSON><PERSON>", "mutationFn", "parseFilter<PERSON><PERSON>s", "parseMutationFilterArgs", "mapQueryStatusFilter", "active", "inactive", "isActive", "matchQuery", "filters", "query", "exact", "fetching", "predicate", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "queryS<PERSON>us<PERSON><PERSON>er", "isStale", "isFetching", "matchMutation", "mutation", "hashQuery<PERSON>ey", "state", "status", "hashFn", "queryKeyHashFn", "asArray", "stableValueHash", "JSON", "stringify", "_", "val", "isPlainObject", "keys", "sort", "reduce", "result", "a", "b", "partialDeepEqual", "some", "replaceEqualDeep", "aSize", "bItems", "bSize", "equalItems", "shallowEqualObjects", "o", "hasObjectPrototype", "ctor", "prot", "toString", "isError", "Error", "sleep", "timeout", "Promise", "resolve", "setTimeout", "scheduleMicrotask", "then", "catch", "error", "getAbortController", "AbortController", "FocusManager", "setup", "onFocus", "addEventListener", "removeEventListener", "cleanup", "setEventListener", "focused", "setFocused", "for<PERSON>ach", "isFocused", "document", "includes", "visibilityState", "focusManager", "OnlineManager", "onOnline", "online", "setOnline", "isOnline", "navigator", "onLine", "onlineManager", "defaultRetryDelay", "failureCount", "min", "isCancelable", "cancel", "CancelledError", "revert", "silent", "isCancelledError", "<PERSON><PERSON><PERSON>", "config", "cancelRetry", "cancelFn", "continueFn", "promiseResolve", "promiseReject", "abort", "cancelOptions", "continueRetry", "continue", "isPaused", "isResolved", "isTransportCancelable", "promise", "outerResolve", "outerReject", "onSuccess", "reject", "onError", "pause", "continueResolve", "onPause", "onContinue", "run", "promiseOrValue", "fn", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "NotifyManager", "queue", "transactions", "notifyFn", "batchNotifyFn", "batch", "flush", "schedule", "batchCalls", "args", "setNotifyFunction", "setBatchNotifyFunction", "notify<PERSON><PERSON>ger", "logger", "console", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Query", "abortSignalConsumed", "hadObservers", "defaultOptions", "setOptions", "observers", "cache", "initialState", "getDefaultState", "meta", "scheduleGc", "cacheTime", "setDefaultOptions", "clearGcTimeout", "gcTimeout", "optionalRemove", "clearTimeout", "remove", "setData", "prevData", "data", "isDataEqual", "structuralSharing", "dispatch", "type", "dataUpdatedAt", "setState", "setStateOptions", "retryer", "destroy", "reset", "observer", "enabled", "isInvalidated", "getCurrentResult", "isStaleByTime", "find", "shouldFetchOnWindowFocus", "refetch", "shouldFetchOnReconnect", "addObserver", "notify", "removeObserver", "getObserversCount", "invalidate", "fetch", "fetchOptions", "cancelRefetch", "abortController", "queryFnContext", "pageParam", "defineProperty", "enumerable", "get", "signal", "fetchFn", "context", "behavior", "onFetch", "revertState", "fetchMeta", "bind", "action", "reducer", "onQueryUpdate", "initialData", "hasInitialData", "initialDataUpdatedAt", "hasData", "dataUpdateCount", "errorUpdateCount", "errorUpdatedAt", "fetchFailureCount", "Query<PERSON>ache", "queries", "queriesMap", "build", "client", "defaultQueryOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "queryInMap", "clear", "getAll", "findAll", "event", "Mutation", "mutationId", "mutationCache", "execute", "restored", "variables", "onMutate", "executeMutation", "onSettled", "onMutationUpdate", "MutationCache", "mutations", "defaultMutationOptions", "getMutationDefaults", "resumePausedMutations", "pausedMutations", "infiniteQueryBehavior", "refetchPage", "fetchMore", "isFetchingNextPage", "direction", "isFetchingPreviousPage", "oldPages", "pages", "oldPageParams", "pageParams", "abortSignal", "newPageParams", "cancelled", "buildNewPages", "param", "page", "previous", "fetchPage", "manual", "queryFnResult", "promiseAsAny", "getNextPageParam", "getPreviousPageParam", "shouldFetchFirstPage", "shouldFetchNextPage", "finalPromise", "finalPromiseAsAny", "hasNextPage", "nextPageParam", "hasPreviousPage", "previousPageParam", "QueryClient", "queryCache", "queryDefaults", "mutationDefaults", "mount", "unsubscribeFocus", "unsubscribeOnline", "unmount", "isMutating", "getQueryData", "getQueriesData", "query<PERSON>eyOrFilters", "get<PERSON><PERSON><PERSON><PERSON>ache", "map", "setQueryData", "parsedOptions", "defaultedOptions", "setQueriesData", "getQueryState", "removeQueries", "resetQueries", "refetchFilters", "refetchQueries", "cancelQueries", "promises", "all", "invalidateQueries", "refetchActive", "refetchInactive", "throwOnError", "<PERSON><PERSON><PERSON><PERSON>", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "prefetchInfiniteQuery", "cancelMutations", "getMutationCache", "getDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setMutationDefaults", "_defaulted", "defaultQueryObserverOptions", "QueryObserver", "trackedProps", "selectError", "bindMethods", "<PERSON><PERSON><PERSON><PERSON>", "shouldFetchOnMount", "executeFetch", "updateTimers", "shouldFetchOn", "refetchOnReconnect", "refetchOnWindowFocus", "clearTimers", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "updateQuery", "mounted", "shouldFetchOptionally", "updateResult", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "createResult", "currentResult", "trackResult", "trackedResult", "trackProp", "configurable", "useErrorBoundary", "suspense", "getNextResult", "unsubscribe", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchOptimistic", "clearStaleTimeout", "time", "staleTimeoutId", "refetchInterval", "nextInterval", "clearRefetchInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "clearInterval", "prevResult", "prevResultState", "currentResultState", "prevResultOptions", "currentResultOptions", "query<PERSON>hange", "queryInitialState", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "isPreviousData", "isPlaceholderData", "optimisticResults", "fetchOnMount", "fetchOptionally", "keepPreviousData", "isSuccess", "select", "selectFn", "selectResult", "placeholderData", "isLoading", "isIdle", "isFetched", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isRefetchError", "shouldNotifyListeners", "notifyOnChangeProps", "notifyOnChangePropsExclusions", "includedProps", "<PERSON><PERSON><PERSON>", "changed", "isIncluded", "isExcluded", "defaultNotifyOptions", "shouldLoadOnMount", "retryOnMount", "refetchOnMount", "field", "QueriesObserver", "observersMap", "setQueries", "onUpdate", "updateObservers", "findMatchingObservers", "match", "defaultedQueryOptions", "prevObservers", "matchingObservers", "flatMap", "matchedQueryHashes", "unmatchedQueries", "unmatchedObservers", "prevObserver", "newOrReusedObservers", "previouslyUsedObserver", "getObserver", "sortMatchesByOrderOfQueries", "concat", "currentObserver", "newObserverMatches", "newObservers", "newObserversMap", "fromEntries", "newResult", "hasIndexChange", "InfiniteQueryObserver", "fetchNextPage", "fetchPreviousPage", "MutationObserver", "mutate", "currentMutation", "mutateOptions", "dehydrateMutation", "dehydrate<PERSON><PERSON>y", "defaultShouldDehydrateMutation", "defaultShouldDehydrateQuery", "dehydrate", "dehydrateMutations", "shouldDehydrateMutation", "dehydrateQueries", "shouldDehydrateQuery", "hydrate", "dehydratedState", "dehydratedMutation", "dehydrated<PERSON><PERSON>y", "defaultContext", "React", "createContext", "QueryClientSharingContext", "getQueryClientContext", "contextSharing", "ReactQueryClientContext", "useQueryClient", "queryClient", "useContext", "QueryClientProvider", "children", "useEffect", "Context", "createValue", "isReset", "clear<PERSON><PERSON>t", "QueryErrorResetBoundaryContext", "useQueryErrorResetBoundary", "QueryErrorResetBoundary", "useMemo", "checkIsFetching", "setIsFetching", "newIsFetching", "useIsFetching", "mountedRef", "useRef", "useState", "filtersRef", "current", "isFetchingRef", "useIsMutating", "setIsMutating", "isMutatingRef", "newIsMutating", "shouldThrowError", "_useErrorBoundary", "params", "useMutation", "forceUpdate", "obsRef", "useCallback", "mutateAsync", "useBaseQuery", "Observer", "errorResetBoundary", "useQuery", "useQueries", "defaultedQueries", "useInfiniteQuery", "useHydrate", "optionsRef", "Hydrate"], "mappings": ";;;;;;;;EAAe,SAASA,cAAT,CAAwBC,QAAxB,EAAkCC,UAAlC,EAA8C;EAC3DD,EAAAA,QAAQ,CAACE,SAAT,GAAqBC,MAAM,CAACC,MAAP,CAAcH,UAAU,CAACC,SAAzB,CAArB;EACAF,EAAAA,QAAQ,CAACE,SAAT,CAAmBG,WAAnB,GAAiCL,QAAjC;EACAA,EAAAA,QAAQ,CAACM,SAAT,GAAqBL,UAArB;EACD;;MCFYM,YAAb;EAGE,0BAAc;EACZ,SAAKC,SAAL,GAAiB,EAAjB;EACD;;EALH;;EAAA,SAOEC,SAPF,GAOE,mBAAUC,QAAV,EAA4C;EAAA;;EAC1C,QAAMC,QAAQ,GAAGD,QAAQ,IAAK;EAAA,aAAME,SAAN;EAAA,KAA9B;;EAEA,SAAKJ,SAAL,CAAeK,IAAf,CAAoBF,QAApB;EAEA,SAAKG,WAAL;EAEA,WAAO,YAAM;EACX,MAAA,KAAI,CAACN,SAAL,GAAiB,KAAI,CAACA,SAAL,CAAeO,MAAf,CAAsB,UAAAC,CAAC;EAAA,eAAIA,CAAC,KAAKL,QAAV;EAAA,OAAvB,CAAjB;;EACA,MAAA,KAAI,CAACM,aAAL;EACD,KAHD;EAID,GAlBH;;EAAA,SAoBEC,YApBF,GAoBE,wBAAwB;EACtB,WAAO,KAAKV,SAAL,CAAeW,MAAf,GAAwB,CAA/B;EACD,GAtBH;;EAAA,SAwBYL,WAxBZ,GAwBE,uBAA8B;EAE7B,GA1BH;;EAAA,SA4BYG,aA5BZ,GA4BE,yBAAgC;EAE/B,GA9BH;;EAAA;EAAA;;ECFe,SAASG,QAAT,GAAoB;EACjCA,EAAAA,QAAQ,GAAGjB,MAAM,CAACkB,MAAP,IAAiB,UAAUC,MAAV,EAAkB;EAC5C,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,SAAS,CAACL,MAA9B,EAAsCI,CAAC,EAAvC,EAA2C;EACzC,UAAIE,MAAM,GAAGD,SAAS,CAACD,CAAD,CAAtB;;EAEA,WAAK,IAAIG,GAAT,IAAgBD,MAAhB,EAAwB;EACtB,YAAItB,MAAM,CAACD,SAAP,CAAiByB,cAAjB,CAAgCC,IAAhC,CAAqCH,MAArC,EAA6CC,GAA7C,CAAJ,EAAuD;EACrDJ,UAAAA,MAAM,CAACI,GAAD,CAAN,GAAcD,MAAM,CAACC,GAAD,CAApB;EACD;EACF;EACF;;EAED,WAAOJ,MAAP;EACD,GAZD;;EAcA,SAAOF,QAAQ,CAACS,KAAT,CAAe,IAAf,EAAqBL,SAArB,CAAP;EACD;;ECJD;EA4DA;EAEO,IAAMM,QAAQ,GAAG,OAAOC,MAAP,KAAkB,WAAnC;EAEA,SAASC,IAAT,GAA2B;EAChC,SAAOpB,SAAP;EACD;EAEM,SAASqB,gBAAT,CACLC,OADK,EAELC,KAFK,EAGI;EACT,SAAO,OAAOD,OAAP,KAAmB,UAAnB,GACFA,OAAD,CAAiDC,KAAjD,CADG,GAEHD,OAFJ;EAGD;EAEM,SAASE,cAAT,CAAwBC,KAAxB,EAAyD;EAC9D,SAAO,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,IAAI,CAAtC,IAA2CA,KAAK,KAAKC,QAA5D;EACD;EAEM,SAASC,mBAAT,CACLF,KADK,EAEe;EACpB,SAAQG,KAAK,CAACC,OAAN,CAAcJ,KAAd,IACJA,KADI,GAEH,CAACA,KAAD,CAFL;EAGD;EAEM,SAASK,UAAT,CAAuBC,MAAvB,EAAoCC,MAApC,EAAsD;EAC3D,SAAOD,MAAM,CAAC5B,MAAP,CAAc,UAAAC,CAAC;EAAA,WAAI4B,MAAM,CAACC,OAAP,CAAe7B,CAAf,MAAsB,CAAC,CAA3B;EAAA,GAAf,CAAP;EACD;EAEM,SAAS8B,SAAT,CAAsBC,KAAtB,EAAkCC,KAAlC,EAAiDX,KAAjD,EAAgE;EACrE,MAAMY,IAAI,GAAGF,KAAK,CAACG,KAAN,CAAY,CAAZ,CAAb;EACAD,EAAAA,IAAI,CAACD,KAAD,CAAJ,GAAcX,KAAd;EACA,SAAOY,IAAP;EACD;EAEM,SAASE,cAAT,CAAwBC,SAAxB,EAA2CC,SAA3C,EAAuE;EAC5E,SAAOC,IAAI,CAACC,GAAL,CAASH,SAAS,IAAIC,SAAS,IAAI,CAAjB,CAAT,GAA+BG,IAAI,CAACC,GAAL,EAAxC,EAAoD,CAApD,CAAP;EACD;EAEM,SAASC,cAAT,CAILC,IAJK,EAKLC,IALK,EAMLC,IANK,EAOK;EACV,MAAI,CAACC,UAAU,CAACH,IAAD,CAAf,EAAuB;EACrB,WAAOA,IAAP;EACD;;EAED,MAAI,OAAOC,IAAP,KAAgB,UAApB,EAAgC;EAC9B,wBAAYC,IAAZ;EAAkBE,MAAAA,QAAQ,EAAEJ,IAA5B;EAAkCK,MAAAA,OAAO,EAAEJ;EAA3C;EACD;;EAED,sBAAYA,IAAZ;EAAkBG,IAAAA,QAAQ,EAAEJ;EAA5B;EACD;EAEM,SAASM,iBAAT,CAGLN,IAHK,EAILC,IAJK,EAKLC,IALK,EAMK;EACV,MAAIC,UAAU,CAACH,IAAD,CAAd,EAAsB;EACpB,QAAI,OAAOC,IAAP,KAAgB,UAApB,EAAgC;EAC9B,0BAAYC,IAAZ;EAAkBK,QAAAA,WAAW,EAAEP,IAA/B;EAAqCQ,QAAAA,UAAU,EAAEP;EAAjD;EACD;;EACD,wBAAYA,IAAZ;EAAkBM,MAAAA,WAAW,EAAEP;EAA/B;EACD;;EAED,MAAI,OAAOA,IAAP,KAAgB,UAApB,EAAgC;EAC9B,wBAAYC,IAAZ;EAAkBO,MAAAA,UAAU,EAAER;EAA9B;EACD;;EAED,sBAAYA,IAAZ;EACD;EAEM,SAASS,eAAT,CAILT,IAJK,EAKLC,IALK,EAMLC,IANK,EAO6B;EAClC,SAAQC,UAAU,CAACH,IAAD,CAAV,GACJ,cAAMC,IAAN;EAAYG,IAAAA,QAAQ,EAAEJ;EAAtB,MAA8BE,IAA9B,CADI,GAEJ,CAACF,IAAI,IAAI,EAAT,EAAaC,IAAb,CAFJ;EAGD;EAEM,SAASS,uBAAT,CACLV,IADK,EAELC,IAFK,EAGwB;EAC7B,SAAOE,UAAU,CAACH,IAAD,CAAV,gBAAwBC,IAAxB;EAA8BM,IAAAA,WAAW,EAAEP;EAA3C,OAAoDA,IAA3D;EACD;EAEM,SAASW,oBAAT,CACLC,MADK,EAELC,QAFK,EAGc;EACnB,MACGD,MAAM,KAAK,IAAX,IAAmBC,QAAQ,KAAK,IAAjC,IACCD,MAAM,IAAI,IAAV,IAAkBC,QAAQ,IAAI,IAFjC,EAGE;EACA,WAAO,KAAP;EACD,GALD,MAKO,IAAID,MAAM,KAAK,KAAX,IAAoBC,QAAQ,KAAK,KAArC,EAA4C;EACjD,WAAO,MAAP;EACD,GAFM,MAEA;EACL;EACA;EACA,QAAMC,QAAQ,GAAGF,MAAH,WAAGA,MAAH,GAAa,CAACC,QAA5B;EACA,WAAOC,QAAQ,GAAG,QAAH,GAAc,UAA7B;EACD;EACF;EAEM,SAASC,UAAT,CACLC,OADK,EAELC,KAFK,EAGI;EAAA,MAEPL,MAFO,GASLI,OATK,CAEPJ,MAFO;EAAA,MAGPM,KAHO,GASLF,OATK,CAGPE,KAHO;EAAA,MAIPC,QAJO,GASLH,OATK,CAIPG,QAJO;EAAA,MAKPN,QALO,GASLG,OATK,CAKPH,QALO;EAAA,MAMPO,SANO,GASLJ,OATK,CAMPI,SANO;EAAA,MAOPhB,QAPO,GASLY,OATK,CAOPZ,QAPO;EAAA,MAQPiB,KARO,GASLL,OATK,CAQPK,KARO;;EAWT,MAAIlB,UAAU,CAACC,QAAD,CAAd,EAA0B;EACxB,QAAIc,KAAJ,EAAW;EACT,UAAID,KAAK,CAACK,SAAN,KAAoBC,qBAAqB,CAACnB,QAAD,EAAWa,KAAK,CAACO,OAAjB,CAA7C,EAAwE;EACtE,eAAO,KAAP;EACD;EACF,KAJD,MAIO,IAAI,CAACC,eAAe,CAACR,KAAK,CAACb,QAAP,EAAiBA,QAAjB,CAApB,EAAgD;EACrD,aAAO,KAAP;EACD;EACF;;EAED,MAAMsB,iBAAiB,GAAGf,oBAAoB,CAACC,MAAD,EAASC,QAAT,CAA9C;;EAEA,MAAIa,iBAAiB,KAAK,MAA1B,EAAkC;EAChC,WAAO,KAAP;EACD,GAFD,MAEO,IAAIA,iBAAiB,KAAK,KAA1B,EAAiC;EACtC,QAAMZ,QAAQ,GAAGG,KAAK,CAACH,QAAN,EAAjB;;EACA,QAAIY,iBAAiB,KAAK,QAAtB,IAAkC,CAACZ,QAAvC,EAAiD;EAC/C,aAAO,KAAP;EACD;;EACD,QAAIY,iBAAiB,KAAK,UAAtB,IAAoCZ,QAAxC,EAAkD;EAChD,aAAO,KAAP;EACD;EACF;;EAED,MAAI,OAAOO,KAAP,KAAiB,SAAjB,IAA8BJ,KAAK,CAACU,OAAN,OAAoBN,KAAtD,EAA6D;EAC3D,WAAO,KAAP;EACD;;EAED,MAAI,OAAOF,QAAP,KAAoB,SAApB,IAAiCF,KAAK,CAACW,UAAN,OAAuBT,QAA5D,EAAsE;EACpE,WAAO,KAAP;EACD;;EAED,MAAIC,SAAS,IAAI,CAACA,SAAS,CAACH,KAAD,CAA3B,EAAoC;EAClC,WAAO,KAAP;EACD;;EAED,SAAO,IAAP;EACD;EAEM,SAASY,aAAT,CACLb,OADK,EAELc,QAFK,EAGI;EAAA,MACDZ,KADC,GAC2CF,OAD3C,CACDE,KADC;EAAA,MACMC,QADN,GAC2CH,OAD3C,CACMG,QADN;EAAA,MACgBC,SADhB,GAC2CJ,OAD3C,CACgBI,SADhB;EAAA,MAC2Bb,WAD3B,GAC2CS,OAD3C,CAC2BT,WAD3B;;EAET,MAAIJ,UAAU,CAACI,WAAD,CAAd,EAA6B;EAC3B,QAAI,CAACuB,QAAQ,CAACN,OAAT,CAAiBjB,WAAtB,EAAmC;EACjC,aAAO,KAAP;EACD;;EACD,QAAIW,KAAJ,EAAW;EACT,UACEa,YAAY,CAACD,QAAQ,CAACN,OAAT,CAAiBjB,WAAlB,CAAZ,KAA+CwB,YAAY,CAACxB,WAAD,CAD7D,EAEE;EACA,eAAO,KAAP;EACD;EACF,KAND,MAMO,IAAI,CAACkB,eAAe,CAACK,QAAQ,CAACN,OAAT,CAAiBjB,WAAlB,EAA+BA,WAA/B,CAApB,EAAiE;EACtE,aAAO,KAAP;EACD;EACF;;EAED,MACE,OAAOY,QAAP,KAAoB,SAApB,IACCW,QAAQ,CAACE,KAAT,CAAeC,MAAf,KAA0B,SAA3B,KAA0Cd,QAF5C,EAGE;EACA,WAAO,KAAP;EACD;;EAED,MAAIC,SAAS,IAAI,CAACA,SAAS,CAACU,QAAD,CAA3B,EAAuC;EACrC,WAAO,KAAP;EACD;;EAED,SAAO,IAAP;EACD;EAEM,SAASP,qBAAT,CACLnB,QADK,EAELoB,OAFK,EAGG;EACR,MAAMU,MAAM,GAAG,CAAAV,OAAO,QAAP,YAAAA,OAAO,CAAEW,cAAT,KAA2BJ,YAA1C;EACA,SAAOG,MAAM,CAAC9B,QAAD,CAAb;EACD;EAED;;;;EAGO,SAAS2B,YAAT,CAAsB3B,QAAtB,EAAkD;EACvD,MAAMgC,OAAO,GAAGxD,mBAAmB,CAACwB,QAAD,CAAnC;EACA,SAAOiC,eAAe,CAACD,OAAD,CAAtB;EACD;EAED;;;;EAGO,SAASC,eAAT,CAAyB3D,KAAzB,EAA6C;EAClD,SAAO4D,IAAI,CAACC,SAAL,CAAe7D,KAAf,EAAsB,UAAC8D,CAAD,EAAIC,GAAJ;EAAA,WAC3BC,aAAa,CAACD,GAAD,CAAb,GACIjG,MAAM,CAACmG,IAAP,CAAYF,GAAZ,EACGG,IADH,GAEGC,MAFH,CAEU,UAACC,MAAD,EAAS/E,GAAT,EAAiB;EACvB+E,MAAAA,MAAM,CAAC/E,GAAD,CAAN,GAAc0E,GAAG,CAAC1E,GAAD,CAAjB;EACA,aAAO+E,MAAP;EACD,KALH,EAKK,EALL,CADJ,GAOIL,GARuB;EAAA,GAAtB,CAAP;EAUD;EAED;;;;EAGO,SAAShB,eAAT,CAAyBsB,CAAzB,EAAsCC,CAAtC,EAA4D;EACjE,SAAOC,gBAAgB,CAACrE,mBAAmB,CAACmE,CAAD,CAApB,EAAyBnE,mBAAmB,CAACoE,CAAD,CAA5C,CAAvB;EACD;EAED;;;;EAGO,SAASC,gBAAT,CAA0BF,CAA1B,EAAkCC,CAAlC,EAAmD;EACxD,MAAID,CAAC,KAAKC,CAAV,EAAa;EACX,WAAO,IAAP;EACD;;EAED,MAAI,OAAOD,CAAP,KAAa,OAAOC,CAAxB,EAA2B;EACzB,WAAO,KAAP;EACD;;EAED,MAAID,CAAC,IAAIC,CAAL,IAAU,OAAOD,CAAP,KAAa,QAAvB,IAAmC,OAAOC,CAAP,KAAa,QAApD,EAA8D;EAC5D,WAAO,CAACxG,MAAM,CAACmG,IAAP,CAAYK,CAAZ,EAAeE,IAAf,CAAoB,UAAAnF,GAAG;EAAA,aAAI,CAACkF,gBAAgB,CAACF,CAAC,CAAChF,GAAD,CAAF,EAASiF,CAAC,CAACjF,GAAD,CAAV,CAArB;EAAA,KAAvB,CAAR;EACD;;EAED,SAAO,KAAP;EACD;EAED;;;;;;EAMO,SAASoF,gBAAT,CAA0BJ,CAA1B,EAAkCC,CAAlC,EAA+C;EACpD,MAAID,CAAC,KAAKC,CAAV,EAAa;EACX,WAAOD,CAAP;EACD;;EAED,MAAM3D,KAAK,GAAGP,KAAK,CAACC,OAAN,CAAciE,CAAd,KAAoBlE,KAAK,CAACC,OAAN,CAAckE,CAAd,CAAlC;;EAEA,MAAI5D,KAAK,IAAKsD,aAAa,CAACK,CAAD,CAAb,IAAoBL,aAAa,CAACM,CAAD,CAA/C,EAAqD;EACnD,QAAMI,KAAK,GAAGhE,KAAK,GAAG2D,CAAC,CAACvF,MAAL,GAAchB,MAAM,CAACmG,IAAP,CAAYI,CAAZ,EAAevF,MAAhD;EACA,QAAM6F,MAAM,GAAGjE,KAAK,GAAG4D,CAAH,GAAOxG,MAAM,CAACmG,IAAP,CAAYK,CAAZ,CAA3B;EACA,QAAMM,KAAK,GAAGD,MAAM,CAAC7F,MAArB;EACA,QAAM8B,IAAS,GAAGF,KAAK,GAAG,EAAH,GAAQ,EAA/B;EAEA,QAAImE,UAAU,GAAG,CAAjB;;EAEA,SAAK,IAAI3F,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0F,KAApB,EAA2B1F,CAAC,EAA5B,EAAgC;EAC9B,UAAMG,GAAG,GAAGqB,KAAK,GAAGxB,CAAH,GAAOyF,MAAM,CAACzF,CAAD,CAA9B;EACA0B,MAAAA,IAAI,CAACvB,GAAD,CAAJ,GAAYoF,gBAAgB,CAACJ,CAAC,CAAChF,GAAD,CAAF,EAASiF,CAAC,CAACjF,GAAD,CAAV,CAA5B;;EACA,UAAIuB,IAAI,CAACvB,GAAD,CAAJ,KAAcgF,CAAC,CAAChF,GAAD,CAAnB,EAA0B;EACxBwF,QAAAA,UAAU;EACX;EACF;;EAED,WAAOH,KAAK,KAAKE,KAAV,IAAmBC,UAAU,KAAKH,KAAlC,GAA0CL,CAA1C,GAA8CzD,IAArD;EACD;;EAED,SAAO0D,CAAP;EACD;EAED;;;;EAGO,SAASQ,mBAAT,CAAgCT,CAAhC,EAAsCC,CAAtC,EAAqD;EAC1D,MAAKD,CAAC,IAAI,CAACC,CAAP,IAAcA,CAAC,IAAI,CAACD,CAAxB,EAA4B;EAC1B,WAAO,KAAP;EACD;;EAED,OAAK,IAAMhF,GAAX,IAAkBgF,CAAlB,EAAqB;EACnB,QAAIA,CAAC,CAAChF,GAAD,CAAD,KAAWiF,CAAC,CAACjF,GAAD,CAAhB,EAAuB;EACrB,aAAO,KAAP;EACD;EACF;;EAED,SAAO,IAAP;EACD;;EAGM,SAAS2E,aAAT,CAAuBe,CAAvB,EAA4C;EACjD,MAAI,CAACC,kBAAkB,CAACD,CAAD,CAAvB,EAA4B;EAC1B,WAAO,KAAP;EACD,GAHgD;;;EAMjD,MAAME,IAAI,GAAGF,CAAC,CAAC/G,WAAf;;EACA,MAAI,OAAOiH,IAAP,KAAgB,WAApB,EAAiC;EAC/B,WAAO,IAAP;EACD,GATgD;;;EAYjD,MAAMC,IAAI,GAAGD,IAAI,CAACpH,SAAlB;;EACA,MAAI,CAACmH,kBAAkB,CAACE,IAAD,CAAvB,EAA+B;EAC7B,WAAO,KAAP;EACD,GAfgD;;;EAkBjD,MAAI,CAACA,IAAI,CAAC5F,cAAL,CAAoB,eAApB,CAAL,EAA2C;EACzC,WAAO,KAAP;EACD,GApBgD;;;EAuBjD,SAAO,IAAP;EACD;;EAED,SAAS0F,kBAAT,CAA4BD,CAA5B,EAA6C;EAC3C,SAAOjH,MAAM,CAACD,SAAP,CAAiBsH,QAAjB,CAA0B5F,IAA1B,CAA+BwF,CAA/B,MAAsC,iBAA7C;EACD;;EAEM,SAAStD,UAAT,CAAoBzB,KAApB,EAAmD;EACxD,SAAO,OAAOA,KAAP,KAAiB,QAAjB,IAA6BG,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAApC;EACD;EAEM,SAASoF,OAAT,CAAiBpF,KAAjB,EAA6C;EAClD,SAAOA,KAAK,YAAYqF,KAAxB;EACD;EAEM,SAASC,KAAT,CAAeC,OAAf,EAA+C;EACpD,SAAO,IAAIC,OAAJ,CAAY,UAAAC,OAAO,EAAI;EAC5BC,IAAAA,UAAU,CAACD,OAAD,EAAUF,OAAV,CAAV;EACD,GAFM,CAAP;EAGD;EAED;;;;;EAIO,SAASI,iBAAT,CAA2BrH,QAA3B,EAAuD;EAC5DkH,EAAAA,OAAO,CAACC,OAAR,GACGG,IADH,CACQtH,QADR,EAEGuH,KAFH,CAES,UAAAC,KAAK;EAAA,WACVJ,UAAU,CAAC,YAAM;EACf,YAAMI,KAAN;EACD,KAFS,CADA;EAAA,GAFd;EAOD;EAEM,SAASC,kBAAT,GAA2D;EAChE,MAAI,OAAOC,eAAP,KAA2B,UAA/B,EAA2C;EACzC,WAAO,IAAIA,eAAJ,EAAP;EACD;EACF;;MC/bYC,YAAb;EAAA;;EAME,0BAAc;EAAA;;EACZ;;EACA,UAAKC,KAAL,GAAa,UAAAC,OAAO,EAAI;EAAA;;EACtB,UAAI,CAAC1G,QAAD,gBAAaC,MAAb,qBAAa,QAAQ0G,gBAArB,CAAJ,EAA2C;EACzC,YAAM/H,QAAQ,GAAG,SAAXA,QAAW;EAAA,iBAAM8H,OAAO,EAAb;EAAA,SAAjB,CADyC;;;EAGzCzG,QAAAA,MAAM,CAAC0G,gBAAP,CAAwB,kBAAxB,EAA4C/H,QAA5C,EAAsD,KAAtD;EACAqB,QAAAA,MAAM,CAAC0G,gBAAP,CAAwB,OAAxB,EAAiC/H,QAAjC,EAA2C,KAA3C;EAEA,eAAO,YAAM;EACX;EACAqB,UAAAA,MAAM,CAAC2G,mBAAP,CAA2B,kBAA3B,EAA+ChI,QAA/C;EACAqB,UAAAA,MAAM,CAAC2G,mBAAP,CAA2B,OAA3B,EAAoChI,QAApC;EACD,SAJD;EAKD;EACF,KAbD;;EAFY;EAgBb;;EAtBH;;EAAA,SAwBYI,WAxBZ,GAwBE,uBAA8B;EAC5B,QAAI,CAAC,KAAK6H,OAAV,EAAmB;EACjB,WAAKC,gBAAL,CAAsB,KAAKL,KAA3B;EACD;EACF,GA5BH;;EAAA,SA8BYtH,aA9BZ,GA8BE,yBAA0B;EACxB,QAAI,CAAC,KAAKC,YAAL,EAAL,EAA0B;EAAA;;EACxB,4BAAKyH,OAAL;EACA,WAAKA,OAAL,GAAe/H,SAAf;EACD;EACF,GAnCH;;EAAA,SAqCEgI,gBArCF,GAqCE,0BAAiBL,KAAjB,EAAuC;EAAA;EAAA;;EACrC,SAAKA,KAAL,GAAaA,KAAb;EACA,2BAAKI,OAAL;EACA,SAAKA,OAAL,GAAeJ,KAAK,CAAC,UAAAM,OAAO,EAAI;EAC9B,UAAI,OAAOA,OAAP,KAAmB,SAAvB,EAAkC;EAChC,QAAA,MAAI,CAACC,UAAL,CAAgBD,OAAhB;EACD,OAFD,MAEO;EACL,QAAA,MAAI,CAACL,OAAL;EACD;EACF,KANmB,CAApB;EAOD,GA/CH;;EAAA,SAiDEM,UAjDF,GAiDE,oBAAWD,OAAX,EAAoC;EAClC,SAAKA,OAAL,GAAeA,OAAf;;EAEA,QAAIA,OAAJ,EAAa;EACX,WAAKL,OAAL;EACD;EACF,GAvDH;;EAAA,SAyDEA,OAzDF,GAyDE,mBAAgB;EACd,SAAKhI,SAAL,CAAeuI,OAAf,CAAuB,UAAArI,QAAQ,EAAI;EACjCA,MAAAA,QAAQ;EACT,KAFD;EAGD,GA7DH;;EAAA,SA+DEsI,SA/DF,GA+DE,qBAAqB;EACnB,QAAI,OAAO,KAAKH,OAAZ,KAAwB,SAA5B,EAAuC;EACrC,aAAO,KAAKA,OAAZ;EACD,KAHkB;;;EAMnB,QAAI,OAAOI,QAAP,KAAoB,WAAxB,EAAqC;EACnC,aAAO,IAAP;EACD;;EAED,WAAO,CAACrI,SAAD,EAAY,SAAZ,EAAuB,WAAvB,EAAoCsI,QAApC,CACLD,QAAQ,CAACE,eADJ,CAAP;EAGD,GA5EH;;EAAA;EAAA,EAAkC5I,YAAlC;MA+Ea6I,YAAY,GAAG,IAAId,YAAJ;;MC/Efe,aAAb;EAAA;;EAME,2BAAc;EAAA;;EACZ;;EACA,UAAKd,KAAL,GAAa,UAAAe,QAAQ,EAAI;EAAA;;EACvB,UAAI,CAACxH,QAAD,gBAAaC,MAAb,qBAAa,QAAQ0G,gBAArB,CAAJ,EAA2C;EACzC,YAAM/H,QAAQ,GAAG,SAAXA,QAAW;EAAA,iBAAM4I,QAAQ,EAAd;EAAA,SAAjB,CADyC;;;EAGzCvH,QAAAA,MAAM,CAAC0G,gBAAP,CAAwB,QAAxB,EAAkC/H,QAAlC,EAA4C,KAA5C;EACAqB,QAAAA,MAAM,CAAC0G,gBAAP,CAAwB,SAAxB,EAAmC/H,QAAnC,EAA6C,KAA7C;EAEA,eAAO,YAAM;EACX;EACAqB,UAAAA,MAAM,CAAC2G,mBAAP,CAA2B,QAA3B,EAAqChI,QAArC;EACAqB,UAAAA,MAAM,CAAC2G,mBAAP,CAA2B,SAA3B,EAAsChI,QAAtC;EACD,SAJD;EAKD;EACF,KAbD;;EAFY;EAgBb;;EAtBH;;EAAA,SAwBYI,WAxBZ,GAwBE,uBAA8B;EAC5B,QAAI,CAAC,KAAK6H,OAAV,EAAmB;EACjB,WAAKC,gBAAL,CAAsB,KAAKL,KAA3B;EACD;EACF,GA5BH;;EAAA,SA8BYtH,aA9BZ,GA8BE,yBAA0B;EACxB,QAAI,CAAC,KAAKC,YAAL,EAAL,EAA0B;EAAA;;EACxB,4BAAKyH,OAAL;EACA,WAAKA,OAAL,GAAe/H,SAAf;EACD;EACF,GAnCH;;EAAA,SAqCEgI,gBArCF,GAqCE,0BAAiBL,KAAjB,EAAuC;EAAA;EAAA;;EACrC,SAAKA,KAAL,GAAaA,KAAb;EACA,2BAAKI,OAAL;EACA,SAAKA,OAAL,GAAeJ,KAAK,CAAC,UAACgB,MAAD,EAAsB;EACzC,UAAI,OAAOA,MAAP,KAAkB,SAAtB,EAAiC;EAC/B,QAAA,MAAI,CAACC,SAAL,CAAeD,MAAf;EACD,OAFD,MAEO;EACL,QAAA,MAAI,CAACD,QAAL;EACD;EACF,KANmB,CAApB;EAOD,GA/CH;;EAAA,SAiDEE,SAjDF,GAiDE,mBAAUD,MAAV,EAAkC;EAChC,SAAKA,MAAL,GAAcA,MAAd;;EAEA,QAAIA,MAAJ,EAAY;EACV,WAAKD,QAAL;EACD;EACF,GAvDH;;EAAA,SAyDEA,QAzDF,GAyDE,oBAAiB;EACf,SAAK9I,SAAL,CAAeuI,OAAf,CAAuB,UAAArI,QAAQ,EAAI;EACjCA,MAAAA,QAAQ;EACT,KAFD;EAGD,GA7DH;;EAAA,SA+DE+I,QA/DF,GA+DE,oBAAoB;EAClB,QAAI,OAAO,KAAKF,MAAZ,KAAuB,SAA3B,EAAsC;EACpC,aAAO,KAAKA,MAAZ;EACD;;EAED,QACE,OAAOG,SAAP,KAAqB,WAArB,IACA,OAAOA,SAAS,CAACC,MAAjB,KAA4B,WAF9B,EAGE;EACA,aAAO,IAAP;EACD;;EAED,WAAOD,SAAS,CAACC,MAAjB;EACD,GA5EH;;EAAA;EAAA,EAAmCpJ,YAAnC;MA+EaqJ,aAAa,GAAG,IAAIP,aAAJ;;ECrD7B,SAASQ,iBAAT,CAA2BC,YAA3B,EAAiD;EAC/C,SAAOxG,IAAI,CAACyG,GAAL,CAAS,gBAAO,CAAP,EAAYD,YAAZ,CAAT,EAAmC,KAAnC,CAAP;EACD;;EAMM,SAASE,YAAT,CAAsB3H,KAAtB,EAAuD;EAC5D,SAAO,QAAOA,KAAP,oBAAOA,KAAK,CAAE4H,MAAd,MAAyB,UAAhC;EACD;MAEYC,cAAb,GAGE,wBAAY/E,OAAZ,EAAqC;EACnC,OAAKgF,MAAL,GAAchF,OAAd,oBAAcA,OAAO,CAAEgF,MAAvB;EACA,OAAKC,MAAL,GAAcjF,OAAd,oBAAcA,OAAO,CAAEiF,MAAvB;EACD;EAGI,SAASC,gBAAT,CAA0BhI,KAA1B,EAA+D;EACpE,SAAOA,KAAK,YAAY6H,cAAxB;EACD;;MAIYI,OAAb,GAaE,iBAAYC,MAAZ,EAAkD;EAAA;;EAChD,MAAIC,WAAW,GAAG,KAAlB;EACA,MAAIC,QAAJ;EACA,MAAIC,UAAJ;EACA,MAAIC,cAAJ;EACA,MAAIC,aAAJ;EAEA,OAAKC,KAAL,GAAaN,MAAM,CAACM,KAApB;;EACA,OAAKZ,MAAL,GAAc,UAAAa,aAAa;EAAA,WAAIL,QAAJ,oBAAIA,QAAQ,CAAGK,aAAH,CAAZ;EAAA,GAA3B;;EACA,OAAKN,WAAL,GAAmB,YAAM;EACvBA,IAAAA,WAAW,GAAG,IAAd;EACD,GAFD;;EAGA,OAAKO,aAAL,GAAqB,YAAM;EACzBP,IAAAA,WAAW,GAAG,KAAd;EACD,GAFD;;EAGA,OAAKQ,QAAL,GAAgB;EAAA,WAAMN,UAAN,oBAAMA,UAAU,EAAhB;EAAA,GAAhB;;EACA,OAAKZ,YAAL,GAAoB,CAApB;EACA,OAAKmB,QAAL,GAAgB,KAAhB;EACA,OAAKC,UAAL,GAAkB,KAAlB;EACA,OAAKC,qBAAL,GAA6B,KAA7B;EACA,OAAKC,OAAL,GAAe,IAAIvD,OAAJ,CAAmB,UAACwD,YAAD,EAAeC,WAAf,EAA+B;EAC/DX,IAAAA,cAAc,GAAGU,YAAjB;EACAT,IAAAA,aAAa,GAAGU,WAAhB;EACD,GAHc,CAAf;;EAKA,MAAMxD,OAAO,GAAG,SAAVA,OAAU,CAACzF,KAAD,EAAgB;EAC9B,QAAI,CAAC,KAAI,CAAC6I,UAAV,EAAsB;EACpB,MAAA,KAAI,CAACA,UAAL,GAAkB,IAAlB;EACAX,MAAAA,MAAM,CAACgB,SAAP,oBAAAhB,MAAM,CAACgB,SAAP,CAAmBlJ,KAAnB;EACAqI,MAAAA,UAAU,QAAV,YAAAA,UAAU;EACVC,MAAAA,cAAc,CAACtI,KAAD,CAAd;EACD;EACF,GAPD;;EASA,MAAMmJ,MAAM,GAAG,SAATA,MAAS,CAACnJ,KAAD,EAAgB;EAC7B,QAAI,CAAC,KAAI,CAAC6I,UAAV,EAAsB;EACpB,MAAA,KAAI,CAACA,UAAL,GAAkB,IAAlB;EACAX,MAAAA,MAAM,CAACkB,OAAP,oBAAAlB,MAAM,CAACkB,OAAP,CAAiBpJ,KAAjB;EACAqI,MAAAA,UAAU,QAAV,YAAAA,UAAU;EACVE,MAAAA,aAAa,CAACvI,KAAD,CAAb;EACD;EACF,GAPD;;EASA,MAAMqJ,KAAK,GAAG,SAARA,KAAQ,GAAM;EAClB,WAAO,IAAI7D,OAAJ,CAAY,UAAA8D,eAAe,EAAI;EACpCjB,MAAAA,UAAU,GAAGiB,eAAb;EACA,MAAA,KAAI,CAACV,QAAL,GAAgB,IAAhB;EACAV,MAAAA,MAAM,CAACqB,OAAP,oBAAArB,MAAM,CAACqB,OAAP;EACD,KAJM,EAIJ3D,IAJI,CAIC,YAAM;EACZyC,MAAAA,UAAU,GAAG9J,SAAb;EACA,MAAA,KAAI,CAACqK,QAAL,GAAgB,KAAhB;EACAV,MAAAA,MAAM,CAACsB,UAAP,oBAAAtB,MAAM,CAACsB,UAAP;EACD,KARM,CAAP;EASD,GAVD,CA3CgD;;;EAwDhD,MAAMC,GAAG,GAAG,SAANA,GAAM,GAAM;EAChB;EACA,QAAI,KAAI,CAACZ,UAAT,EAAqB;EACnB;EACD;;EAED,QAAIa,cAAJ,CANgB;;EAShB,QAAI;EACFA,MAAAA,cAAc,GAAGxB,MAAM,CAACyB,EAAP,EAAjB;EACD,KAFD,CAEE,OAAO7D,KAAP,EAAc;EACd4D,MAAAA,cAAc,GAAGlE,OAAO,CAAC2D,MAAR,CAAerD,KAAf,CAAjB;EACD,KAbe;;;EAgBhBsC,IAAAA,QAAQ,GAAG,kBAAAK,aAAa,EAAI;EAC1B,UAAI,CAAC,KAAI,CAACI,UAAV,EAAsB;EACpBM,QAAAA,MAAM,CAAC,IAAItB,cAAJ,CAAmBY,aAAnB,CAAD,CAAN;EAEA,QAAA,KAAI,CAACD,KAAL,oBAAA,KAAI,CAACA,KAAL,GAHoB;;EAMpB,YAAIb,YAAY,CAAC+B,cAAD,CAAhB,EAAkC;EAChC,cAAI;EACFA,YAAAA,cAAc,CAAC9B,MAAf;EACD,WAFD,CAEE,gBAAM;EACT;EACF;EACF,KAbD,CAhBgB;;;EAgChB,IAAA,KAAI,CAACkB,qBAAL,GAA6BnB,YAAY,CAAC+B,cAAD,CAAzC;EAEAlE,IAAAA,OAAO,CAACC,OAAR,CAAgBiE,cAAhB,EACG9D,IADH,CACQH,OADR,EAEGI,KAFH,CAES,UAAAC,KAAK,EAAI;EAAA;;EACd;EACA,UAAI,KAAI,CAAC+C,UAAT,EAAqB;EACnB;EACD,OAJa;;;EAOd,UAAMe,KAAK,oBAAG1B,MAAM,CAAC0B,KAAV,4BAAmB,CAA9B;EACA,UAAMC,UAAU,yBAAG3B,MAAM,CAAC2B,UAAV,iCAAwBrC,iBAAxC;EACA,UAAMsC,KAAK,GACT,OAAOD,UAAP,KAAsB,UAAtB,GACIA,UAAU,CAAC,KAAI,CAACpC,YAAN,EAAoB3B,KAApB,CADd,GAEI+D,UAHN;EAIA,UAAME,WAAW,GACfH,KAAK,KAAK,IAAV,IACC,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,KAAI,CAACnC,YAAL,GAAoBmC,KADlD,IAEC,OAAOA,KAAP,KAAiB,UAAjB,IAA+BA,KAAK,CAAC,KAAI,CAACnC,YAAN,EAAoB3B,KAApB,CAHvC;;EAKA,UAAIqC,WAAW,IAAI,CAAC4B,WAApB,EAAiC;EAC/B;EACAZ,QAAAA,MAAM,CAACrD,KAAD,CAAN;EACA;EACD;;EAED,MAAA,KAAI,CAAC2B,YAAL,GAxBc;;EA2BdS,MAAAA,MAAM,CAAC8B,MAAP,oBAAA9B,MAAM,CAAC8B,MAAP,CAAgB,KAAI,CAACvC,YAArB,EAAmC3B,KAAnC,EA3Bc;;EA8BdR,MAAAA,KAAK,CAACwE,KAAD,CAAL;EAAA,OAEGlE,IAFH,CAEQ,YAAM;EACV,YAAI,CAACmB,YAAY,CAACJ,SAAb,EAAD,IAA6B,CAACY,aAAa,CAACH,QAAd,EAAlC,EAA4D;EAC1D,iBAAOiC,KAAK,EAAZ;EACD;EACF,OANH,EAOGzD,IAPH,CAOQ,YAAM;EACV,YAAIuC,WAAJ,EAAiB;EACfgB,UAAAA,MAAM,CAACrD,KAAD,CAAN;EACD,SAFD,MAEO;EACL2D,UAAAA,GAAG;EACJ;EACF,OAbH;EAcD,KA9CH;EA+CD,GAjFD,CAxDgD;;;EA4IhDA,EAAAA,GAAG;EACJ,CA1JH;;EClDA;MAEaQ,aAAb;EAME,2BAAc;EACZ,SAAKC,KAAL,GAAa,EAAb;EACA,SAAKC,YAAL,GAAoB,CAApB;;EAEA,SAAKC,QAAL,GAAgB,UAAC9L,QAAD,EAA0B;EACxCA,MAAAA,QAAQ;EACT,KAFD;;EAIA,SAAK+L,aAAL,GAAqB,UAAC/L,QAAD,EAA0B;EAC7CA,MAAAA,QAAQ;EACT,KAFD;EAGD;;EAjBH;;EAAA,SAmBEgM,KAnBF,GAmBE,eAAShM,QAAT,EAA+B;EAC7B,QAAI8F,MAAJ;EACA,SAAK+F,YAAL;;EACA,QAAI;EACF/F,MAAAA,MAAM,GAAG9F,QAAQ,EAAjB;EACD,KAFD,SAEU;EACR,WAAK6L,YAAL;;EACA,UAAI,CAAC,KAAKA,YAAV,EAAwB;EACtB,aAAKI,KAAL;EACD;EACF;;EACD,WAAOnG,MAAP;EACD,GA/BH;;EAAA,SAiCEoG,QAjCF,GAiCE,kBAASlM,QAAT,EAAyC;EAAA;;EACvC,QAAI,KAAK6L,YAAT,EAAuB;EACrB,WAAKD,KAAL,CAAW1L,IAAX,CAAgBF,QAAhB;EACD,KAFD,MAEO;EACLqH,MAAAA,iBAAiB,CAAC,YAAM;EACtB,QAAA,KAAI,CAACyE,QAAL,CAAc9L,QAAd;EACD,OAFgB,CAAjB;EAGD;EACF;EAED;;;EA3CF;;EAAA,SA8CEmM,UA9CF,GA8CE,oBAA+BnM,QAA/B,EAA+C;EAAA;;EAC7C,WAAQ,YAAoB;EAAA,wCAAhBoM,IAAgB;EAAhBA,QAAAA,IAAgB;EAAA;;EAC1B,MAAA,MAAI,CAACF,QAAL,CAAc,YAAM;EAClBlM,QAAAA,QAAQ,MAAR,SAAYoM,IAAZ;EACD,OAFD;EAGD,KAJD;EAKD,GApDH;;EAAA,SAsDEH,KAtDF,GAsDE,iBAAc;EAAA;;EACZ,QAAML,KAAK,GAAG,KAAKA,KAAnB;EACA,SAAKA,KAAL,GAAa,EAAb;;EACA,QAAIA,KAAK,CAACpL,MAAV,EAAkB;EAChB6G,MAAAA,iBAAiB,CAAC,YAAM;EACtB,QAAA,MAAI,CAAC0E,aAAL,CAAmB,YAAM;EACvBH,UAAAA,KAAK,CAACxD,OAAN,CAAc,UAAApI,QAAQ,EAAI;EACxB,YAAA,MAAI,CAAC8L,QAAL,CAAc9L,QAAd;EACD,WAFD;EAGD,SAJD;EAKD,OANgB,CAAjB;EAOD;EACF;EAED;;;;EApEF;;EAAA,SAwEEqM,iBAxEF,GAwEE,2BAAkBhB,EAAlB,EAAsC;EACpC,SAAKS,QAAL,GAAgBT,EAAhB;EACD;EAED;;;;EA5EF;;EAAA,SAgFEiB,sBAhFF,GAgFE,gCAAuBjB,EAAvB,EAAgD;EAC9C,SAAKU,aAAL,GAAqBV,EAArB;EACD,GAlFH;;EAAA;EAAA;;MAuFakB,aAAa,GAAG,IAAIZ,aAAJ;;ECnG7B;EAUA;EAEA,IAAIa,MAAc,GAAGC,OAArB;EAEO,SAASC,SAAT,GAA6B;EAClC,SAAOF,MAAP;EACD;EAEM,SAASG,SAAT,CAAmBC,SAAnB,EAAsC;EAC3CJ,EAAAA,MAAM,GAAGI,SAAT;EACD;;ECyHD;MAEaC,KAAb;EAwBE,iBAAYjD,MAAZ,EAAyE;EACvE,SAAKkD,mBAAL,GAA2B,KAA3B;EACA,SAAKC,YAAL,GAAoB,KAApB;EACA,SAAKC,cAAL,GAAsBpD,MAAM,CAACoD,cAA7B;EACA,SAAKC,UAAL,CAAgBrD,MAAM,CAACpF,OAAvB;EACA,SAAK0I,SAAL,GAAiB,EAAjB;EACA,SAAKC,KAAL,GAAavD,MAAM,CAACuD,KAApB;EACA,SAAK/J,QAAL,GAAgBwG,MAAM,CAACxG,QAAvB;EACA,SAAKkB,SAAL,GAAiBsF,MAAM,CAACtF,SAAxB;EACA,SAAK8I,YAAL,GAAoBxD,MAAM,CAAC5E,KAAP,IAAgB,KAAKqI,eAAL,CAAqB,KAAK7I,OAA1B,CAApC;EACA,SAAKQ,KAAL,GAAa,KAAKoI,YAAlB;EACA,SAAKE,IAAL,GAAY1D,MAAM,CAAC0D,IAAnB;EACA,SAAKC,UAAL;EACD;;EArCH;;EAAA,SAuCUN,UAvCV,GAuCE,oBACEzI,OADF,EAEQ;EAAA;;EACN,SAAKA,OAAL,gBAAoB,KAAKwI,cAAzB,EAA4CxI,OAA5C;EAEA,SAAK8I,IAAL,GAAY9I,OAAZ,oBAAYA,OAAO,CAAE8I,IAArB,CAHM;;EAMN,SAAKE,SAAL,GAAiB7K,IAAI,CAACC,GAAL,CACf,KAAK4K,SAAL,IAAkB,CADH,2BAEf,KAAKhJ,OAAL,CAAagJ,SAFE,oCAEW,IAAI,EAAJ,GAAS,IAFpB,CAAjB;EAID,GAnDH;;EAAA,SAqDEC,iBArDF,GAqDE,2BACEjJ,OADF,EAEQ;EACN,SAAKwI,cAAL,GAAsBxI,OAAtB;EACD,GAzDH;;EAAA,SA2DU+I,UA3DV,GA2DE,sBAA2B;EAAA;;EACzB,SAAKG,cAAL;;EAEA,QAAIjM,cAAc,CAAC,KAAK+L,SAAN,CAAlB,EAAoC;EAClC,WAAKG,SAAL,GAAiBvG,UAAU,CAAC,YAAM;EAChC,QAAA,KAAI,CAACwG,cAAL;EACD,OAF0B,EAExB,KAAKJ,SAFmB,CAA3B;EAGD;EACF,GAnEH;;EAAA,SAqEUE,cArEV,GAqEE,0BAAyB;EACvBG,IAAAA,YAAY,CAAC,KAAKF,SAAN,CAAZ;EACA,SAAKA,SAAL,GAAiB1N,SAAjB;EACD,GAxEH;;EAAA,SA0EU2N,cA1EV,GA0EE,0BAAyB;EACvB,QAAI,CAAC,KAAKV,SAAL,CAAe1M,MAApB,EAA4B;EAC1B,UAAI,KAAKwE,KAAL,CAAWJ,UAAf,EAA2B;EACzB,YAAI,KAAKmI,YAAT,EAAuB;EACrB,eAAKQ,UAAL;EACD;EACF,OAJD,MAIO;EACL,aAAKJ,KAAL,CAAWW,MAAX,CAAkB,IAAlB;EACD;EACF;EACF,GApFH;;EAAA,SAsFEC,OAtFF,GAsFE,iBACExM,OADF,EAEEiD,OAFF,EAGS;EAAA;;EACP,QAAMwJ,QAAQ,GAAG,KAAKhJ,KAAL,CAAWiJ,IAA5B,CADO;;EAIP,QAAIA,IAAI,GAAG3M,gBAAgB,CAACC,OAAD,EAAUyM,QAAV,CAA3B,CAJO;;EAOP,iCAAI,sBAAKxJ,OAAL,EAAa0J,WAAjB,qBAAI,0CAA2BF,QAA3B,EAAqCC,IAArC,CAAJ,EAAgD;EAC9CA,MAAAA,IAAI,GAAGD,QAAP;EACD,KAFD,MAEO,IAAI,KAAKxJ,OAAL,CAAa2J,iBAAb,KAAmC,KAAvC,EAA8C;EACnD;EACAF,MAAAA,IAAI,GAAG9H,gBAAgB,CAAC6H,QAAD,EAAWC,IAAX,CAAvB;EACD,KAZM;;;EAeP,SAAKG,QAAL,CAAc;EACZH,MAAAA,IAAI,EAAJA,IADY;EAEZI,MAAAA,IAAI,EAAE,SAFM;EAGZC,MAAAA,aAAa,EAAE9J,OAAF,oBAAEA,OAAO,CAAE/B;EAHZ,KAAd;EAMA,WAAOwL,IAAP;EACD,GA/GH;;EAAA,SAiHEM,QAjHF,GAiHE,kBACEvJ,KADF,EAEEwJ,eAFF,EAGQ;EACN,SAAKJ,QAAL,CAAc;EAAEC,MAAAA,IAAI,EAAE,UAAR;EAAoBrJ,MAAAA,KAAK,EAALA,KAApB;EAA2BwJ,MAAAA,eAAe,EAAfA;EAA3B,KAAd;EACD,GAtHH;;EAAA,SAwHElF,MAxHF,GAwHE,gBAAO9E,OAAP,EAA+C;EAAA;;EAC7C,QAAMiG,OAAO,GAAG,KAAKA,OAArB;EACA,0BAAKgE,OAAL,mCAAcnF,MAAd,CAAqB9E,OAArB;EACA,WAAOiG,OAAO,GAAGA,OAAO,CAACnD,IAAR,CAAajG,IAAb,EAAmBkG,KAAnB,CAAyBlG,IAAzB,CAAH,GAAoC6F,OAAO,CAACC,OAAR,EAAlD;EACD,GA5HH;;EAAA,SA8HEuH,OA9HF,GA8HE,mBAAgB;EACd,SAAKhB,cAAL;EACA,SAAKpE,MAAL,CAAY;EAAEG,MAAAA,MAAM,EAAE;EAAV,KAAZ;EACD,GAjIH;;EAAA,SAmIEkF,KAnIF,GAmIE,iBAAc;EACZ,SAAKD,OAAL;EACA,SAAKH,QAAL,CAAc,KAAKnB,YAAnB;EACD,GAtIH;;EAAA,SAwIEtJ,QAxIF,GAwIE,oBAAoB;EAClB,WAAO,KAAKoJ,SAAL,CAAehH,IAAf,CAAoB,UAAA0I,QAAQ;EAAA,aAAIA,QAAQ,CAACpK,OAAT,CAAiBqK,OAAjB,KAA6B,KAAjC;EAAA,KAA5B,CAAP;EACD,GA1IH;;EAAA,SA4IEjK,UA5IF,GA4IE,sBAAsB;EACpB,WAAO,KAAKI,KAAL,CAAWJ,UAAlB;EACD,GA9IH;;EAAA,SAgJED,OAhJF,GAgJE,mBAAmB;EACjB,WACE,KAAKK,KAAL,CAAW8J,aAAX,IACA,CAAC,KAAK9J,KAAL,CAAWsJ,aADZ,IAEA,KAAKpB,SAAL,CAAehH,IAAf,CAAoB,UAAA0I,QAAQ;EAAA,aAAIA,QAAQ,CAACG,gBAAT,GAA4BpK,OAAhC;EAAA,KAA5B,CAHF;EAKD,GAtJH;;EAAA,SAwJEqK,aAxJF,GAwJE,uBAActM,SAAd,EAAsC;EAAA,QAAxBA,SAAwB;EAAxBA,MAAAA,SAAwB,GAAZ,CAAY;EAAA;;EACpC,WACE,KAAKsC,KAAL,CAAW8J,aAAX,IACA,CAAC,KAAK9J,KAAL,CAAWsJ,aADZ,IAEA,CAAC9L,cAAc,CAAC,KAAKwC,KAAL,CAAWsJ,aAAZ,EAA2B5L,SAA3B,CAHjB;EAKD,GA9JH;;EAAA,SAgKEmF,OAhKF,GAgKE,mBAAgB;EAAA;;EACd,QAAM+G,QAAQ,GAAG,KAAK1B,SAAL,CAAe+B,IAAf,CAAoB,UAAA5O,CAAC;EAAA,aAAIA,CAAC,CAAC6O,wBAAF,EAAJ;EAAA,KAArB,CAAjB;;EAEA,QAAIN,QAAJ,EAAc;EACZA,MAAAA,QAAQ,CAACO,OAAT;EACD,KALa;;;EAQd,2BAAKV,OAAL,oCAAcpE,QAAd;EACD,GAzKH;;EAAA,SA2KE1B,QA3KF,GA2KE,oBAAiB;EAAA;;EACf,QAAMiG,QAAQ,GAAG,KAAK1B,SAAL,CAAe+B,IAAf,CAAoB,UAAA5O,CAAC;EAAA,aAAIA,CAAC,CAAC+O,sBAAF,EAAJ;EAAA,KAArB,CAAjB;;EAEA,QAAIR,QAAJ,EAAc;EACZA,MAAAA,QAAQ,CAACO,OAAT;EACD,KALc;;;EAQf,2BAAKV,OAAL,oCAAcpE,QAAd;EACD,GApLH;;EAAA,SAsLEgF,WAtLF,GAsLE,qBAAYT,QAAZ,EAAoE;EAClE,QAAI,KAAK1B,SAAL,CAAehL,OAAf,CAAuB0M,QAAvB,MAAqC,CAAC,CAA1C,EAA6C;EAC3C,WAAK1B,SAAL,CAAehN,IAAf,CAAoB0O,QAApB;EACA,WAAK7B,YAAL,GAAoB,IAApB,CAF2C;;EAK3C,WAAKW,cAAL;EAEA,WAAKP,KAAL,CAAWmC,MAAX,CAAkB;EAAEjB,QAAAA,IAAI,EAAE,eAAR;EAAyBpK,QAAAA,KAAK,EAAE,IAAhC;EAAsC2K,QAAAA,QAAQ,EAARA;EAAtC,OAAlB;EACD;EACF,GAhMH;;EAAA,SAkMEW,cAlMF,GAkME,wBAAeX,QAAf,EAAuE;EACrE,QAAI,KAAK1B,SAAL,CAAehL,OAAf,CAAuB0M,QAAvB,MAAqC,CAAC,CAA1C,EAA6C;EAC3C,WAAK1B,SAAL,GAAiB,KAAKA,SAAL,CAAe9M,MAAf,CAAsB,UAAAC,CAAC;EAAA,eAAIA,CAAC,KAAKuO,QAAV;EAAA,OAAvB,CAAjB;;EAEA,UAAI,CAAC,KAAK1B,SAAL,CAAe1M,MAApB,EAA4B;EAC1B;EACA;EACA,YAAI,KAAKiO,OAAT,EAAkB;EAChB,cAAI,KAAKA,OAAL,CAAajE,qBAAb,IAAsC,KAAKsC,mBAA/C,EAAoE;EAClE,iBAAK2B,OAAL,CAAanF,MAAb,CAAoB;EAAEE,cAAAA,MAAM,EAAE;EAAV,aAApB;EACD,WAFD,MAEO;EACL,iBAAKiF,OAAL,CAAa5E,WAAb;EACD;EACF;;EAED,YAAI,KAAK2D,SAAT,EAAoB;EAClB,eAAKD,UAAL;EACD,SAFD,MAEO;EACL,eAAKJ,KAAL,CAAWW,MAAX,CAAkB,IAAlB;EACD;EACF;;EAED,WAAKX,KAAL,CAAWmC,MAAX,CAAkB;EAAEjB,QAAAA,IAAI,EAAE,iBAAR;EAA2BpK,QAAAA,KAAK,EAAE,IAAlC;EAAwC2K,QAAAA,QAAQ,EAARA;EAAxC,OAAlB;EACD;EACF,GA1NH;;EAAA,SA4NEY,iBA5NF,GA4NE,6BAA4B;EAC1B,WAAO,KAAKtC,SAAL,CAAe1M,MAAtB;EACD,GA9NH;;EAAA,SAgOEiP,UAhOF,GAgOE,sBAAmB;EACjB,QAAI,CAAC,KAAKzK,KAAL,CAAW8J,aAAhB,EAA+B;EAC7B,WAAKV,QAAL,CAAc;EAAEC,QAAAA,IAAI,EAAE;EAAR,OAAd;EACD;EACF,GApOH;;EAAA,SAsOEqB,KAtOF,GAsOE,eACElL,OADF,EAEEmL,YAFF,EAGkB;EAAA;EAAA;EAAA;EAAA;;EAChB,QAAI,KAAK3K,KAAL,CAAWJ,UAAf,EAA2B;EACzB,UAAI,KAAKI,KAAL,CAAWsJ,aAAX,KAA4BqB,YAA5B,oBAA4BA,YAAY,CAAEC,aAA1C,CAAJ,EAA6D;EAC3D;EACA,aAAKtG,MAAL,CAAY;EAAEG,UAAAA,MAAM,EAAE;EAAV,SAAZ;EACD,OAHD,MAGO,IAAI,KAAKgB,OAAT,EAAkB;EAAA;;EACvB;EACA,+BAAKgE,OAAL,oCAAcrE,aAAd,GAFuB;;EAIvB,eAAO,KAAKK,OAAZ;EACD;EACF,KAXe;;;EAchB,QAAIjG,OAAJ,EAAa;EACX,WAAKyI,UAAL,CAAgBzI,OAAhB;EACD,KAhBe;EAmBhB;;;EACA,QAAI,CAAC,KAAKA,OAAL,CAAanB,OAAlB,EAA2B;EACzB,UAAMuL,QAAQ,GAAG,KAAK1B,SAAL,CAAe+B,IAAf,CAAoB,UAAA5O,CAAC;EAAA,eAAIA,CAAC,CAACmE,OAAF,CAAUnB,OAAd;EAAA,OAArB,CAAjB;;EACA,UAAIuL,QAAJ,EAAc;EACZ,aAAK3B,UAAL,CAAgB2B,QAAQ,CAACpK,OAAzB;EACD;EACF;;EAED,QAAMpB,QAAQ,GAAGxB,mBAAmB,CAAC,KAAKwB,QAAN,CAApC;EACA,QAAMyM,eAAe,GAAGpI,kBAAkB,EAA1C,CA5BgB;;EA+BhB,QAAMqI,cAA+C,GAAG;EACtD1M,MAAAA,QAAQ,EAARA,QADsD;EAEtD2M,MAAAA,SAAS,EAAE9P,SAF2C;EAGtDqN,MAAAA,IAAI,EAAE,KAAKA;EAH2C,KAAxD;EAMA9N,IAAAA,MAAM,CAACwQ,cAAP,CAAsBF,cAAtB,EAAsC,QAAtC,EAAgD;EAC9CG,MAAAA,UAAU,EAAE,IADkC;EAE9CC,MAAAA,GAAG,EAAE,eAAM;EACT,YAAIL,eAAJ,EAAqB;EACnB,UAAA,MAAI,CAAC/C,mBAAL,GAA2B,IAA3B;EACA,iBAAO+C,eAAe,CAACM,MAAvB;EACD;;EACD,eAAOlQ,SAAP;EACD;EAR6C,KAAhD,EArCgB;;EAiDhB,QAAMmQ,OAAO,GAAG,SAAVA,OAAU,GAAM;EACpB,UAAI,CAAC,MAAI,CAAC5L,OAAL,CAAanB,OAAlB,EAA2B;EACzB,eAAO6D,OAAO,CAAC2D,MAAR,CAAe,iBAAf,CAAP;EACD;;EACD,MAAA,MAAI,CAACiC,mBAAL,GAA2B,KAA3B;EACA,aAAO,MAAI,CAACtI,OAAL,CAAanB,OAAb,CAAqByM,cAArB,CAAP;EACD,KAND,CAjDgB;;;EA0DhB,QAAMO,OAA6D,GAAG;EACpEV,MAAAA,YAAY,EAAZA,YADoE;EAEpEnL,MAAAA,OAAO,EAAE,KAAKA,OAFsD;EAGpEpB,MAAAA,QAAQ,EAAEA,QAH0D;EAIpE4B,MAAAA,KAAK,EAAE,KAAKA,KAJwD;EAKpEoL,MAAAA,OAAO,EAAPA,OALoE;EAMpE9C,MAAAA,IAAI,EAAE,KAAKA;EANyD,KAAtE;;EASA,iCAAI,KAAK9I,OAAL,CAAa8L,QAAjB,qBAAI,sBAAuBC,OAA3B,EAAoC;EAAA;;EAClC,qCAAK/L,OAAL,CAAa8L,QAAb,4CAAuBC,OAAvB,CAA+BF,OAA/B;EACD,KArEe;;;EAwEhB,SAAKG,WAAL,GAAmB,KAAKxL,KAAxB,CAxEgB;;EA2EhB,QACE,CAAC,KAAKA,KAAL,CAAWJ,UAAZ,IACA,KAAKI,KAAL,CAAWyL,SAAX,+BAAyBJ,OAAO,CAACV,YAAjC,qBAAyB,sBAAsBrC,IAA/C,CAFF,EAGE;EAAA;;EACA,WAAKc,QAAL,CAAc;EAAEC,QAAAA,IAAI,EAAE,OAAR;EAAiBf,QAAAA,IAAI,4BAAE+C,OAAO,CAACV,YAAV,qBAAE,uBAAsBrC;EAA7C,OAAd;EACD,KAhFe;;;EAmFhB,SAAKmB,OAAL,GAAe,IAAI9E,OAAJ,CAAY;EACzB0B,MAAAA,EAAE,EAAEgF,OAAO,CAACD,OADa;EAEzBlG,MAAAA,KAAK,EAAE2F,eAAF,6CAAEA,eAAe,CAAE3F,KAAnB,qBAAE,sBAAwBwG,IAAxB,CAA6Bb,eAA7B,CAFkB;EAGzBjF,MAAAA,SAAS,EAAE,mBAAAqD,IAAI,EAAI;EACjB,QAAA,MAAI,CAACF,OAAL,CAAaE,IAAb,EADiB;;;EAIjB,QAAA,MAAI,CAACd,KAAL,CAAWvD,MAAX,CAAkBgB,SAAlB,oBAAA,MAAI,CAACuC,KAAL,CAAWvD,MAAX,CAAkBgB,SAAlB,CAA8BqD,IAA9B,EAAoC,MAApC,EAJiB;;EAOjB,YAAI,MAAI,CAACT,SAAL,KAAmB,CAAvB,EAA0B;EACxB,UAAA,MAAI,CAACI,cAAL;EACD;EACF,OAbwB;EAczB9C,MAAAA,OAAO,EAAE,iBAACtD,KAAD,EAA0C;EACjD;EACA,YAAI,EAAEkC,gBAAgB,CAAClC,KAAD,CAAhB,IAA2BA,KAAK,CAACiC,MAAnC,CAAJ,EAAgD;EAC9C,UAAA,MAAI,CAAC2E,QAAL,CAAc;EACZC,YAAAA,IAAI,EAAE,OADM;EAEZ7G,YAAAA,KAAK,EAAEA;EAFK,WAAd;EAID;;EAED,YAAI,CAACkC,gBAAgB,CAAClC,KAAD,CAArB,EAA8B;EAC5B;EACA,UAAA,MAAI,CAAC2F,KAAL,CAAWvD,MAAX,CAAkBkB,OAAlB,oBAAA,MAAI,CAACqC,KAAL,CAAWvD,MAAX,CAAkBkB,OAAlB,CAA4BtD,KAA5B,EAAmC,MAAnC,EAF4B;;EAK5BkF,UAAAA,SAAS,GAAGlF,KAAZ,CAAkBA,KAAlB;EACD,SAfgD;;;EAkBjD,YAAI,MAAI,CAACgG,SAAL,KAAmB,CAAvB,EAA0B;EACxB,UAAA,MAAI,CAACI,cAAL;EACD;EACF,OAnCwB;EAoCzBlC,MAAAA,MAAM,EAAE,kBAAM;EACZ,QAAA,MAAI,CAAC0C,QAAL,CAAc;EAAEC,UAAAA,IAAI,EAAE;EAAR,SAAd;EACD,OAtCwB;EAuCzBpD,MAAAA,OAAO,EAAE,mBAAM;EACb,QAAA,MAAI,CAACmD,QAAL,CAAc;EAAEC,UAAAA,IAAI,EAAE;EAAR,SAAd;EACD,OAzCwB;EA0CzBnD,MAAAA,UAAU,EAAE,sBAAM;EAChB,QAAA,MAAI,CAACkD,QAAL,CAAc;EAAEC,UAAAA,IAAI,EAAE;EAAR,SAAd;EACD,OA5CwB;EA6CzB/C,MAAAA,KAAK,EAAE+E,OAAO,CAAC7L,OAAR,CAAgB8G,KA7CE;EA8CzBC,MAAAA,UAAU,EAAE8E,OAAO,CAAC7L,OAAR,CAAgB+G;EA9CH,KAAZ,CAAf;EAiDA,SAAKd,OAAL,GAAe,KAAKgE,OAAL,CAAahE,OAA5B;EAEA,WAAO,KAAKA,OAAZ;EACD,GAhXH;;EAAA,SAkXU2D,QAlXV,GAkXE,kBAAiBuC,MAAjB,EAAsD;EAAA;;EACpD,SAAK3L,KAAL,GAAa,KAAK4L,OAAL,CAAa,KAAK5L,KAAlB,EAAyB2L,MAAzB,CAAb;EAEApE,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB,MAAA,MAAI,CAACkB,SAAL,CAAe9E,OAAf,CAAuB,UAAAwG,QAAQ,EAAI;EACjCA,QAAAA,QAAQ,CAACiC,aAAT,CAAuBF,MAAvB;EACD,OAFD;;EAIA,MAAA,MAAI,CAACxD,KAAL,CAAWmC,MAAX,CAAkB;EAAErL,QAAAA,KAAK,EAAE,MAAT;EAAeoK,QAAAA,IAAI,EAAE,cAArB;EAAqCsC,QAAAA,MAAM,EAANA;EAArC,OAAlB;EACD,KAND;EAOD,GA5XH;;EAAA,SA8XYtD,eA9XZ,GA8XE,yBACE7I,OADF,EAE6B;EAC3B,QAAMyJ,IAAI,GACR,OAAOzJ,OAAO,CAACsM,WAAf,KAA+B,UAA/B,GACKtM,OAAO,CAACsM,WAAT,EADJ,GAEItM,OAAO,CAACsM,WAHd;EAKA,QAAMC,cAAc,GAAG,OAAOvM,OAAO,CAACsM,WAAf,KAA+B,WAAtD;EAEA,QAAME,oBAAoB,GAAGD,cAAc,GACvC,OAAOvM,OAAO,CAACwM,oBAAf,KAAwC,UAAxC,GACGxM,OAAO,CAACwM,oBAAT,EADF,GAEExM,OAAO,CAACwM,oBAH6B,GAIvC,CAJJ;EAMA,QAAMC,OAAO,GAAG,OAAOhD,IAAP,KAAgB,WAAhC;EAEA,WAAO;EACLA,MAAAA,IAAI,EAAJA,IADK;EAELiD,MAAAA,eAAe,EAAE,CAFZ;EAGL5C,MAAAA,aAAa,EAAE2C,OAAO,GAAGD,oBAAH,WAAGA,oBAAH,GAA2BnO,IAAI,CAACC,GAAL,EAA3B,GAAwC,CAHzD;EAIL0E,MAAAA,KAAK,EAAE,IAJF;EAKL2J,MAAAA,gBAAgB,EAAE,CALb;EAMLC,MAAAA,cAAc,EAAE,CANX;EAOLC,MAAAA,iBAAiB,EAAE,CAPd;EAQLZ,MAAAA,SAAS,EAAE,IARN;EASL7L,MAAAA,UAAU,EAAE,KATP;EAULkK,MAAAA,aAAa,EAAE,KAVV;EAWLxE,MAAAA,QAAQ,EAAE,KAXL;EAYLrF,MAAAA,MAAM,EAAEgM,OAAO,GAAG,SAAH,GAAe;EAZzB,KAAP;EAcD,GA9ZH;;EAAA,SAgaYL,OAhaZ,GAgaE,iBACE5L,KADF,EAEE2L,MAFF,EAG6B;EAAA;;EAC3B,YAAQA,MAAM,CAACtC,IAAf;EACE,WAAK,QAAL;EACE,4BACKrJ,KADL;EAEEqM,UAAAA,iBAAiB,EAAErM,KAAK,CAACqM,iBAAN,GAA0B;EAF/C;;EAIF,WAAK,OAAL;EACE,4BACKrM,KADL;EAEEsF,UAAAA,QAAQ,EAAE;EAFZ;;EAIF,WAAK,UAAL;EACE,4BACKtF,KADL;EAEEsF,UAAAA,QAAQ,EAAE;EAFZ;;EAIF,WAAK,OAAL;EACE,4BACKtF,KADL;EAEEqM,UAAAA,iBAAiB,EAAE,CAFrB;EAGEZ,UAAAA,SAAS,kBAAEE,MAAM,CAACrD,IAAT,2BAAiB,IAH5B;EAIE1I,UAAAA,UAAU,EAAE,IAJd;EAKE0F,UAAAA,QAAQ,EAAE;EALZ,WAMM,CAACtF,KAAK,CAACsJ,aAAP,IAAwB;EAC1B9G,UAAAA,KAAK,EAAE,IADmB;EAE1BvC,UAAAA,MAAM,EAAE;EAFkB,SAN9B;;EAWF,WAAK,SAAL;EACE,4BACKD,KADL;EAEEiJ,UAAAA,IAAI,EAAE0C,MAAM,CAAC1C,IAFf;EAGEiD,UAAAA,eAAe,EAAElM,KAAK,CAACkM,eAAN,GAAwB,CAH3C;EAIE5C,UAAAA,aAAa,2BAAEqC,MAAM,CAACrC,aAAT,oCAA0BzL,IAAI,CAACC,GAAL,EAJzC;EAKE0E,UAAAA,KAAK,EAAE,IALT;EAME6J,UAAAA,iBAAiB,EAAE,CANrB;EAOEzM,UAAAA,UAAU,EAAE,KAPd;EAQEkK,UAAAA,aAAa,EAAE,KARjB;EASExE,UAAAA,QAAQ,EAAE,KATZ;EAUErF,UAAAA,MAAM,EAAE;EAVV;;EAYF,WAAK,OAAL;EACE,YAAMuC,KAAK,GAAGmJ,MAAM,CAACnJ,KAArB;;EAEA,YAAIkC,gBAAgB,CAAClC,KAAD,CAAhB,IAA2BA,KAAK,CAACgC,MAAjC,IAA2C,KAAKgH,WAApD,EAAiE;EAC/D,8BAAY,KAAKA,WAAjB;EACD;;EAED,4BACKxL,KADL;EAEEwC,UAAAA,KAAK,EAAEA,KAFT;EAGE2J,UAAAA,gBAAgB,EAAEnM,KAAK,CAACmM,gBAAN,GAAyB,CAH7C;EAIEC,UAAAA,cAAc,EAAEvO,IAAI,CAACC,GAAL,EAJlB;EAKEuO,UAAAA,iBAAiB,EAAErM,KAAK,CAACqM,iBAAN,GAA0B,CAL/C;EAMEzM,UAAAA,UAAU,EAAE,KANd;EAOE0F,UAAAA,QAAQ,EAAE,KAPZ;EAQErF,UAAAA,MAAM,EAAE;EARV;;EAUF,WAAK,YAAL;EACE,4BACKD,KADL;EAEE8J,UAAAA,aAAa,EAAE;EAFjB;;EAIF,WAAK,UAAL;EACE,4BACK9J,KADL,EAEK2L,MAAM,CAAC3L,KAFZ;;EAIF;EACE,eAAOA,KAAP;EArEJ;EAuED,GA3eH;;EAAA;EAAA;;EC5EA;MAEasM,UAAb;EAAA;;EAME,sBAAY1H,MAAZ,EAAuC;EAAA;;EACrC;EACA,UAAKA,MAAL,GAAcA,MAAM,IAAI,EAAxB;EACA,UAAK2H,OAAL,GAAe,EAAf;EACA,UAAKC,UAAL,GAAkB,EAAlB;EAJqC;EAKtC;;EAXH;;EAAA,SAaEC,KAbF,GAaE,eACEC,MADF,EAEElN,OAFF,EAGEQ,KAHF,EAIiD;EAAA;;EAC/C,QAAM5B,QAAQ,GAAGoB,OAAO,CAACpB,QAAzB;EACA,QAAMkB,SAAS,yBACbE,OAAO,CAACF,SADK,iCACQC,qBAAqB,CAACnB,QAAD,EAAWoB,OAAX,CAD5C;EAEA,QAAIP,KAAK,GAAG,KAAKiM,GAAL,CAAiD5L,SAAjD,CAAZ;;EAEA,QAAI,CAACL,KAAL,EAAY;EACVA,MAAAA,KAAK,GAAG,IAAI4I,KAAJ,CAAU;EAChBM,QAAAA,KAAK,EAAE,IADS;EAEhB/J,QAAAA,QAAQ,EAARA,QAFgB;EAGhBkB,QAAAA,SAAS,EAATA,SAHgB;EAIhBE,QAAAA,OAAO,EAAEkN,MAAM,CAACC,mBAAP,CAA2BnN,OAA3B,CAJO;EAKhBQ,QAAAA,KAAK,EAALA,KALgB;EAMhBgI,QAAAA,cAAc,EAAE0E,MAAM,CAACE,gBAAP,CAAwBxO,QAAxB,CANA;EAOhBkK,QAAAA,IAAI,EAAE9I,OAAO,CAAC8I;EAPE,OAAV,CAAR;EASA,WAAKuE,GAAL,CAAS5N,KAAT;EACD;;EAED,WAAOA,KAAP;EACD,GArCH;;EAAA,SAuCE4N,GAvCF,GAuCE,aAAI5N,KAAJ,EAA4C;EAC1C,QAAI,CAAC,KAAKuN,UAAL,CAAgBvN,KAAK,CAACK,SAAtB,CAAL,EAAuC;EACrC,WAAKkN,UAAL,CAAgBvN,KAAK,CAACK,SAAtB,IAAmCL,KAAnC;EACA,WAAKsN,OAAL,CAAarR,IAAb,CAAkB+D,KAAlB;EACA,WAAKqL,MAAL,CAAY;EACVjB,QAAAA,IAAI,EAAE,YADI;EAEVpK,QAAAA,KAAK,EAALA;EAFU,OAAZ;EAID;EACF,GAhDH;;EAAA,SAkDE6J,MAlDF,GAkDE,gBAAO7J,KAAP,EAA+C;EAC7C,QAAM6N,UAAU,GAAG,KAAKN,UAAL,CAAgBvN,KAAK,CAACK,SAAtB,CAAnB;;EAEA,QAAIwN,UAAJ,EAAgB;EACd7N,MAAAA,KAAK,CAACyK,OAAN;EAEA,WAAK6C,OAAL,GAAe,KAAKA,OAAL,CAAanR,MAAb,CAAoB,UAAAC,CAAC;EAAA,eAAIA,CAAC,KAAK4D,KAAV;EAAA,OAArB,CAAf;;EAEA,UAAI6N,UAAU,KAAK7N,KAAnB,EAA0B;EACxB,eAAO,KAAKuN,UAAL,CAAgBvN,KAAK,CAACK,SAAtB,CAAP;EACD;;EAED,WAAKgL,MAAL,CAAY;EAAEjB,QAAAA,IAAI,EAAE,cAAR;EAAwBpK,QAAAA,KAAK,EAALA;EAAxB,OAAZ;EACD;EACF,GAhEH;;EAAA,SAkEE8N,KAlEF,GAkEE,iBAAc;EAAA;;EACZxF,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB,MAAA,MAAI,CAACuF,OAAL,CAAanJ,OAAb,CAAqB,UAAAnE,KAAK,EAAI;EAC5B,QAAA,MAAI,CAAC6J,MAAL,CAAY7J,KAAZ;EACD,OAFD;EAGD,KAJD;EAKD,GAxEH;;EAAA,SA0EEiM,GA1EF,GA0EE,aAME5L,SANF,EAO4D;EAC1D,WAAO,KAAKkN,UAAL,CAAgBlN,SAAhB,CAAP;EACD,GAnFH;;EAAA,SAqFE0N,MArFF,GAqFE,kBAAkB;EAChB,WAAO,KAAKT,OAAZ;EACD,GAvFH;;EAAA,SAyFEtC,IAzFF,GAyFE,cACEjM,IADF,EAEEC,IAFF,EAGkD;EAAA,2BAC9BQ,eAAe,CAACT,IAAD,EAAOC,IAAP,CADe;EAAA,QACzCe,OADyC;;EAGhD,QAAI,OAAOA,OAAO,CAACE,KAAf,KAAyB,WAA7B,EAA0C;EACxCF,MAAAA,OAAO,CAACE,KAAR,GAAgB,IAAhB;EACD;;EAED,WAAO,KAAKqN,OAAL,CAAatC,IAAb,CAAkB,UAAAhL,KAAK;EAAA,aAAIF,UAAU,CAACC,OAAD,EAAUC,KAAV,CAAd;EAAA,KAAvB,CAAP;EACD,GApGH;;EAAA,SAyGEgO,OAzGF,GAyGE,iBAAQjP,IAAR,EAAwCC,IAAxC,EAAsE;EAAA,4BAClDQ,eAAe,CAACT,IAAD,EAAOC,IAAP,CADmC;EAAA,QAC7De,OAD6D;;EAEpE,WAAOxE,MAAM,CAACmG,IAAP,CAAY3B,OAAZ,EAAqBxD,MAArB,GAA8B,CAA9B,GACH,KAAK+Q,OAAL,CAAanR,MAAb,CAAoB,UAAA6D,KAAK;EAAA,aAAIF,UAAU,CAACC,OAAD,EAAUC,KAAV,CAAd;EAAA,KAAzB,CADG,GAEH,KAAKsN,OAFT;EAGD,GA9GH;;EAAA,SAgHEjC,MAhHF,GAgHE,gBAAO4C,KAAP,EAAqC;EAAA;;EACnC3F,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB,MAAA,MAAI,CAACnM,SAAL,CAAeuI,OAAf,CAAuB,UAAArI,QAAQ,EAAI;EACjCA,QAAAA,QAAQ,CAACmS,KAAD,CAAR;EACD,OAFD;EAGD,KAJD;EAKD,GAtHH;;EAAA,SAwHErK,OAxHF,GAwHE,mBAAgB;EAAA;;EACd0E,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB,MAAA,MAAI,CAACuF,OAAL,CAAanJ,OAAb,CAAqB,UAAAnE,KAAK,EAAI;EAC5BA,QAAAA,KAAK,CAAC4D,OAAN;EACD,OAFD;EAGD,KAJD;EAKD,GA9HH;;EAAA,SAgIEc,QAhIF,GAgIE,oBAAiB;EAAA;;EACf4D,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB,MAAA,MAAI,CAACuF,OAAL,CAAanJ,OAAb,CAAqB,UAAAnE,KAAK,EAAI;EAC5BA,QAAAA,KAAK,CAAC0E,QAAN;EACD,OAFD;EAGD,KAJD;EAKD,GAtIH;;EAAA;EAAA,EAAgC/I,YAAhC;;ECOA;MAEauS,QAAb;EAeE,oBAAYvI,MAAZ,EAAyE;EACvE,SAAKpF,OAAL,gBACKoF,MAAM,CAACoD,cADZ,EAEKpD,MAAM,CAACpF,OAFZ;EAIA,SAAK4N,UAAL,GAAkBxI,MAAM,CAACwI,UAAzB;EACA,SAAKC,aAAL,GAAqBzI,MAAM,CAACyI,aAA5B;EACA,SAAKnF,SAAL,GAAiB,EAAjB;EACA,SAAKlI,KAAL,GAAa4E,MAAM,CAAC5E,KAAP,IAAgBqI,eAAe,EAA5C;EACA,SAAKC,IAAL,GAAY1D,MAAM,CAAC0D,IAAnB;EACD;;EAzBH;;EAAA,SA2BEiB,QA3BF,GA2BE,kBAASvJ,KAAT,EAA0E;EACxE,SAAKoJ,QAAL,CAAc;EAAEC,MAAAA,IAAI,EAAE,UAAR;EAAoBrJ,MAAAA,KAAK,EAALA;EAApB,KAAd;EACD,GA7BH;;EAAA,SA+BEqK,WA/BF,GA+BE,qBAAYT,QAAZ,EAAkE;EAChE,QAAI,KAAK1B,SAAL,CAAehL,OAAf,CAAuB0M,QAAvB,MAAqC,CAAC,CAA1C,EAA6C;EAC3C,WAAK1B,SAAL,CAAehN,IAAf,CAAoB0O,QAApB;EACD;EACF,GAnCH;;EAAA,SAqCEW,cArCF,GAqCE,wBAAeX,QAAf,EAAqE;EACnE,SAAK1B,SAAL,GAAiB,KAAKA,SAAL,CAAe9M,MAAf,CAAsB,UAAAC,CAAC;EAAA,aAAIA,CAAC,KAAKuO,QAAV;EAAA,KAAvB,CAAjB;EACD,GAvCH;;EAAA,SAyCEtF,MAzCF,GAyCE,kBAAwB;EACtB,QAAI,KAAKmF,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAanF,MAAb;EACA,aAAO,KAAKmF,OAAL,CAAahE,OAAb,CAAqBnD,IAArB,CAA0BjG,IAA1B,EAAgCkG,KAAhC,CAAsClG,IAAtC,CAAP;EACD;;EACD,WAAO6F,OAAO,CAACC,OAAR,EAAP;EACD,GA/CH;;EAAA,SAiDEkD,QAjDF,GAiDE,qBAA2B;EACzB,QAAI,KAAKoE,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAapE,QAAb;EACA,aAAO,KAAKoE,OAAL,CAAahE,OAApB;EACD;;EACD,WAAO,KAAK6H,OAAL,EAAP;EACD,GAvDH;;EAAA,SAyDEA,OAzDF,GAyDE,mBAA0B;EAAA;;EACxB,QAAIrE,IAAJ;EAEA,QAAMsE,QAAQ,GAAG,KAAKvN,KAAL,CAAWC,MAAX,KAAsB,SAAvC;EAEA,QAAIwF,OAAO,GAAGvD,OAAO,CAACC,OAAR,EAAd;;EAEA,QAAI,CAACoL,QAAL,EAAe;EACb,WAAKnE,QAAL,CAAc;EAAEC,QAAAA,IAAI,EAAE,SAAR;EAAmBmE,QAAAA,SAAS,EAAE,KAAKhO,OAAL,CAAagO;EAA3C,OAAd;EACA/H,MAAAA,OAAO,GAAGA,OAAO,CACdnD,IADO,CACF,YAAM;EACV;EACA,QAAA,KAAI,CAAC+K,aAAL,CAAmBzI,MAAnB,CAA0B6I,QAA1B,oBAAA,KAAI,CAACJ,aAAL,CAAmBzI,MAAnB,CAA0B6I,QAA1B,CACE,KAAI,CAACzN,KAAL,CAAWwN,SADb,EAEE,KAFF;EAID,OAPO,EAQPlL,IARO,CAQF;EAAA,eAAM,KAAI,CAAC9C,OAAL,CAAaiO,QAAnB,oBAAM,KAAI,CAACjO,OAAL,CAAaiO,QAAb,CAAwB,KAAI,CAACzN,KAAL,CAAWwN,SAAnC,CAAN;EAAA,OARE,EASPlL,IATO,CASF,UAAA+I,OAAO,EAAI;EACf,YAAIA,OAAO,KAAK,KAAI,CAACrL,KAAL,CAAWqL,OAA3B,EAAoC;EAClC,UAAA,KAAI,CAACjC,QAAL,CAAc;EACZC,YAAAA,IAAI,EAAE,SADM;EAEZgC,YAAAA,OAAO,EAAPA,OAFY;EAGZmC,YAAAA,SAAS,EAAE,KAAI,CAACxN,KAAL,CAAWwN;EAHV,WAAd;EAKD;EACF,OAjBO,CAAV;EAkBD;;EAED,WAAO/H,OAAO,CACXnD,IADI,CACC;EAAA,aAAM,KAAI,CAACoL,eAAL,EAAN;EAAA,KADD,EAEJpL,IAFI,CAEC,UAAAxB,MAAM,EAAI;EACdmI,MAAAA,IAAI,GAAGnI,MAAP,CADc;;EAGd,MAAA,KAAI,CAACuM,aAAL,CAAmBzI,MAAnB,CAA0BgB,SAA1B,oBAAA,KAAI,CAACyH,aAAL,CAAmBzI,MAAnB,CAA0BgB,SAA1B,CACEqD,IADF,EAEE,KAAI,CAACjJ,KAAL,CAAWwN,SAFb,EAGE,KAAI,CAACxN,KAAL,CAAWqL,OAHb,EAIE,KAJF;EAMD,KAXI,EAYJ/I,IAZI,CAYC;EAAA,aACJ,KAAI,CAAC9C,OAAL,CAAaoG,SADT,oBACJ,KAAI,CAACpG,OAAL,CAAaoG,SAAb,CACEqD,IADF,EAEE,KAAI,CAACjJ,KAAL,CAAWwN,SAFb,EAGE,KAAI,CAACxN,KAAL,CAAWqL,OAHb,CADI;EAAA,KAZD,EAmBJ/I,IAnBI,CAmBC;EAAA,aACJ,KAAI,CAAC9C,OAAL,CAAamO,SADT,oBACJ,KAAI,CAACnO,OAAL,CAAamO,SAAb,CACE1E,IADF,EAEE,IAFF,EAGE,KAAI,CAACjJ,KAAL,CAAWwN,SAHb,EAIE,KAAI,CAACxN,KAAL,CAAWqL,OAJb,CADI;EAAA,KAnBD,EA2BJ/I,IA3BI,CA2BC,YAAM;EACV,MAAA,KAAI,CAAC8G,QAAL,CAAc;EAAEC,QAAAA,IAAI,EAAE,SAAR;EAAmBJ,QAAAA,IAAI,EAAJA;EAAnB,OAAd;;EACA,aAAOA,IAAP;EACD,KA9BI,EA+BJ1G,KA/BI,CA+BE,UAAAC,KAAK,EAAI;EACd;EACA,MAAA,KAAI,CAAC6K,aAAL,CAAmBzI,MAAnB,CAA0BkB,OAA1B,oBAAA,KAAI,CAACuH,aAAL,CAAmBzI,MAAnB,CAA0BkB,OAA1B,CACEtD,KADF,EAEE,KAAI,CAACxC,KAAL,CAAWwN,SAFb,EAGE,KAAI,CAACxN,KAAL,CAAWqL,OAHb,EAIE,KAJF,EAFc;;EAUd3D,MAAAA,SAAS,GAAGlF,KAAZ,CAAkBA,KAAlB;EAEA,aAAON,OAAO,CAACC,OAAR,GACJG,IADI,CACC;EAAA,eACJ,KAAI,CAAC9C,OAAL,CAAasG,OADT,oBACJ,KAAI,CAACtG,OAAL,CAAasG,OAAb,CACEtD,KADF,EAEE,KAAI,CAACxC,KAAL,CAAWwN,SAFb,EAGE,KAAI,CAACxN,KAAL,CAAWqL,OAHb,CADI;EAAA,OADD,EAQJ/I,IARI,CAQC;EAAA,eACJ,KAAI,CAAC9C,OAAL,CAAamO,SADT,oBACJ,KAAI,CAACnO,OAAL,CAAamO,SAAb,CACE1S,SADF,EAEEuH,KAFF,EAGE,KAAI,CAACxC,KAAL,CAAWwN,SAHb,EAIE,KAAI,CAACxN,KAAL,CAAWqL,OAJb,CADI;EAAA,OARD,EAgBJ/I,IAhBI,CAgBC,YAAM;EACV,QAAA,KAAI,CAAC8G,QAAL,CAAc;EAAEC,UAAAA,IAAI,EAAE,OAAR;EAAiB7G,UAAAA,KAAK,EAALA;EAAjB,SAAd;;EACA,cAAMA,KAAN;EACD,OAnBI,CAAP;EAoBD,KA/DI,CAAP;EAgED,GAtJH;;EAAA,SAwJUkL,eAxJV,GAwJE,2BAA0C;EAAA;EAAA;;EACxC,SAAKjE,OAAL,GAAe,IAAI9E,OAAJ,CAAY;EACzB0B,MAAAA,EAAE,EAAE,cAAM;EACR,YAAI,CAAC,MAAI,CAAC7G,OAAL,CAAahB,UAAlB,EAA8B;EAC5B,iBAAO0D,OAAO,CAAC2D,MAAR,CAAe,qBAAf,CAAP;EACD;;EACD,eAAO,MAAI,CAACrG,OAAL,CAAahB,UAAb,CAAwB,MAAI,CAACwB,KAAL,CAAWwN,SAAnC,CAAP;EACD,OANwB;EAOzB9G,MAAAA,MAAM,EAAE,kBAAM;EACZ,QAAA,MAAI,CAAC0C,QAAL,CAAc;EAAEC,UAAAA,IAAI,EAAE;EAAR,SAAd;EACD,OATwB;EAUzBpD,MAAAA,OAAO,EAAE,mBAAM;EACb,QAAA,MAAI,CAACmD,QAAL,CAAc;EAAEC,UAAAA,IAAI,EAAE;EAAR,SAAd;EACD,OAZwB;EAazBnD,MAAAA,UAAU,EAAE,sBAAM;EAChB,QAAA,MAAI,CAACkD,QAAL,CAAc;EAAEC,UAAAA,IAAI,EAAE;EAAR,SAAd;EACD,OAfwB;EAgBzB/C,MAAAA,KAAK,yBAAE,KAAK9G,OAAL,CAAa8G,KAAf,kCAAwB,CAhBJ;EAiBzBC,MAAAA,UAAU,EAAE,KAAK/G,OAAL,CAAa+G;EAjBA,KAAZ,CAAf;EAoBA,WAAO,KAAKkD,OAAL,CAAahE,OAApB;EACD,GA9KH;;EAAA,SAgLU2D,QAhLV,GAgLE,kBAAiBuC,MAAjB,EAA4E;EAAA;;EAC1E,SAAK3L,KAAL,GAAa4L,OAAO,CAAC,KAAK5L,KAAN,EAAa2L,MAAb,CAApB;EAEApE,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB,MAAA,MAAI,CAACkB,SAAL,CAAe9E,OAAf,CAAuB,UAAAwG,QAAQ,EAAI;EACjCA,QAAAA,QAAQ,CAACgE,gBAAT,CAA0BjC,MAA1B;EACD,OAFD;;EAGA,MAAA,MAAI,CAAC0B,aAAL,CAAmB/C,MAAnB,CAA0B,MAA1B;EACD,KALD;EAMD,GAzLH;;EAAA;EAAA;EA4LO,SAASjC,eAAT,GAKiD;EACtD,SAAO;EACLgD,IAAAA,OAAO,EAAEpQ,SADJ;EAELgO,IAAAA,IAAI,EAAEhO,SAFD;EAGLuH,IAAAA,KAAK,EAAE,IAHF;EAIL2B,IAAAA,YAAY,EAAE,CAJT;EAKLmB,IAAAA,QAAQ,EAAE,KALL;EAMLrF,IAAAA,MAAM,EAAE,MANH;EAOLuN,IAAAA,SAAS,EAAEvS;EAPN,GAAP;EASD;;EAED,SAAS2Q,OAAT,CACE5L,KADF,EAEE2L,MAFF,EAGsD;EACpD,UAAQA,MAAM,CAACtC,IAAf;EACE,SAAK,QAAL;EACE,0BACKrJ,KADL;EAEEmE,QAAAA,YAAY,EAAEnE,KAAK,CAACmE,YAAN,GAAqB;EAFrC;;EAIF,SAAK,OAAL;EACE,0BACKnE,KADL;EAEEsF,QAAAA,QAAQ,EAAE;EAFZ;;EAIF,SAAK,UAAL;EACE,0BACKtF,KADL;EAEEsF,QAAAA,QAAQ,EAAE;EAFZ;;EAIF,SAAK,SAAL;EACE,0BACKtF,KADL;EAEEqL,QAAAA,OAAO,EAAEM,MAAM,CAACN,OAFlB;EAGEpC,QAAAA,IAAI,EAAEhO,SAHR;EAIEuH,QAAAA,KAAK,EAAE,IAJT;EAKE8C,QAAAA,QAAQ,EAAE,KALZ;EAMErF,QAAAA,MAAM,EAAE,SANV;EAOEuN,QAAAA,SAAS,EAAE7B,MAAM,CAAC6B;EAPpB;;EASF,SAAK,SAAL;EACE,0BACKxN,KADL;EAEEiJ,QAAAA,IAAI,EAAE0C,MAAM,CAAC1C,IAFf;EAGEzG,QAAAA,KAAK,EAAE,IAHT;EAIEvC,QAAAA,MAAM,EAAE,SAJV;EAKEqF,QAAAA,QAAQ,EAAE;EALZ;;EAOF,SAAK,OAAL;EACE,0BACKtF,KADL;EAEEiJ,QAAAA,IAAI,EAAEhO,SAFR;EAGEuH,QAAAA,KAAK,EAAEmJ,MAAM,CAACnJ,KAHhB;EAIE2B,QAAAA,YAAY,EAAEnE,KAAK,CAACmE,YAAN,GAAqB,CAJrC;EAKEmB,QAAAA,QAAQ,EAAE,KALZ;EAMErF,QAAAA,MAAM,EAAE;EANV;;EAQF,SAAK,UAAL;EACE,0BACKD,KADL,EAEK2L,MAAM,CAAC3L,KAFZ;;EAIF;EACE,aAAOA,KAAP;EAjDJ;EAmDD;;ECpTD;MAEa6N,aAAb;EAAA;;EAME,yBAAYjJ,MAAZ,EAA0C;EAAA;;EACxC;EACA,UAAKA,MAAL,GAAcA,MAAM,IAAI,EAAxB;EACA,UAAKkJ,SAAL,GAAiB,EAAjB;EACA,UAAKV,UAAL,GAAkB,CAAlB;EAJwC;EAKzC;;EAXH;;EAAA,SAaEX,KAbF,GAaE,eACEC,MADF,EAEElN,OAFF,EAGEQ,KAHF,EAIiD;EAC/C,QAAMF,QAAQ,GAAG,IAAIqN,QAAJ,CAAa;EAC5BE,MAAAA,aAAa,EAAE,IADa;EAE5BD,MAAAA,UAAU,EAAE,EAAE,KAAKA,UAFS;EAG5B5N,MAAAA,OAAO,EAAEkN,MAAM,CAACqB,sBAAP,CAA8BvO,OAA9B,CAHmB;EAI5BQ,MAAAA,KAAK,EAALA,KAJ4B;EAK5BgI,MAAAA,cAAc,EAAExI,OAAO,CAACjB,WAAR,GACZmO,MAAM,CAACsB,mBAAP,CAA2BxO,OAAO,CAACjB,WAAnC,CADY,GAEZtD,SAPwB;EAQ5BqN,MAAAA,IAAI,EAAE9I,OAAO,CAAC8I;EARc,KAAb,CAAjB;EAWA,SAAKuE,GAAL,CAAS/M,QAAT;EAEA,WAAOA,QAAP;EACD,GAhCH;;EAAA,SAkCE+M,GAlCF,GAkCE,aAAI/M,QAAJ,EAAkD;EAChD,SAAKgO,SAAL,CAAe5S,IAAf,CAAoB4E,QAApB;EACA,SAAKwK,MAAL,CAAYxK,QAAZ;EACD,GArCH;;EAAA,SAuCEgJ,MAvCF,GAuCE,gBAAOhJ,QAAP,EAAqD;EACnD,SAAKgO,SAAL,GAAiB,KAAKA,SAAL,CAAe1S,MAAf,CAAsB,UAAAC,CAAC;EAAA,aAAIA,CAAC,KAAKyE,QAAV;EAAA,KAAvB,CAAjB;EACAA,IAAAA,QAAQ,CAACwE,MAAT;EACA,SAAKgG,MAAL,CAAYxK,QAAZ;EACD,GA3CH;;EAAA,SA6CEiN,KA7CF,GA6CE,iBAAc;EAAA;;EACZxF,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB,MAAA,MAAI,CAAC8G,SAAL,CAAe1K,OAAf,CAAuB,UAAAtD,QAAQ,EAAI;EACjC,QAAA,MAAI,CAACgJ,MAAL,CAAYhJ,QAAZ;EACD,OAFD;EAGD,KAJD;EAKD,GAnDH;;EAAA,SAqDEkN,MArDF,GAqDE,kBAAqB;EACnB,WAAO,KAAKc,SAAZ;EACD,GAvDH;;EAAA,SAyDE7D,IAzDF,GAyDE,cACEjL,OADF,EAE6D;EAC3D,QAAI,OAAOA,OAAO,CAACE,KAAf,KAAyB,WAA7B,EAA0C;EACxCF,MAAAA,OAAO,CAACE,KAAR,GAAgB,IAAhB;EACD;;EAED,WAAO,KAAK4O,SAAL,CAAe7D,IAAf,CAAoB,UAAAnK,QAAQ;EAAA,aAAID,aAAa,CAACb,OAAD,EAAUc,QAAV,CAAjB;EAAA,KAA5B,CAAP;EACD,GAjEH;;EAAA,SAmEEmN,OAnEF,GAmEE,iBAAQjO,OAAR,EAA8C;EAC5C,WAAO,KAAK8O,SAAL,CAAe1S,MAAf,CAAsB,UAAA0E,QAAQ;EAAA,aAAID,aAAa,CAACb,OAAD,EAAUc,QAAV,CAAjB;EAAA,KAA9B,CAAP;EACD,GArEH;;EAAA,SAuEEwK,MAvEF,GAuEE,gBAAOxK,QAAP,EAAgD;EAAA;;EAC9CyH,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB,MAAA,MAAI,CAACnM,SAAL,CAAeuI,OAAf,CAAuB,UAAArI,QAAQ,EAAI;EACjCA,QAAAA,QAAQ,CAAC+E,QAAD,CAAR;EACD,OAFD;EAGD,KAJD;EAKD,GA7EH;;EAAA,SA+EE+C,OA/EF,GA+EE,mBAAgB;EACd,SAAKoL,qBAAL;EACD,GAjFH;;EAAA,SAmFEtK,QAnFF,GAmFE,oBAAiB;EACf,SAAKsK,qBAAL;EACD,GArFH;;EAAA,SAuFEA,qBAvFF,GAuFE,iCAAuC;EACrC,QAAMC,eAAe,GAAG,KAAKJ,SAAL,CAAe1S,MAAf,CAAsB,UAAAC,CAAC;EAAA,aAAIA,CAAC,CAAC2E,KAAF,CAAQsF,QAAZ;EAAA,KAAvB,CAAxB;EACA,WAAOiC,aAAa,CAACP,KAAd,CAAoB;EAAA,aACzBkH,eAAe,CAACrN,MAAhB,CACE,UAAC4E,OAAD,EAAU3F,QAAV;EAAA,eACE2F,OAAO,CAACnD,IAAR,CAAa;EAAA,iBAAMxC,QAAQ,CAACuF,QAAT,GAAoB9C,KAApB,CAA0BlG,IAA1B,CAAN;EAAA,SAAb,CADF;EAAA,OADF,EAGE6F,OAAO,CAACC,OAAR,EAHF,CADyB;EAAA,KAApB,CAAP;EAOD,GAhGH;;EAAA;EAAA,EAAmCvH,YAAnC;;ECtBO,SAASuT,qBAAT,GAIuD;EAC5D,SAAO;EACL5C,IAAAA,OAAO,EAAE,iBAAAF,OAAO,EAAI;EAClBA,MAAAA,OAAO,CAACD,OAAR,GAAkB,YAAM;EAAA;;EACtB,YAAMgD,WAA2D,4BAC/D/C,OAAO,CAACV,YADuD,+CAC/D,sBAAsBrC,IADyC,qBAC/D,uBAA4B8F,WAD9B;EAEA,YAAMC,SAAS,6BAAGhD,OAAO,CAACV,YAAX,+CAAG,uBAAsBrC,IAAzB,qBAAG,uBAA4B+F,SAA9C;EACA,YAAMtD,SAAS,GAAGsD,SAAH,oBAAGA,SAAS,CAAEtD,SAA7B;EACA,YAAMuD,kBAAkB,GAAG,CAAAD,SAAS,QAAT,YAAAA,SAAS,CAAEE,SAAX,MAAyB,SAApD;EACA,YAAMC,sBAAsB,GAAG,CAAAH,SAAS,QAAT,YAAAA,SAAS,CAAEE,SAAX,MAAyB,UAAxD;EACA,YAAME,QAAQ,GAAG,wBAAApD,OAAO,CAACrL,KAAR,CAAciJ,IAAd,yCAAoByF,KAApB,KAA6B,EAA9C;EACA,YAAMC,aAAa,GAAG,yBAAAtD,OAAO,CAACrL,KAAR,CAAciJ,IAAd,0CAAoB2F,UAApB,KAAkC,EAAxD;EACA,YAAM/D,eAAe,GAAGpI,kBAAkB,EAA1C;EACA,YAAMoM,WAAW,GAAGhE,eAAH,oBAAGA,eAAe,CAAEM,MAArC;EACA,YAAI2D,aAAa,GAAGH,aAApB;EACA,YAAII,SAAS,GAAG,KAAhB,CAZsB;;EAetB,YAAM1Q,OAAO,GACXgN,OAAO,CAAC7L,OAAR,CAAgBnB,OAAhB,IAA4B;EAAA,iBAAM6D,OAAO,CAAC2D,MAAR,CAAe,iBAAf,CAAN;EAAA,SAD9B;;EAGA,YAAMmJ,aAAa,GAAG,SAAhBA,aAAgB,CACpBN,KADoB,EAEpBO,KAFoB,EAGpBC,IAHoB,EAIpBC,QAJoB,EAKjB;EACHL,UAAAA,aAAa,GAAGK,QAAQ,IACnBF,KADmB,SACTH,aADS,cAEhBA,aAFgB,GAEDG,KAFC,EAAxB;EAGA,iBAAOE,QAAQ,IAAID,IAAJ,SAAaR,KAAb,cAA0BA,KAA1B,GAAiCQ,IAAjC,EAAf;EACD,SAVD,CAlBsB;;;EA+BtB,YAAME,SAAS,GAAG,SAAZA,SAAY,CAChBV,KADgB,EAEhBW,MAFgB,EAGhBJ,KAHgB,EAIhBE,QAJgB,EAKO;EACvB,cAAIJ,SAAJ,EAAe;EACb,mBAAO7M,OAAO,CAAC2D,MAAR,CAAe,WAAf,CAAP;EACD;;EAED,cAAI,OAAOoJ,KAAP,KAAiB,WAAjB,IAAgC,CAACI,MAAjC,IAA2CX,KAAK,CAAClT,MAArD,EAA6D;EAC3D,mBAAO0G,OAAO,CAACC,OAAR,CAAgBuM,KAAhB,CAAP;EACD;;EAED,cAAM5D,cAAoC,GAAG;EAC3C1M,YAAAA,QAAQ,EAAEiN,OAAO,CAACjN,QADyB;EAE3C+M,YAAAA,MAAM,EAAE0D,WAFmC;EAG3C9D,YAAAA,SAAS,EAAEkE,KAHgC;EAI3C3G,YAAAA,IAAI,EAAE+C,OAAO,CAAC/C;EAJ6B,WAA7C;EAOA,cAAMgH,aAAa,GAAGjR,OAAO,CAACyM,cAAD,CAA7B;EAEA,cAAMrF,OAAO,GAAGvD,OAAO,CAACC,OAAR,CAAgBmN,aAAhB,EAA+BhN,IAA/B,CAAoC,UAAA4M,IAAI;EAAA,mBACtDF,aAAa,CAACN,KAAD,EAAQO,KAAR,EAAeC,IAAf,EAAqBC,QAArB,CADyC;EAAA,WAAxC,CAAhB;;EAIA,cAAI9K,YAAY,CAACiL,aAAD,CAAhB,EAAiC;EAC/B,gBAAMC,YAAY,GAAG9J,OAArB;EACA8J,YAAAA,YAAY,CAACjL,MAAb,GAAsBgL,aAAa,CAAChL,MAApC;EACD;;EAED,iBAAOmB,OAAP;EACD,SAjCD;;EAmCA,YAAIA,OAAJ,CAlEsB;;EAqEtB,YAAI,CAACgJ,QAAQ,CAACjT,MAAd,EAAsB;EACpBiK,UAAAA,OAAO,GAAG2J,SAAS,CAAC,EAAD,CAAnB;EACD,SAFD;EAAA,aAKK,IAAId,kBAAJ,EAAwB;EAC3B,gBAAMe,MAAM,GAAG,OAAOtE,SAAP,KAAqB,WAApC;EACA,gBAAMkE,KAAK,GAAGI,MAAM,GAChBtE,SADgB,GAEhByE,gBAAgB,CAACnE,OAAO,CAAC7L,OAAT,EAAkBiP,QAAlB,CAFpB;EAGAhJ,YAAAA,OAAO,GAAG2J,SAAS,CAACX,QAAD,EAAWY,MAAX,EAAmBJ,KAAnB,CAAnB;EACD,WANI;EAAA,eASA,IAAIT,sBAAJ,EAA4B;EAC/B,kBAAMa,OAAM,GAAG,OAAOtE,SAAP,KAAqB,WAApC;;EACA,kBAAMkE,MAAK,GAAGI,OAAM,GAChBtE,SADgB,GAEhB0E,oBAAoB,CAACpE,OAAO,CAAC7L,OAAT,EAAkBiP,QAAlB,CAFxB;;EAGAhJ,cAAAA,OAAO,GAAG2J,SAAS,CAACX,QAAD,EAAWY,OAAX,EAAmBJ,MAAnB,EAA0B,IAA1B,CAAnB;EACD,aANI;EAAA,iBASA;EAAA;EACHH,kBAAAA,aAAa,GAAG,EAAhB;EAEA,sBAAMO,MAAM,GAAG,OAAOhE,OAAO,CAAC7L,OAAR,CAAgBgQ,gBAAvB,KAA4C,WAA3D;EAEA,sBAAME,oBAAoB,GACxBtB,WAAW,IAAIK,QAAQ,CAAC,CAAD,CAAvB,GACIL,WAAW,CAACK,QAAQ,CAAC,CAAD,CAAT,EAAc,CAAd,EAAiBA,QAAjB,CADf,GAEI,IAHN,CALG;;EAWHhJ,kBAAAA,OAAO,GAAGiK,oBAAoB,GAC1BN,SAAS,CAAC,EAAD,EAAKC,MAAL,EAAaV,aAAa,CAAC,CAAD,CAA1B,CADiB,GAE1BzM,OAAO,CAACC,OAAR,CAAgB6M,aAAa,CAAC,EAAD,EAAKL,aAAa,CAAC,CAAD,CAAlB,EAAuBF,QAAQ,CAAC,CAAD,CAA/B,CAA7B,CAFJ,CAXG;;EAAA,6CAgBM7S,CAhBN;EAiBD6J,oBAAAA,OAAO,GAAGA,OAAO,CAACnD,IAAR,CAAa,UAAAoM,KAAK,EAAI;EAC9B,0BAAMiB,mBAAmB,GACvBvB,WAAW,IAAIK,QAAQ,CAAC7S,CAAD,CAAvB,GACIwS,WAAW,CAACK,QAAQ,CAAC7S,CAAD,CAAT,EAAcA,CAAd,EAAiB6S,QAAjB,CADf,GAEI,IAHN;;EAKA,0BAAIkB,mBAAJ,EAAyB;EACvB,4BAAMV,OAAK,GAAGI,MAAM,GAChBV,aAAa,CAAC/S,CAAD,CADG,GAEhB4T,gBAAgB,CAACnE,OAAO,CAAC7L,OAAT,EAAkBkP,KAAlB,CAFpB;;EAGA,+BAAOU,SAAS,CAACV,KAAD,EAAQW,MAAR,EAAgBJ,OAAhB,CAAhB;EACD;;EACD,6BAAO/M,OAAO,CAACC,OAAR,CACL6M,aAAa,CAACN,KAAD,EAAQC,aAAa,CAAC/S,CAAD,CAArB,EAA0B6S,QAAQ,CAAC7S,CAAD,CAAlC,CADR,CAAP;EAGD,qBAfS,CAAV;EAjBC;;EAgBH,uBAAK,IAAIA,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6S,QAAQ,CAACjT,MAA7B,EAAqCI,CAAC,EAAtC,EAA0C;EAAA,0BAAjCA,CAAiC;EAiBzC;EAjCE;EAkCJ;;EAED,YAAMgU,YAAY,GAAGnK,OAAO,CAACnD,IAAR,CAAa,UAAAoM,KAAK;EAAA,iBAAK;EAC1CA,YAAAA,KAAK,EAALA,KAD0C;EAE1CE,YAAAA,UAAU,EAAEE;EAF8B,WAAL;EAAA,SAAlB,CAArB;EAKA,YAAMe,iBAAiB,GAAGD,YAA1B;;EAEAC,QAAAA,iBAAiB,CAACvL,MAAlB,GAA2B,YAAM;EAC/ByK,UAAAA,SAAS,GAAG,IAAZ;EACAlE,UAAAA,eAAe,QAAf,YAAAA,eAAe,CAAE3F,KAAjB;;EACA,cAAIb,YAAY,CAACoB,OAAD,CAAhB,EAA2B;EACzBA,YAAAA,OAAO,CAACnB,MAAR;EACD;EACF,SAND;;EAQA,eAAOsL,YAAP;EACD,OAhJD;EAiJD;EAnJI,GAAP;EAqJD;EAEM,SAASJ,gBAAT,CACLhQ,OADK,EAELkP,KAFK,EAGgB;EACrB,SAAOlP,OAAO,CAACgQ,gBAAf,oBAAOhQ,OAAO,CAACgQ,gBAAR,CAA2Bd,KAAK,CAACA,KAAK,CAAClT,MAAN,GAAe,CAAhB,CAAhC,EAAoDkT,KAApD,CAAP;EACD;EAEM,SAASe,oBAAT,CACLjQ,OADK,EAELkP,KAFK,EAGgB;EACrB,SAAOlP,OAAO,CAACiQ,oBAAf,oBAAOjQ,OAAO,CAACiQ,oBAAR,CAA+Bf,KAAK,CAAC,CAAD,CAApC,EAAyCA,KAAzC,CAAP;EACD;EAED;;;;;EAIO,SAASoB,WAAT,CACLtQ,OADK,EAELkP,KAFK,EAGgB;EACrB,MAAIlP,OAAO,CAACgQ,gBAAR,IAA4B3S,KAAK,CAACC,OAAN,CAAc4R,KAAd,CAAhC,EAAsD;EACpD,QAAMqB,aAAa,GAAGP,gBAAgB,CAAChQ,OAAD,EAAUkP,KAAV,CAAtC;EACA,WACE,OAAOqB,aAAP,KAAyB,WAAzB,IACAA,aAAa,KAAK,IADlB,IAEAA,aAAa,KAAK,KAHpB;EAKD;EACF;EAED;;;;;EAIO,SAASC,eAAT,CACLxQ,OADK,EAELkP,KAFK,EAGgB;EACrB,MAAIlP,OAAO,CAACiQ,oBAAR,IAAgC5S,KAAK,CAACC,OAAN,CAAc4R,KAAd,CAApC,EAA0D;EACxD,QAAMuB,iBAAiB,GAAGR,oBAAoB,CAACjQ,OAAD,EAAUkP,KAAV,CAA9C;EACA,WACE,OAAOuB,iBAAP,KAA6B,WAA7B,IACAA,iBAAiB,KAAK,IADtB,IAEAA,iBAAiB,KAAK,KAHxB;EAKD;EACF;;ECjKD;MAEaC,WAAb;EASE,uBAAYtL,MAAZ,EAA4C;EAAA,QAAhCA,MAAgC;EAAhCA,MAAAA,MAAgC,GAAJ,EAAI;EAAA;;EAC1C,SAAKuL,UAAL,GAAkBvL,MAAM,CAACuL,UAAP,IAAqB,IAAI7D,UAAJ,EAAvC;EACA,SAAKe,aAAL,GAAqBzI,MAAM,CAACyI,aAAP,IAAwB,IAAIQ,aAAJ,EAA7C;EACA,SAAK7F,cAAL,GAAsBpD,MAAM,CAACoD,cAAP,IAAyB,EAA/C;EACA,SAAKoI,aAAL,GAAqB,EAArB;EACA,SAAKC,gBAAL,GAAwB,EAAxB;EACD;;EAfH;;EAAA,SAiBEC,KAjBF,GAiBE,iBAAc;EAAA;;EACZ,SAAKC,gBAAL,GAAwB9M,YAAY,CAAC3I,SAAb,CAAuB,YAAM;EACnD,UAAI2I,YAAY,CAACJ,SAAb,MAA4BY,aAAa,CAACH,QAAd,EAAhC,EAA0D;EACxD,QAAA,KAAI,CAACuJ,aAAL,CAAmBxK,OAAnB;;EACA,QAAA,KAAI,CAACsN,UAAL,CAAgBtN,OAAhB;EACD;EACF,KALuB,CAAxB;EAMA,SAAK2N,iBAAL,GAAyBvM,aAAa,CAACnJ,SAAd,CAAwB,YAAM;EACrD,UAAI2I,YAAY,CAACJ,SAAb,MAA4BY,aAAa,CAACH,QAAd,EAAhC,EAA0D;EACxD,QAAA,KAAI,CAACuJ,aAAL,CAAmB1J,QAAnB;;EACA,QAAA,KAAI,CAACwM,UAAL,CAAgBxM,QAAhB;EACD;EACF,KALwB,CAAzB;EAMD,GA9BH;;EAAA,SAgCE8M,OAhCF,GAgCE,mBAAgB;EAAA;;EACd,kCAAKF,gBAAL;EACA,kCAAKC,iBAAL;EACD,GAnCH;;EAAA,SAuCE5Q,UAvCF,GAuCE,oBAAW5B,IAAX,EAA2CC,IAA3C,EAAwE;EAAA,2BACpDQ,eAAe,CAACT,IAAD,EAAOC,IAAP,CADqC;EAAA,QAC/De,OAD+D;;EAEtEA,IAAAA,OAAO,CAACG,QAAR,GAAmB,IAAnB;EACA,WAAO,KAAKgR,UAAL,CAAgBlD,OAAhB,CAAwBjO,OAAxB,EAAiCxD,MAAxC;EACD,GA3CH;;EAAA,SA6CEkV,UA7CF,GA6CE,oBAAW1R,OAAX,EAA8C;EAC5C,WAAO,KAAKqO,aAAL,CAAmBJ,OAAnB,cAAgCjO,OAAhC;EAAyCG,MAAAA,QAAQ,EAAE;EAAnD,QAA2D3D,MAAlE;EACD,GA/CH;;EAAA,SAiDEmV,YAjDF,GAiDE,sBACEvS,QADF,EAEEY,OAFF,EAGqB;EAAA;;EACnB,oCAAO,KAAKmR,UAAL,CAAgBlG,IAAhB,CAA4B7L,QAA5B,EAAsCY,OAAtC,CAAP,qBAAO,sBAAgDgB,KAAhD,CAAsDiJ,IAA7D;EACD,GAtDH;;EAAA,SA0DE2H,cA1DF,GA0DE,wBACEC,iBADF,EAEuB;EACrB,WAAO,KAAKC,aAAL,GACJ7D,OADI,CACI4D,iBADJ,EAEJE,GAFI,CAEA,gBAAyB;EAAA,UAAtB3S,QAAsB,QAAtBA,QAAsB;EAAA,UAAZ4B,KAAY,QAAZA,KAAY;EAC5B,UAAMiJ,IAAI,GAAGjJ,KAAK,CAACiJ,IAAnB;EACA,aAAO,CAAC7K,QAAD,EAAW6K,IAAX,CAAP;EACD,KALI,CAAP;EAMD,GAnEH;;EAAA,SAqEE+H,YArEF,GAqEE,sBACE5S,QADF,EAEE7B,OAFF,EAGEiD,OAHF,EAIS;EACP,QAAMyR,aAAa,GAAGlT,cAAc,CAACK,QAAD,CAApC;EACA,QAAM8S,gBAAgB,GAAG,KAAKvE,mBAAL,CAAyBsE,aAAzB,CAAzB;EACA,WAAO,KAAKd,UAAL,CACJ1D,KADI,CACE,IADF,EACQyE,gBADR,EAEJnI,OAFI,CAEIxM,OAFJ,EAEaiD,OAFb,CAAP;EAGD,GA/EH;;EAAA,SA6FE2R,cA7FF,GA6FE,wBACEN,iBADF,EAEEtU,OAFF,EAGEiD,OAHF,EAIuB;EAAA;;EACrB,WAAO+H,aAAa,CAACP,KAAd,CAAoB;EAAA,aACzB,MAAI,CAAC8J,aAAL,GACG7D,OADH,CACW4D,iBADX,EAEGE,GAFH,CAEO;EAAA,YAAG3S,QAAH,SAAGA,QAAH;EAAA,eAAkB,CACrBA,QADqB,EAErB,MAAI,CAAC4S,YAAL,CAAyB5S,QAAzB,EAAmC7B,OAAnC,EAA4CiD,OAA5C,CAFqB,CAAlB;EAAA,OAFP,CADyB;EAAA,KAApB,CAAP;EAQD,GA1GH;;EAAA,SA4GE4R,aA5GF,GA4GE,uBACEhT,QADF,EAEEY,OAFF,EAGyC;EAAA;;EACvC,qCAAO,KAAKmR,UAAL,CAAgBlG,IAAhB,CAAoC7L,QAApC,EAA8CY,OAA9C,CAAP,qBAAO,uBAAwDgB,KAA/D;EACD,GAjHH;;EAAA,SAqHEqR,aArHF,GAqHE,uBAAcrT,IAAd,EAA8CC,IAA9C,EAAyE;EAAA,4BACrDQ,eAAe,CAACT,IAAD,EAAOC,IAAP,CADsC;EAAA,QAChEe,OADgE;;EAEvE,QAAMmR,UAAU,GAAG,KAAKA,UAAxB;EACA5I,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxBmJ,MAAAA,UAAU,CAAClD,OAAX,CAAmBjO,OAAnB,EAA4BoE,OAA5B,CAAoC,UAAAnE,KAAK,EAAI;EAC3CkR,QAAAA,UAAU,CAACrH,MAAX,CAAkB7J,KAAlB;EACD,OAFD;EAGD,KAJD;EAKD,GA7HH;;EAAA,SAwIEqS,YAxIF,GAwIE,sBACEtT,IADF,EAEEC,IAFF,EAGEC,IAHF,EAIiB;EAAA;;EAAA,4BACYO,eAAe,CAACT,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAD3B;EAAA,QACRc,OADQ;EAAA,QACCQ,OADD;;EAEf,QAAM2Q,UAAU,GAAG,KAAKA,UAAxB;;EAEA,QAAMoB,cAAmC,gBACpCvS,OADoC;EAEvCJ,MAAAA,MAAM,EAAE;EAF+B,MAAzC;;EAKA,WAAO2I,aAAa,CAACP,KAAd,CAAoB,YAAM;EAC/BmJ,MAAAA,UAAU,CAAClD,OAAX,CAAmBjO,OAAnB,EAA4BoE,OAA5B,CAAoC,UAAAnE,KAAK,EAAI;EAC3CA,QAAAA,KAAK,CAAC0K,KAAN;EACD,OAFD;EAGA,aAAO,MAAI,CAAC6H,cAAL,CAAoBD,cAApB,EAAoC/R,OAApC,CAAP;EACD,KALM,CAAP;EAMD,GA3JH;;EAAA,SAmKEiS,aAnKF,GAmKE,uBACEzT,IADF,EAEEC,IAFF,EAGEC,IAHF,EAIiB;EAAA;;EAAA,4BACuBO,eAAe,CAACT,IAAD,EAAOC,IAAP,EAAaC,IAAb,CADtC;EAAA,QACRc,OADQ;EAAA;EAAA,QACCmG,aADD,mCACiB,EADjB;;EAGf,QAAI,OAAOA,aAAa,CAACX,MAArB,KAAgC,WAApC,EAAiD;EAC/CW,MAAAA,aAAa,CAACX,MAAd,GAAuB,IAAvB;EACD;;EAED,QAAMkN,QAAQ,GAAGnK,aAAa,CAACP,KAAd,CAAoB;EAAA,aACnC,MAAI,CAACmJ,UAAL,CAAgBlD,OAAhB,CAAwBjO,OAAxB,EAAiC+R,GAAjC,CAAqC,UAAA9R,KAAK;EAAA,eAAIA,KAAK,CAACqF,MAAN,CAAaa,aAAb,CAAJ;EAAA,OAA1C,CADmC;EAAA,KAApB,CAAjB;EAIA,WAAOjD,OAAO,CAACyP,GAAR,CAAYD,QAAZ,EAAsBpP,IAAtB,CAA2BjG,IAA3B,EAAiCkG,KAAjC,CAAuClG,IAAvC,CAAP;EACD,GAnLH;;EAAA,SA8LEuV,iBA9LF,GA8LE,2BACE5T,IADF,EAEEC,IAFF,EAGEC,IAHF,EAIiB;EAAA;EAAA;EAAA;EAAA;;EAAA,4BACYO,eAAe,CAACT,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAD3B;EAAA,QACRc,OADQ;EAAA,QACCQ,OADD;;EAGf,QAAM+R,cAAmC,gBACpCvS,OADoC;EAEvC;EACA;EACAJ,MAAAA,MAAM,oCAAEI,OAAO,CAAC6S,aAAV,oCAA2B7S,OAAO,CAACJ,MAAnC,oBAA6C,IAJZ;EAKvCC,MAAAA,QAAQ,2BAAEG,OAAO,CAAC8S,eAAV,oCAA6B;EALE,MAAzC;;EAQA,WAAOvK,aAAa,CAACP,KAAd,CAAoB,YAAM;EAC/B,MAAA,MAAI,CAACmJ,UAAL,CAAgBlD,OAAhB,CAAwBjO,OAAxB,EAAiCoE,OAAjC,CAAyC,UAAAnE,KAAK,EAAI;EAChDA,QAAAA,KAAK,CAACwL,UAAN;EACD,OAFD;;EAGA,aAAO,MAAI,CAAC+G,cAAL,CAAoBD,cAApB,EAAoC/R,OAApC,CAAP;EACD,KALM,CAAP;EAMD,GAnNH;;EAAA,SA8NEgS,cA9NF,GA8NE,wBACExT,IADF,EAEEC,IAFF,EAGEC,IAHF,EAIiB;EAAA;;EAAA,4BACYO,eAAe,CAACT,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAD3B;EAAA,QACRc,OADQ;EAAA,QACCQ,OADD;;EAGf,QAAMkS,QAAQ,GAAGnK,aAAa,CAACP,KAAd,CAAoB;EAAA,aACnC,MAAI,CAACmJ,UAAL,CAAgBlD,OAAhB,CAAwBjO,OAAxB,EAAiC+R,GAAjC,CAAqC,UAAA9R,KAAK;EAAA,eACxCA,KAAK,CAACyL,KAAN,CAAYzP,SAAZ,eACKuE,OADL;EAEE8I,UAAAA,IAAI,EAAE;EAAE8F,YAAAA,WAAW,EAAEpP,OAAF,oBAAEA,OAAO,CAAEoP;EAAxB;EAFR,WADwC;EAAA,OAA1C,CADmC;EAAA,KAApB,CAAjB;EASA,QAAI3I,OAAO,GAAGvD,OAAO,CAACyP,GAAR,CAAYD,QAAZ,EAAsBpP,IAAtB,CAA2BjG,IAA3B,CAAd;;EAEA,QAAI,EAACmD,OAAD,oBAACA,OAAO,CAAEuS,YAAV,CAAJ,EAA4B;EAC1BtM,MAAAA,OAAO,GAAGA,OAAO,CAAClD,KAAR,CAAclG,IAAd,CAAV;EACD;;EAED,WAAOoJ,OAAP;EACD,GArPH;;EAAA,SAkREuM,UAlRF,GAkRE,oBAMEhU,IANF,EAOEC,IAPF,EAUEC,IAVF,EAWkB;EAChB,QAAM+S,aAAa,GAAGlT,cAAc,CAACC,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAApC;EACA,QAAMgT,gBAAgB,GAAG,KAAKvE,mBAAL,CAAyBsE,aAAzB,CAAzB,CAFgB;;EAKhB,QAAI,OAAOC,gBAAgB,CAAC5K,KAAxB,KAAkC,WAAtC,EAAmD;EACjD4K,MAAAA,gBAAgB,CAAC5K,KAAjB,GAAyB,KAAzB;EACD;;EAED,QAAMrH,KAAK,GAAG,KAAKkR,UAAL,CAAgB1D,KAAhB,CAAsB,IAAtB,EAA4ByE,gBAA5B,CAAd;EAEA,WAAOjS,KAAK,CAAC+K,aAAN,CAAoBkH,gBAAgB,CAACxT,SAArC,IACHuB,KAAK,CAACyL,KAAN,CAAYwG,gBAAZ,CADG,GAEHhP,OAAO,CAACC,OAAR,CAAgBlD,KAAK,CAACe,KAAN,CAAYiJ,IAA5B,CAFJ;EAGD,GA3SH;;EAAA,SAwUEgJ,aAxUF,GAwUE,uBAMEjU,IANF,EAOEC,IAPF,EAUEC,IAVF,EAWiB;EACf,WAAO,KAAK8T,UAAL,CAAgBhU,IAAhB,EAA6BC,IAA7B,EAA0CC,IAA1C,EACJoE,IADI,CACCjG,IADD,EAEJkG,KAFI,CAEElG,IAFF,CAAP;EAGD,GAvVH;;EAAA,SAoXE6V,kBApXF,GAoXE,4BAMElU,IANF,EASEC,IATF,EAYEC,IAZF,EAagC;EAC9B,QAAM+S,aAAa,GAAGlT,cAAc,CAACC,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAApC;EACA+S,IAAAA,aAAa,CAAC3F,QAAd,GAAyB6C,qBAAqB,EAA9C;EAKA,WAAO,KAAK6D,UAAL,CAAgBf,aAAhB,CAAP;EACD,GAzYH;;EAAA,SAsaEkB,qBAtaF,GAsaE,+BAMEnU,IANF,EASEC,IATF,EAYEC,IAZF,EAaiB;EACf,WAAO,KAAKgU,kBAAL,CAAwBlU,IAAxB,EAAqCC,IAArC,EAAkDC,IAAlD,EACJoE,IADI,CACCjG,IADD,EAEJkG,KAFI,CAEElG,IAFF,CAAP;EAGD,GAvbH;;EAAA,SAybE+V,eAzbF,GAybE,2BAAiC;EAAA;;EAC/B,QAAMV,QAAQ,GAAGnK,aAAa,CAACP,KAAd,CAAoB;EAAA,aACnC,MAAI,CAACqG,aAAL,CAAmBL,MAAnB,GAA4B+D,GAA5B,CAAgC,UAAAjR,QAAQ;EAAA,eAAIA,QAAQ,CAACwE,MAAT,EAAJ;EAAA,OAAxC,CADmC;EAAA,KAApB,CAAjB;EAGA,WAAOpC,OAAO,CAACyP,GAAR,CAAYD,QAAZ,EAAsBpP,IAAtB,CAA2BjG,IAA3B,EAAiCkG,KAAjC,CAAuClG,IAAvC,CAAP;EACD,GA9bH;;EAAA,SAgcE4R,qBAhcF,GAgcE,iCAAuC;EACrC,WAAO,KAAKoE,gBAAL,GAAwBpE,qBAAxB,EAAP;EACD,GAlcH;;EAAA,SAocEP,eApcF,GAocE,yBAMElO,OANF,EAOkB;EAChB,WAAO,KAAK6N,aAAL,CAAmBZ,KAAnB,CAAyB,IAAzB,EAA+BjN,OAA/B,EAAwC8N,OAAxC,EAAP;EACD,GA7cH;;EAAA,SA+cEwD,aA/cF,GA+cE,yBAA4B;EAC1B,WAAO,KAAKX,UAAZ;EACD,GAjdH;;EAAA,SAmdEkC,gBAndF,GAmdE,4BAAkC;EAChC,WAAO,KAAKhF,aAAZ;EACD,GArdH;;EAAA,SAudEiF,iBAvdF,GAudE,6BAAoC;EAClC,WAAO,KAAKtK,cAAZ;EACD,GAzdH;;EAAA,SA2dES,iBA3dF,GA2dE,2BAAkBjJ,OAAlB,EAAiD;EAC/C,SAAKwI,cAAL,GAAsBxI,OAAtB;EACD,GA7dH;;EAAA,SA+dE+S,gBA/dF,GA+dE,0BACEnU,QADF,EAEEoB,OAFF,EAGQ;EACN,QAAMsB,MAAM,GAAG,KAAKsP,aAAL,CAAmBnG,IAAnB,CACb,UAAA5O,CAAC;EAAA,aAAI0E,YAAY,CAAC3B,QAAD,CAAZ,KAA2B2B,YAAY,CAAC1E,CAAC,CAAC+C,QAAH,CAA3C;EAAA,KADY,CAAf;;EAGA,QAAI0C,MAAJ,EAAY;EACVA,MAAAA,MAAM,CAACkH,cAAP,GAAwBxI,OAAxB;EACD,KAFD,MAEO;EACL,WAAK4Q,aAAL,CAAmBlV,IAAnB,CAAwB;EAAEkD,QAAAA,QAAQ,EAARA,QAAF;EAAY4J,QAAAA,cAAc,EAAExI;EAA5B,OAAxB;EACD;EACF,GA3eH;;EAAA,SA6eEoN,gBA7eF,GA6eE,0BACExO,QADF,EAE6D;EAAA;;EAC3D,WAAOA,QAAQ,4BACX,KAAKgS,aAAL,CAAmBnG,IAAnB,CAAwB,UAAA5O,CAAC;EAAA,aAAIoE,eAAe,CAACrB,QAAD,EAAW/C,CAAC,CAAC+C,QAAb,CAAnB;EAAA,KAAzB,CADW,qBACX,sBACI4J,cAFO,GAGX/M,SAHJ;EAID,GApfH;;EAAA,SAsfEuX,mBAtfF,GAsfE,6BACEjU,WADF,EAEEiB,OAFF,EAGQ;EACN,QAAMsB,MAAM,GAAG,KAAKuP,gBAAL,CAAsBpG,IAAtB,CACb,UAAA5O,CAAC;EAAA,aAAI0E,YAAY,CAACxB,WAAD,CAAZ,KAA8BwB,YAAY,CAAC1E,CAAC,CAACkD,WAAH,CAA9C;EAAA,KADY,CAAf;;EAGA,QAAIuC,MAAJ,EAAY;EACVA,MAAAA,MAAM,CAACkH,cAAP,GAAwBxI,OAAxB;EACD,KAFD,MAEO;EACL,WAAK6Q,gBAAL,CAAsBnV,IAAtB,CAA2B;EAAEqD,QAAAA,WAAW,EAAXA,WAAF;EAAeyJ,QAAAA,cAAc,EAAExI;EAA/B,OAA3B;EACD;EACF,GAlgBH;;EAAA,SAogBEwO,mBApgBF,GAogBE,6BACEzP,WADF,EAE2D;EAAA;;EACzD,WAAOA,WAAW,4BACd,KAAK8R,gBAAL,CAAsBpG,IAAtB,CAA2B,UAAA5O,CAAC;EAAA,aAC1BoE,eAAe,CAAClB,WAAD,EAAclD,CAAC,CAACkD,WAAhB,CADW;EAAA,KAA5B,CADc,qBACd,sBAEGyJ,cAHW,GAId/M,SAJJ;EAKD,GA5gBH;;EAAA,SA8gBE0R,mBA9gBF,GA8gBE,6BAOEnN,OAPF,EAc4E;EAC1E,QAAIA,OAAJ,oBAAIA,OAAO,CAAEiT,UAAb,EAAyB;EACvB,aAAOjT,OAAP;EACD;;EAED,QAAM0R,gBAAgB,gBACjB,KAAKlJ,cAAL,CAAoBuE,OADH,EAEjB,KAAKK,gBAAL,CAAsBpN,OAAtB,oBAAsBA,OAAO,CAAEpB,QAA/B,CAFiB,EAGjBoB,OAHiB;EAIpBiT,MAAAA,UAAU,EAAE;EAJQ,MAAtB;;EAaA,QAAI,CAACvB,gBAAgB,CAAC5R,SAAlB,IAA+B4R,gBAAgB,CAAC9S,QAApD,EAA8D;EAC5D8S,MAAAA,gBAAgB,CAAC5R,SAAjB,GAA6BC,qBAAqB,CAChD2R,gBAAgB,CAAC9S,QAD+B,EAEhD8S,gBAFgD,CAAlD;EAID;;EAED,WAAOA,gBAAP;EACD,GAtjBH;;EAAA,SAwjBEwB,2BAxjBF,GAwjBE,qCAOElT,OAPF,EAc4E;EAC1E,WAAO,KAAKmN,mBAAL,CAAyBnN,OAAzB,CAAP;EACD,GAxkBH;;EAAA,SA0kBEuO,sBA1kBF,GA0kBE,gCACEvO,OADF,EAEK;EACH,QAAIA,OAAJ,oBAAIA,OAAO,CAAEiT,UAAb,EAAyB;EACvB,aAAOjT,OAAP;EACD;;EACD,wBACK,KAAKwI,cAAL,CAAoB8F,SADzB,EAEK,KAAKE,mBAAL,CAAyBxO,OAAzB,oBAAyBA,OAAO,CAAEjB,WAAlC,CAFL,EAGKiB,OAHL;EAIEiT,MAAAA,UAAU,EAAE;EAJd;EAMD,GAtlBH;;EAAA,SAwlBE1F,KAxlBF,GAwlBE,iBAAc;EACZ,SAAKoD,UAAL,CAAgBpD,KAAhB;EACA,SAAKM,aAAL,CAAmBN,KAAnB;EACD,GA3lBH;;EAAA;EAAA;;MCba4F,aAAb;EAAA;;EAoCE,yBACEjG,MADF,EAEElN,OAFF,EASE;EAAA;;EACA;EAEA,UAAKkN,MAAL,GAAcA,MAAd;EACA,UAAKlN,OAAL,GAAeA,OAAf;EACA,UAAKoT,YAAL,GAAoB,EAApB;EACA,UAAKC,WAAL,GAAmB,IAAnB;;EACA,UAAKC,WAAL;;EACA,UAAK7K,UAAL,CAAgBzI,OAAhB;;EARA;EASD;;EAtDH;;EAAA,SAwDYsT,WAxDZ,GAwDE,uBAA8B;EAC5B,SAAKhK,MAAL,GAAc,KAAKA,MAAL,CAAY4C,IAAZ,CAAiB,IAAjB,CAAd;EACA,SAAKvB,OAAL,GAAe,KAAKA,OAAL,CAAauB,IAAb,CAAkB,IAAlB,CAAf;EACD,GA3DH;;EAAA,SA6DYvQ,WA7DZ,GA6DE,uBAA8B;EAC5B,QAAI,KAAKN,SAAL,CAAeW,MAAf,KAA0B,CAA9B,EAAiC;EAC/B,WAAKuX,YAAL,CAAkB1I,WAAlB,CAA8B,IAA9B;;EAEA,UAAI2I,kBAAkB,CAAC,KAAKD,YAAN,EAAoB,KAAKvT,OAAzB,CAAtB,EAAyD;EACvD,aAAKyT,YAAL;EACD;;EAED,WAAKC,YAAL;EACD;EACF,GAvEH;;EAAA,SAyEY5X,aAzEZ,GAyEE,yBAAgC;EAC9B,QAAI,CAAC,KAAKT,SAAL,CAAeW,MAApB,EAA4B;EAC1B,WAAKkO,OAAL;EACD;EACF,GA7EH;;EAAA,SA+EEU,sBA/EF,GA+EE,kCAAkC;EAChC,WAAO+I,aAAa,CAClB,KAAKJ,YADa,EAElB,KAAKvT,OAFa,EAGlB,KAAKA,OAAL,CAAa4T,kBAHK,CAApB;EAKD,GArFH;;EAAA,SAuFElJ,wBAvFF,GAuFE,oCAAoC;EAClC,WAAOiJ,aAAa,CAClB,KAAKJ,YADa,EAElB,KAAKvT,OAFa,EAGlB,KAAKA,OAAL,CAAa6T,oBAHK,CAApB;EAKD,GA7FH;;EAAA,SA+FE3J,OA/FF,GA+FE,mBAAgB;EACd,SAAK7O,SAAL,GAAiB,EAAjB;EACA,SAAKyY,WAAL;EACA,SAAKP,YAAL,CAAkBxI,cAAlB,CAAiC,IAAjC;EACD,GAnGH;;EAAA,SAqGEtC,UArGF,GAqGE,oBACEzI,OADF,EAQE+T,aARF,EASQ;EACN,QAAMC,WAAW,GAAG,KAAKhU,OAAzB;EACA,QAAMiU,SAAS,GAAG,KAAKV,YAAvB;EAEA,SAAKvT,OAAL,GAAe,KAAKkN,MAAL,CAAYgG,2BAAZ,CAAwClT,OAAxC,CAAf;;EAEA,QACE,OAAO,KAAKA,OAAL,CAAaqK,OAApB,KAAgC,WAAhC,IACA,OAAO,KAAKrK,OAAL,CAAaqK,OAApB,KAAgC,SAFlC,EAGE;EACA,YAAM,IAAI9H,KAAJ,CAAU,kCAAV,CAAN;EACD,KAXK;;;EAcN,QAAI,CAAC,KAAKvC,OAAL,CAAapB,QAAlB,EAA4B;EAC1B,WAAKoB,OAAL,CAAapB,QAAb,GAAwBoV,WAAW,CAACpV,QAApC;EACD;;EAED,SAAKsV,WAAL;EAEA,QAAMC,OAAO,GAAG,KAAKpY,YAAL,EAAhB,CApBM;;EAuBN,QACEoY,OAAO,IACPC,qBAAqB,CACnB,KAAKb,YADc,EAEnBU,SAFmB,EAGnB,KAAKjU,OAHc,EAInBgU,WAJmB,CAFvB,EAQE;EACA,WAAKP,YAAL;EACD,KAjCK;;;EAoCN,SAAKY,YAAL,CAAkBN,aAAlB,EApCM;;EAuCN,QACEI,OAAO,KACN,KAAKZ,YAAL,KAAsBU,SAAtB,IACC,KAAKjU,OAAL,CAAaqK,OAAb,KAAyB2J,WAAW,CAAC3J,OADtC,IAEC,KAAKrK,OAAL,CAAa9B,SAAb,KAA2B8V,WAAW,CAAC9V,SAHlC,CADT,EAKE;EACA,WAAKoW,kBAAL;EACD;;EAED,QAAMC,mBAAmB,GAAG,KAAKC,sBAAL,EAA5B,CAhDM;;EAmDN,QACEL,OAAO,KACN,KAAKZ,YAAL,KAAsBU,SAAtB,IACC,KAAKjU,OAAL,CAAaqK,OAAb,KAAyB2J,WAAW,CAAC3J,OADtC,IAECkK,mBAAmB,KAAK,KAAKE,sBAHxB,CADT,EAKE;EACA,WAAKC,qBAAL,CAA2BH,mBAA3B;EACD;EACF,GAzKH;;EAAA,SA2KEI,mBA3KF,GA2KE,6BACE3U,OADF,EAQsC;EACpC,QAAM0R,gBAAgB,GAAG,KAAKxE,MAAL,CAAYgG,2BAAZ,CAAwClT,OAAxC,CAAzB;EAEA,QAAMP,KAAK,GAAG,KAAKyN,MAAL,CACXoE,aADW,GAEXrE,KAFW,CAGV,KAAKC,MAHK,EAIVwE,gBAJU,CAAd;EAYA,WAAO,KAAKkD,YAAL,CAAkBnV,KAAlB,EAAyBiS,gBAAzB,CAAP;EACD,GAnMH;;EAAA,SAqMEnH,gBArMF,GAqME,4BAAuD;EACrD,WAAO,KAAKsK,aAAZ;EACD,GAvMH;;EAAA,SAyMEC,WAzMF,GAyME,qBACExT,MADF,EAEEoQ,gBAFF,EASsC;EAAA;;EACpC,QAAMqD,aAAa,GAAG,EAAtB;;EAEA,QAAMC,SAAS,GAAG,SAAZA,SAAY,CAACzY,GAAD,EAAoC;EACpD,UAAI,CAAC,MAAI,CAAC6W,YAAL,CAAkBrP,QAAlB,CAA2BxH,GAA3B,CAAL,EAAsC;EACpC,QAAA,MAAI,CAAC6W,YAAL,CAAkB1X,IAAlB,CAAuBa,GAAvB;EACD;EACF,KAJD;;EAMAvB,IAAAA,MAAM,CAACmG,IAAP,CAAYG,MAAZ,EAAoBsC,OAApB,CAA4B,UAAArH,GAAG,EAAI;EACjCvB,MAAAA,MAAM,CAACwQ,cAAP,CAAsBuJ,aAAtB,EAAqCxY,GAArC,EAA0C;EACxC0Y,QAAAA,YAAY,EAAE,KAD0B;EAExCxJ,QAAAA,UAAU,EAAE,IAF4B;EAGxCC,QAAAA,GAAG,EAAE,eAAM;EACTsJ,UAAAA,SAAS,CAACzY,GAAD,CAAT;EACA,iBAAO+E,MAAM,CAAC/E,GAAD,CAAb;EACD;EANuC,OAA1C;EAQD,KATD;;EAWA,QAAImV,gBAAgB,CAACwD,gBAAjB,IAAqCxD,gBAAgB,CAACyD,QAA1D,EAAoE;EAClEH,MAAAA,SAAS,CAAC,OAAD,CAAT;EACD;;EAED,WAAOD,aAAP;EACD,GA3OH;;EAAA,SA6OEK,aA7OF,GA6OE,uBACEpV,OADF,EAE+C;EAAA;;EAC7C,WAAO,IAAI0C,OAAJ,CAAY,UAACC,OAAD,EAAU0D,MAAV,EAAqB;EACtC,UAAMgP,WAAW,GAAG,MAAI,CAAC/Z,SAAL,CAAe,UAAAgG,MAAM,EAAI;EAC3C,YAAI,CAACA,MAAM,CAAClB,UAAZ,EAAwB;EACtBiV,UAAAA,WAAW;;EACX,cAAI/T,MAAM,CAACgB,OAAP,KAAkBtC,OAAlB,oBAAkBA,OAAO,CAAEuS,YAA3B,CAAJ,EAA6C;EAC3ClM,YAAAA,MAAM,CAAC/E,MAAM,CAAC0B,KAAR,CAAN;EACD,WAFD,MAEO;EACLL,YAAAA,OAAO,CAACrB,MAAD,CAAP;EACD;EACF;EACF,OATmB,CAApB;EAUD,KAXM,CAAP;EAYD,GA5PH;;EAAA,SA8PEgU,eA9PF,GA8PE,2BAAsE;EACpE,WAAO,KAAK/B,YAAZ;EACD,GAhQH;;EAAA,SAkQEjK,MAlQF,GAkQE,kBAAe;EACb,SAAK4D,MAAL,CAAYoE,aAAZ,GAA4BhI,MAA5B,CAAmC,KAAKiK,YAAxC;EACD,GApQH;;EAAA,SAsQE5I,OAtQF,GAsQE,iBACE3K,OADF,EAE+C;EAC7C,WAAO,KAAKkL,KAAL,cACFlL,OADE;EAEL8I,MAAAA,IAAI,EAAE;EAAE8F,QAAAA,WAAW,EAAE5O,OAAF,oBAAEA,OAAO,CAAE4O;EAAxB;EAFD,OAAP;EAID,GA7QH;;EAAA,SA+QE2G,eA/QF,GA+QE,yBACEvV,OADF,EAQ+C;EAAA;;EAC7C,QAAM0R,gBAAgB,GAAG,KAAKxE,MAAL,CAAYgG,2BAAZ,CAAwClT,OAAxC,CAAzB;EAEA,QAAMP,KAAK,GAAG,KAAKyN,MAAL,CACXoE,aADW,GAEXrE,KAFW,CAGV,KAAKC,MAHK,EAIVwE,gBAJU,CAAd;EAYA,WAAOjS,KAAK,CAACyL,KAAN,GAAcpI,IAAd,CAAmB;EAAA,aAAM,MAAI,CAAC8R,YAAL,CAAkBnV,KAAlB,EAAyBiS,gBAAzB,CAAN;EAAA,KAAnB,CAAP;EACD,GAvSH;;EAAA,SAySYxG,KAzSZ,GAySE,eACEC,YADF,EAE+C;EAAA;;EAC7C,WAAO,KAAKsI,YAAL,CAAkBtI,YAAlB,EAAgCrI,IAAhC,CAAqC,YAAM;EAChD,MAAA,MAAI,CAACuR,YAAL;;EACA,aAAO,MAAI,CAACQ,aAAZ;EACD,KAHM,CAAP;EAID,GAhTH;;EAAA,SAkTUpB,YAlTV,GAkTE,sBACEtI,YADF,EAEmC;EACjC;EACA,SAAK+I,WAAL,GAFiC;;EAKjC,QAAIjO,OAAwC,GAAG,KAAKsN,YAAL,CAAkBrI,KAAlB,CAC7C,KAAKlL,OADwC,EAE7CmL,YAF6C,CAA/C;;EAKA,QAAI,EAACA,YAAD,oBAACA,YAAY,CAAEoH,YAAf,CAAJ,EAAiC;EAC/BtM,MAAAA,OAAO,GAAGA,OAAO,CAAClD,KAAR,CAAclG,IAAd,CAAV;EACD;;EAED,WAAOoJ,OAAP;EACD,GAnUH;;EAAA,SAqUUqO,kBArUV,GAqUE,8BAAmC;EAAA;;EACjC,SAAKkB,iBAAL;;EAEA,QACE7Y,QAAQ,IACR,KAAKkY,aAAL,CAAmB1U,OADnB,IAEA,CAAClD,cAAc,CAAC,KAAK+C,OAAL,CAAa9B,SAAd,CAHjB,EAIE;EACA;EACD;;EAED,QAAMuX,IAAI,GAAGzX,cAAc,CACzB,KAAK6W,aAAL,CAAmB/K,aADM,EAEzB,KAAK9J,OAAL,CAAa9B,SAFY,CAA3B,CAXiC;EAiBjC;;EACA,QAAMuE,OAAO,GAAGgT,IAAI,GAAG,CAAvB;EAEA,SAAKC,cAAL,GAAsB9S,UAAU,CAAC,YAAM;EACrC,UAAI,CAAC,MAAI,CAACiS,aAAL,CAAmB1U,OAAxB,EAAiC;EAC/B,QAAA,MAAI,CAACkU,YAAL;EACD;EACF,KAJ+B,EAI7B5R,OAJ6B,CAAhC;EAKD,GA9VH;;EAAA,SAgWU+R,sBAhWV,GAgWE,kCAAiC;EAAA;;EAC/B,WAAO,OAAO,KAAKxU,OAAL,CAAa2V,eAApB,KAAwC,UAAxC,GACH,KAAK3V,OAAL,CAAa2V,eAAb,CAA6B,KAAKd,aAAL,CAAmBpL,IAAhD,EAAsD,KAAK8J,YAA3D,CADG,4BAEH,KAAKvT,OAAL,CAAa2V,eAFV,oCAE6B,KAFpC;EAGD,GApWH;;EAAA,SAsWUjB,qBAtWV,GAsWE,+BAA8BkB,YAA9B,EAAkE;EAAA;;EAChE,SAAKC,oBAAL;EAEA,SAAKpB,sBAAL,GAA8BmB,YAA9B;;EAEA,QACEjZ,QAAQ,IACR,KAAKqD,OAAL,CAAaqK,OAAb,KAAyB,KADzB,IAEA,CAACpN,cAAc,CAAC,KAAKwX,sBAAN,CAFf,IAGA,KAAKA,sBAAL,KAAgC,CAJlC,EAKE;EACA;EACD;;EAED,SAAKqB,iBAAL,GAAyBC,WAAW,CAAC,YAAM;EACzC,UACE,MAAI,CAAC/V,OAAL,CAAagW,2BAAb,IACA/R,YAAY,CAACJ,SAAb,EAFF,EAGE;EACA,QAAA,MAAI,CAAC4P,YAAL;EACD;EACF,KAPmC,EAOjC,KAAKgB,sBAP4B,CAApC;EAQD,GA5XH;;EAAA,SA8XUf,YA9XV,GA8XE,wBAA6B;EAC3B,SAAKY,kBAAL;EACA,SAAKI,qBAAL,CAA2B,KAAKF,sBAAL,EAA3B;EACD,GAjYH;;EAAA,SAmYUV,WAnYV,GAmYE,uBAA4B;EAC1B,SAAK0B,iBAAL;EACA,SAAKK,oBAAL;EACD,GAtYH;;EAAA,SAwYUL,iBAxYV,GAwYE,6BAAkC;EAChCnM,IAAAA,YAAY,CAAC,KAAKqM,cAAN,CAAZ;EACA,SAAKA,cAAL,GAAsBja,SAAtB;EACD,GA3YH;;EAAA,SA6YUoa,oBA7YV,GA6YE,gCAAqC;EACnCI,IAAAA,aAAa,CAAC,KAAKH,iBAAN,CAAb;EACA,SAAKA,iBAAL,GAAyBra,SAAzB;EACD,GAhZH;;EAAA,SAkZYmZ,YAlZZ,GAkZE,sBACEnV,KADF,EAEEO,OAFF,EASsC;EACpC,QAAMiU,SAAS,GAAG,KAAKV,YAAvB;EACA,QAAMS,WAAW,GAAG,KAAKhU,OAAzB;EACA,QAAMkW,UAAU,GAAG,KAAKrB,aAAxB;EACA,QAAMsB,eAAe,GAAG,KAAKC,kBAA7B;EACA,QAAMC,iBAAiB,GAAG,KAAKC,oBAA/B;EACA,QAAMC,WAAW,GAAG9W,KAAK,KAAKwU,SAA9B;EACA,QAAMuC,iBAAiB,GAAGD,WAAW,GACjC9W,KAAK,CAACe,KAD2B,GAEjC,KAAKiW,wBAFT;EAGA,QAAMC,eAAe,GAAGH,WAAW,GAC/B,KAAK1B,aAD0B,GAE/B,KAAK8B,mBAFT;EAVoC,QAc5BnW,KAd4B,GAclBf,KAdkB,CAc5Be,KAd4B;EAAA,QAe9BsJ,aAf8B,GAe+BtJ,KAf/B,CAe9BsJ,aAf8B;EAAA,QAef9G,KAfe,GAe+BxC,KAf/B,CAefwC,KAfe;EAAA,QAeR4J,cAfQ,GAe+BpM,KAf/B,CAeRoM,cAfQ;EAAA,QAeQxM,UAfR,GAe+BI,KAf/B,CAeQJ,UAfR;EAAA,QAeoBK,MAfpB,GAe+BD,KAf/B,CAeoBC,MAfpB;EAgBpC,QAAImW,cAAc,GAAG,KAArB;EACA,QAAIC,iBAAiB,GAAG,KAAxB;EACA,QAAIpN,IAAJ,CAlBoC;;EAqBpC,QAAIzJ,OAAO,CAAC8W,iBAAZ,EAA+B;EAC7B,UAAM3C,OAAO,GAAG,KAAKpY,YAAL,EAAhB;EAEA,UAAMgb,YAAY,GAAG,CAAC5C,OAAD,IAAYX,kBAAkB,CAAC/T,KAAD,EAAQO,OAAR,CAAnD;EAEA,UAAMgX,eAAe,GACnB7C,OAAO,IAAIC,qBAAqB,CAAC3U,KAAD,EAAQwU,SAAR,EAAmBjU,OAAnB,EAA4BgU,WAA5B,CADlC;;EAGA,UAAI+C,YAAY,IAAIC,eAApB,EAAqC;EACnC5W,QAAAA,UAAU,GAAG,IAAb;;EACA,YAAI,CAAC0J,aAAL,EAAoB;EAClBrJ,UAAAA,MAAM,GAAG,SAAT;EACD;EACF;EACF,KAnCmC;;;EAsCpC,QACET,OAAO,CAACiX,gBAAR,IACA,CAACzW,KAAK,CAACkM,eADP,KAEAgK,eAFA,oBAEAA,eAAe,CAAEQ,SAFjB,KAGAzW,MAAM,KAAK,OAJb,EAKE;EACAgJ,MAAAA,IAAI,GAAGiN,eAAe,CAACjN,IAAvB;EACAK,MAAAA,aAAa,GAAG4M,eAAe,CAAC5M,aAAhC;EACArJ,MAAAA,MAAM,GAAGiW,eAAe,CAACjW,MAAzB;EACAmW,MAAAA,cAAc,GAAG,IAAjB;EACD,KAVD;EAAA,SAYK,IAAI5W,OAAO,CAACmX,MAAR,IAAkB,OAAO3W,KAAK,CAACiJ,IAAb,KAAsB,WAA5C,EAAyD;EAC5D;EACA,YACEyM,UAAU,IACV1V,KAAK,CAACiJ,IAAN,MAAe0M,eAAf,oBAAeA,eAAe,CAAE1M,IAAhC,CADA,IAEAzJ,OAAO,CAACmX,MAAR,KAAmB,KAAKC,QAH1B,EAIE;EACA3N,UAAAA,IAAI,GAAG,KAAK4N,YAAZ;EACD,SAND,MAMO;EACL,cAAI;EACF,iBAAKD,QAAL,GAAgBpX,OAAO,CAACmX,MAAxB;EACA1N,YAAAA,IAAI,GAAGzJ,OAAO,CAACmX,MAAR,CAAe3W,KAAK,CAACiJ,IAArB,CAAP;;EACA,gBAAIzJ,OAAO,CAAC2J,iBAAR,KAA8B,KAAlC,EAAyC;EACvCF,cAAAA,IAAI,GAAG9H,gBAAgB,CAACuU,UAAD,oBAACA,UAAU,CAAEzM,IAAb,EAAmBA,IAAnB,CAAvB;EACD;;EACD,iBAAK4N,YAAL,GAAoB5N,IAApB;EACA,iBAAK4J,WAAL,GAAmB,IAAnB;EACD,WARD,CAQE,OAAOA,WAAP,EAAoB;EACpBnL,YAAAA,SAAS,GAAGlF,KAAZ,CAAkBqQ,WAAlB;EACA,iBAAKA,WAAL,GAAmBA,WAAnB;EACD;EACF;EACF,OAtBI;EAAA,WAwBA;EACH5J,UAAAA,IAAI,GAAIjJ,KAAK,CAACiJ,IAAd;EACD,SA5EmC;;;EA+EpC,QACE,OAAOzJ,OAAO,CAACsX,eAAf,KAAmC,WAAnC,IACA,OAAO7N,IAAP,KAAgB,WADhB,KAEChJ,MAAM,KAAK,SAAX,IAAwBA,MAAM,KAAK,MAFpC,CADF,EAIE;EACA,UAAI6W,eAAJ,CADA;;EAIA,UACE,CAAApB,UAAU,QAAV,YAAAA,UAAU,CAAEW,iBAAZ,KACA7W,OAAO,CAACsX,eAAR,MAA4BjB,iBAA5B,oBAA4BA,iBAAiB,CAAEiB,eAA/C,CAFF,EAGE;EACAA,QAAAA,eAAe,GAAGpB,UAAU,CAACzM,IAA7B;EACD,OALD,MAKO;EACL6N,QAAAA,eAAe,GACb,OAAOtX,OAAO,CAACsX,eAAf,KAAmC,UAAnC,GACKtX,OAAO,CAACsX,eAAT,EADJ,GAEItX,OAAO,CAACsX,eAHd;;EAIA,YAAItX,OAAO,CAACmX,MAAR,IAAkB,OAAOG,eAAP,KAA2B,WAAjD,EAA8D;EAC5D,cAAI;EACFA,YAAAA,eAAe,GAAGtX,OAAO,CAACmX,MAAR,CAAeG,eAAf,CAAlB;;EACA,gBAAItX,OAAO,CAAC2J,iBAAR,KAA8B,KAAlC,EAAyC;EACvC2N,cAAAA,eAAe,GAAG3V,gBAAgB,CAChCuU,UADgC,oBAChCA,UAAU,CAAEzM,IADoB,EAEhC6N,eAFgC,CAAlC;EAID;;EACD,iBAAKjE,WAAL,GAAmB,IAAnB;EACD,WATD,CASE,OAAOA,WAAP,EAAoB;EACpBnL,YAAAA,SAAS,GAAGlF,KAAZ,CAAkBqQ,WAAlB;EACA,iBAAKA,WAAL,GAAmBA,WAAnB;EACD;EACF;EACF;;EAED,UAAI,OAAOiE,eAAP,KAA2B,WAA/B,EAA4C;EAC1C7W,QAAAA,MAAM,GAAG,SAAT;EACAgJ,QAAAA,IAAI,GAAG6N,eAAP;EACAT,QAAAA,iBAAiB,GAAG,IAApB;EACD;EACF;;EAED,QAAI,KAAKxD,WAAT,EAAsB;EACpBrQ,MAAAA,KAAK,GAAG,KAAKqQ,WAAb;EACA5J,MAAAA,IAAI,GAAG,KAAK4N,YAAZ;EACAzK,MAAAA,cAAc,GAAGvO,IAAI,CAACC,GAAL,EAAjB;EACAmC,MAAAA,MAAM,GAAG,OAAT;EACD;;EAED,QAAMa,MAA8C,GAAG;EACrDb,MAAAA,MAAM,EAANA,MADqD;EAErD8W,MAAAA,SAAS,EAAE9W,MAAM,KAAK,SAF+B;EAGrDyW,MAAAA,SAAS,EAAEzW,MAAM,KAAK,SAH+B;EAIrD6B,MAAAA,OAAO,EAAE7B,MAAM,KAAK,OAJiC;EAKrD+W,MAAAA,MAAM,EAAE/W,MAAM,KAAK,MALkC;EAMrDgJ,MAAAA,IAAI,EAAJA,IANqD;EAOrDK,MAAAA,aAAa,EAAbA,aAPqD;EAQrD9G,MAAAA,KAAK,EAALA,KARqD;EASrD4J,MAAAA,cAAc,EAAdA,cATqD;EAUrDjI,MAAAA,YAAY,EAAEnE,KAAK,CAACqM,iBAViC;EAWrDF,MAAAA,gBAAgB,EAAEnM,KAAK,CAACmM,gBAX6B;EAYrD8K,MAAAA,SAAS,EAAEjX,KAAK,CAACkM,eAAN,GAAwB,CAAxB,IAA6BlM,KAAK,CAACmM,gBAAN,GAAyB,CAZZ;EAarD+K,MAAAA,mBAAmB,EACjBlX,KAAK,CAACkM,eAAN,GAAwB8J,iBAAiB,CAAC9J,eAA1C,IACAlM,KAAK,CAACmM,gBAAN,GAAyB6J,iBAAiB,CAAC7J,gBAfQ;EAgBrDvM,MAAAA,UAAU,EAAVA,UAhBqD;EAiBrDuX,MAAAA,YAAY,EAAEvX,UAAU,IAAIK,MAAM,KAAK,SAjBc;EAkBrDmX,MAAAA,cAAc,EAAEnX,MAAM,KAAK,OAAX,IAAsBD,KAAK,CAACsJ,aAAN,KAAwB,CAlBT;EAmBrD+M,MAAAA,iBAAiB,EAAjBA,iBAnBqD;EAoBrDD,MAAAA,cAAc,EAAdA,cApBqD;EAqBrDiB,MAAAA,cAAc,EAAEpX,MAAM,KAAK,OAAX,IAAsBD,KAAK,CAACsJ,aAAN,KAAwB,CArBT;EAsBrD3J,MAAAA,OAAO,EAAEA,OAAO,CAACV,KAAD,EAAQO,OAAR,CAtBqC;EAuBrD2K,MAAAA,OAAO,EAAE,KAAKA,OAvBuC;EAwBrDrB,MAAAA,MAAM,EAAE,KAAKA;EAxBwC,KAAvD;EA2BA,WAAOhI,MAAP;EACD,GAvjBH;;EAAA,SAyjBUwW,qBAzjBV,GAyjBE,+BACExW,MADF,EAEE4U,UAFF,EAGW;EACT,QAAI,CAACA,UAAL,EAAiB;EACf,aAAO,IAAP;EACD;;EAHQ,wBAKsD,KAAKlW,OAL3D;EAAA,QAKD+X,mBALC,iBAKDA,mBALC;EAAA,QAKoBC,6BALpB,iBAKoBA,6BALpB;;EAOT,QAAI,CAACD,mBAAD,IAAwB,CAACC,6BAA7B,EAA4D;EAC1D,aAAO,IAAP;EACD;;EAED,QAAID,mBAAmB,KAAK,SAAxB,IAAqC,CAAC,KAAK3E,YAAL,CAAkBpX,MAA5D,EAAoE;EAClE,aAAO,IAAP;EACD;;EAED,QAAMic,aAAa,GACjBF,mBAAmB,KAAK,SAAxB,GACI,KAAK3E,YADT,GAEI2E,mBAHN;EAKA,WAAO/c,MAAM,CAACmG,IAAP,CAAYG,MAAZ,EAAoBI,IAApB,CAAyB,UAAAnF,GAAG,EAAI;EACrC,UAAM2b,QAAQ,GAAG3b,GAAjB;EACA,UAAM4b,OAAO,GAAG7W,MAAM,CAAC4W,QAAD,CAAN,KAAqBhC,UAAU,CAACgC,QAAD,CAA/C;EACA,UAAME,UAAU,GAAGH,aAAH,oBAAGA,aAAa,CAAEvW,IAAf,CAAoB,UAAA7F,CAAC;EAAA,eAAIA,CAAC,KAAKU,GAAV;EAAA,OAArB,CAAnB;EACA,UAAM8b,UAAU,GAAGL,6BAAH,oBAAGA,6BAA6B,CAAEtW,IAA/B,CAAoC,UAAA7F,CAAC;EAAA,eAAIA,CAAC,KAAKU,GAAV;EAAA,OAArC,CAAnB;EACA,aAAO4b,OAAO,IAAI,CAACE,UAAZ,KAA2B,CAACJ,aAAD,IAAkBG,UAA7C,CAAP;EACD,KANM,CAAP;EAOD,GAvlBH;;EAAA,SAylBE/D,YAzlBF,GAylBE,sBAAaN,aAAb,EAAkD;EAChD,QAAMmC,UAAU,GAAG,KAAKrB,aAAxB;EAIA,SAAKA,aAAL,GAAqB,KAAKD,YAAL,CAAkB,KAAKrB,YAAvB,EAAqC,KAAKvT,OAA1C,CAArB;EACA,SAAKoW,kBAAL,GAA0B,KAAK7C,YAAL,CAAkB/S,KAA5C;EACA,SAAK8V,oBAAL,GAA4B,KAAKtW,OAAjC,CAPgD;;EAUhD,QAAIgC,mBAAmB,CAAC,KAAK6S,aAAN,EAAqBqB,UAArB,CAAvB,EAAyD;EACvD;EACD,KAZ+C;;;EAehD,QAAMoC,oBAAmC,GAAG;EAAE3P,MAAAA,KAAK,EAAE;EAAT,KAA5C;;EAEA,QACE,CAAAoL,aAAa,QAAb,YAAAA,aAAa,CAAE1Y,SAAf,MAA6B,KAA7B,IACA,KAAKyc,qBAAL,CAA2B,KAAKjD,aAAhC,EAA+CqB,UAA/C,CAFF,EAGE;EACAoC,MAAAA,oBAAoB,CAACjd,SAArB,GAAiC,IAAjC;EACD;;EAED,SAAKyP,MAAL,cAAiBwN,oBAAjB,EAA0CvE,aAA1C;EACD,GAlnBH;;EAAA,SAonBUG,WApnBV,GAonBE,uBAA4B;EAC1B,QAAMzU,KAAK,GAAG,KAAKyN,MAAL,CACXoE,aADW,GAEXrE,KAFW,CAGV,KAAKC,MAHK,EAIV,KAAKlN,OAJK,CAAd;;EAYA,QAAIP,KAAK,KAAK,KAAK8T,YAAnB,EAAiC;EAC/B;EACD;;EAED,QAAMU,SAAS,GAAG,KAAKV,YAAvB;EACA,SAAKA,YAAL,GAAoB9T,KAApB;EACA,SAAKgX,wBAAL,GAAgChX,KAAK,CAACe,KAAtC;EACA,SAAKmW,mBAAL,GAA2B,KAAK9B,aAAhC;;EAEA,QAAI,KAAK9Y,YAAL,EAAJ,EAAyB;EACvBkY,MAAAA,SAAS,QAAT,YAAAA,SAAS,CAAElJ,cAAX,CAA0B,IAA1B;EACAtL,MAAAA,KAAK,CAACoL,WAAN,CAAkB,IAAlB;EACD;EACF,GA9oBH;;EAAA,SAgpBEwB,aAhpBF,GAgpBE,uBAAcF,MAAd,EAAmD;EACjD,QAAM4H,aAA4B,GAAG,EAArC;;EAEA,QAAI5H,MAAM,CAACtC,IAAP,KAAgB,SAApB,EAA+B;EAC7BkK,MAAAA,aAAa,CAAC3N,SAAd,GAA0B,IAA1B;EACD,KAFD,MAEO,IAAI+F,MAAM,CAACtC,IAAP,KAAgB,OAAhB,IAA2B,CAAC3E,gBAAgB,CAACiH,MAAM,CAACnJ,KAAR,CAAhD,EAAgE;EACrE+Q,MAAAA,aAAa,CAACzN,OAAd,GAAwB,IAAxB;EACD;;EAED,SAAK+N,YAAL,CAAkBN,aAAlB;;EAEA,QAAI,KAAKhY,YAAL,EAAJ,EAAyB;EACvB,WAAK2X,YAAL;EACD;EACF,GA9pBH;;EAAA,SAgqBU5I,MAhqBV,GAgqBE,gBAAeiJ,aAAf,EAAmD;EAAA;;EACjDhM,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB;EACA,UAAIuM,aAAa,CAAC3N,SAAlB,EAA6B;EAC3B,QAAA,MAAI,CAACpG,OAAL,CAAaoG,SAAb,oBAAA,MAAI,CAACpG,OAAL,CAAaoG,SAAb,CAAyB,MAAI,CAACyO,aAAL,CAAmBpL,IAA5C;EACA,QAAA,MAAI,CAACzJ,OAAL,CAAamO,SAAb,oBAAA,MAAI,CAACnO,OAAL,CAAamO,SAAb,CAAyB,MAAI,CAAC0G,aAAL,CAAmBpL,IAA5C,EAAmD,IAAnD;EACD,OAHD,MAGO,IAAIsK,aAAa,CAACzN,OAAlB,EAA2B;EAChC,QAAA,MAAI,CAACtG,OAAL,CAAasG,OAAb,oBAAA,MAAI,CAACtG,OAAL,CAAasG,OAAb,CAAuB,MAAI,CAACuO,aAAL,CAAmB7R,KAA1C;EACA,QAAA,MAAI,CAAChD,OAAL,CAAamO,SAAb,oBAAA,MAAI,CAACnO,OAAL,CAAamO,SAAb,CAAyB1S,SAAzB,EAAoC,MAAI,CAACoZ,aAAL,CAAmB7R,KAAvD;EACD,OARuB;;;EAWxB,UAAI+Q,aAAa,CAAC1Y,SAAlB,EAA6B;EAC3B,QAAA,MAAI,CAACA,SAAL,CAAeuI,OAAf,CAAuB,UAAArI,QAAQ,EAAI;EACjCA,UAAAA,QAAQ,CAAC,MAAI,CAACsZ,aAAN,CAAR;EACD,SAFD;EAGD,OAfuB;;;EAkBxB,UAAId,aAAa,CAACpL,KAAlB,EAAyB;EACvB,QAAA,MAAI,CAACuE,MAAL,CACGoE,aADH,GAEGxG,MAFH,CAEU;EAAErL,UAAAA,KAAK,EAAE,MAAI,CAAC8T,YAAd;EAA4B1J,UAAAA,IAAI,EAAE;EAAlC,SAFV;EAGD;EACF,KAvBD;EAwBD,GAzrBH;;EAAA;EAAA,EAMUzO,YANV;;EA4rBA,SAASmd,iBAAT,CACE9Y,KADF,EAEEO,OAFF,EAGW;EACT,SACEA,OAAO,CAACqK,OAAR,KAAoB,KAApB,IACA,CAAC5K,KAAK,CAACe,KAAN,CAAYsJ,aADb,IAEA,EAAErK,KAAK,CAACe,KAAN,CAAYC,MAAZ,KAAuB,OAAvB,IAAkCT,OAAO,CAACwY,YAAR,KAAyB,KAA7D,CAHF;EAKD;;EAED,SAAShF,kBAAT,CACE/T,KADF,EAEEO,OAFF,EAGW;EACT,SACEuY,iBAAiB,CAAC9Y,KAAD,EAAQO,OAAR,CAAjB,IACCP,KAAK,CAACe,KAAN,CAAYsJ,aAAZ,GAA4B,CAA5B,IACC6J,aAAa,CAAClU,KAAD,EAAQO,OAAR,EAAiBA,OAAO,CAACyY,cAAzB,CAHjB;EAKD;;EAED,SAAS9E,aAAT,CACElU,KADF,EAEEO,OAFF,EAGE0Y,KAHF,EAME;EACA,MAAI1Y,OAAO,CAACqK,OAAR,KAAoB,KAAxB,EAA+B;EAC7B,QAAMnN,KAAK,GAAG,OAAOwb,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAACjZ,KAAD,CAAnC,GAA6CiZ,KAA3D;EAEA,WAAOxb,KAAK,KAAK,QAAV,IAAuBA,KAAK,KAAK,KAAV,IAAmBiD,OAAO,CAACV,KAAD,EAAQO,OAAR,CAAxD;EACD;;EACD,SAAO,KAAP;EACD;;EAED,SAASoU,qBAAT,CACE3U,KADF,EAEEwU,SAFF,EAGEjU,OAHF,EAIEgU,WAJF,EAKW;EACT,SACEhU,OAAO,CAACqK,OAAR,KAAoB,KAApB,KACC5K,KAAK,KAAKwU,SAAV,IAAuBD,WAAW,CAAC3J,OAAZ,KAAwB,KADhD,MAEC,CAACrK,OAAO,CAACmV,QAAT,IAAqB1V,KAAK,CAACe,KAAN,CAAYC,MAAZ,KAAuB,OAF7C,KAGAN,OAAO,CAACV,KAAD,EAAQO,OAAR,CAJT;EAMD;;EAED,SAASG,OAAT,CACEV,KADF,EAEEO,OAFF,EAGW;EACT,SAAOP,KAAK,CAAC+K,aAAN,CAAoBxK,OAAO,CAAC9B,SAA5B,CAAP;EACD;;MCrxBYya,eAAb;EAAA;;EAOE,2BAAYzL,MAAZ,EAAiCH,OAAjC,EAAmE;EAAA;;EACjE;EAEA,UAAKG,MAAL,GAAcA,MAAd;EACA,UAAKH,OAAL,GAAe,EAAf;EACA,UAAKzL,MAAL,GAAc,EAAd;EACA,UAAKoH,SAAL,GAAiB,EAAjB;EACA,UAAKkQ,YAAL,GAAoB,EAApB;;EAEA,QAAI7L,OAAJ,EAAa;EACX,YAAK8L,UAAL,CAAgB9L,OAAhB;EACD;;EAXgE;EAYlE;;EAnBH;;EAAA,SAqBYpR,WArBZ,GAqBE,uBAA8B;EAAA;;EAC5B,QAAI,KAAKN,SAAL,CAAeW,MAAf,KAA0B,CAA9B,EAAiC;EAC/B,WAAK0M,SAAL,CAAe9E,OAAf,CAAuB,UAAAwG,QAAQ,EAAI;EACjCA,QAAAA,QAAQ,CAAC9O,SAAT,CAAmB,UAAAgG,MAAM,EAAI;EAC3B,UAAA,MAAI,CAACwX,QAAL,CAAc1O,QAAd,EAAwB9I,MAAxB;EACD,SAFD;EAGD,OAJD;EAKD;EACF,GA7BH;;EAAA,SA+BYxF,aA/BZ,GA+BE,yBAAgC;EAC9B,QAAI,CAAC,KAAKT,SAAL,CAAeW,MAApB,EAA4B;EAC1B,WAAKkO,OAAL;EACD;EACF,GAnCH;;EAAA,SAqCEA,OArCF,GAqCE,mBAAgB;EACd,SAAK7O,SAAL,GAAiB,EAAjB;EACA,SAAKqN,SAAL,CAAe9E,OAAf,CAAuB,UAAAwG,QAAQ,EAAI;EACjCA,MAAAA,QAAQ,CAACF,OAAT;EACD,KAFD;EAGD,GA1CH;;EAAA,SA4CE2O,UA5CF,GA4CE,oBACE9L,OADF,EAEEgH,aAFF,EAGQ;EACN,SAAKhH,OAAL,GAAeA,OAAf;EACA,SAAKgM,eAAL,CAAqBhF,aAArB;EACD,GAlDH;;EAAA,SAoDExJ,gBApDF,GAoDE,4BAA0C;EACxC,WAAO,KAAKjJ,MAAZ;EACD,GAtDH;;EAAA,SAwDEqT,mBAxDF,GAwDE,6BAAoB5H,OAApB,EAA4E;EAC1E,WAAO,KAAKiM,qBAAL,CAA2BjM,OAA3B,EAAoCwE,GAApC,CAAwC,UAAA0H,KAAK;EAAA,aAClDA,KAAK,CAAC7O,QAAN,CAAeuK,mBAAf,CAAmCsE,KAAK,CAACC,qBAAzC,CADkD;EAAA,KAA7C,CAAP;EAGD,GA5DH;;EAAA,SA8DUF,qBA9DV,GA8DE,+BACEjM,OADF,EAEwB;EAAA;;EACtB,QAAMoM,aAAa,GAAG,KAAKzQ,SAA3B;EACA,QAAMwQ,qBAAqB,GAAGnM,OAAO,CAACwE,GAAR,CAAY,UAAAvR,OAAO;EAAA,aAC/C,MAAI,CAACkN,MAAL,CAAYgG,2BAAZ,CAAwClT,OAAxC,CAD+C;EAAA,KAAnB,CAA9B;EAIA,QAAMoZ,iBAAuC,GAAGF,qBAAqB,CAACG,OAAtB,CAC9C,UAAA3H,gBAAgB,EAAI;EAClB,UAAMuH,KAAK,GAAGE,aAAa,CAAC1O,IAAd,CACZ,UAAAL,QAAQ;EAAA,eAAIA,QAAQ,CAACpK,OAAT,CAAiBF,SAAjB,KAA+B4R,gBAAgB,CAAC5R,SAApD;EAAA,OADI,CAAd;;EAGA,UAAImZ,KAAK,IAAI,IAAb,EAAmB;EACjB,eAAO,CAAC;EAAEC,UAAAA,qBAAqB,EAAExH,gBAAzB;EAA2CtH,UAAAA,QAAQ,EAAE6O;EAArD,SAAD,CAAP;EACD;;EACD,aAAO,EAAP;EACD,KAT6C,CAAhD;EAYA,QAAMK,kBAAkB,GAAGF,iBAAiB,CAAC7H,GAAlB,CACzB,UAAA0H,KAAK;EAAA,aAAIA,KAAK,CAACC,qBAAN,CAA4BpZ,SAAhC;EAAA,KADoB,CAA3B;EAGA,QAAMyZ,gBAAgB,GAAGL,qBAAqB,CAACtd,MAAtB,CACvB,UAAA8V,gBAAgB;EAAA,aACd,CAAC4H,kBAAkB,CAACvV,QAAnB,CAA4B2N,gBAAgB,CAAC5R,SAA7C,CADa;EAAA,KADO,CAAzB;EAKA,QAAM0Z,kBAAkB,GAAGL,aAAa,CAACvd,MAAd,CACzB,UAAA6d,YAAY;EAAA,aACV,CAACL,iBAAiB,CAAC1X,IAAlB,CAAuB,UAAAuX,KAAK;EAAA,eAAIA,KAAK,CAAC7O,QAAN,KAAmBqP,YAAvB;EAAA,OAA5B,CADS;EAAA,KADa,CAA3B;EAKA,QAAMC,oBAA0C,GAAGH,gBAAgB,CAAChI,GAAjB,CACjD,UAACvR,OAAD,EAAUnC,KAAV,EAAoB;EAClB,UAAImC,OAAO,CAACiX,gBAAZ,EAA8B;EAC5B;EACA,YAAM0C,sBAAsB,GAAGH,kBAAkB,CAAC3b,KAAD,CAAjD;;EACA,YAAI8b,sBAAsB,KAAKle,SAA/B,EAA0C;EACxC,iBAAO;EACLyd,YAAAA,qBAAqB,EAAElZ,OADlB;EAELoK,YAAAA,QAAQ,EAAEuP;EAFL,WAAP;EAID;EACF;;EACD,aAAO;EACLT,QAAAA,qBAAqB,EAAElZ,OADlB;EAELoK,QAAAA,QAAQ,EAAE,MAAI,CAACwP,WAAL,CAAiB5Z,OAAjB;EAFL,OAAP;EAID,KAhBgD,CAAnD;;EAmBA,QAAM6Z,2BAA2B,GAAG,SAA9BA,2BAA8B,CAClCtY,CADkC,EAElCC,CAFkC;EAAA,aAIlC0X,qBAAqB,CAACxb,OAAtB,CAA8B6D,CAAC,CAAC2X,qBAAhC,IACAA,qBAAqB,CAACxb,OAAtB,CAA8B8D,CAAC,CAAC0X,qBAAhC,CALkC;EAAA,KAApC;;EAOA,WAAOE,iBAAiB,CACrBU,MADI,CACGJ,oBADH,EAEJtY,IAFI,CAECyY,2BAFD,CAAP;EAGD,GA5HH;;EAAA,SA8HUD,WA9HV,GA8HE,qBAAoB5Z,OAApB,EAAkE;EAChE,QAAM0R,gBAAgB,GAAG,KAAKxE,MAAL,CAAYgG,2BAAZ,CAAwClT,OAAxC,CAAzB;EACA,QAAM+Z,eAAe,GAAG,KAAKnB,YAAL,CAAkBlH,gBAAgB,CAAC5R,SAAnC,CAAxB;EACA,WAAOia,eAAP,WAAOA,eAAP,GAA0B,IAAI5G,aAAJ,CAAkB,KAAKjG,MAAvB,EAA+BwE,gBAA/B,CAA1B;EACD,GAlIH;;EAAA,SAoIUqH,eApIV,GAoIE,yBAAwBhF,aAAxB,EAA6D;EAAA;;EAC3DhM,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB,UAAM2R,aAAa,GAAG,MAAI,CAACzQ,SAA3B;;EAEA,UAAMsR,kBAAkB,GAAG,MAAI,CAAChB,qBAAL,CAA2B,MAAI,CAACjM,OAAhC,CAA3B,CAHwB;;;EAMxBiN,MAAAA,kBAAkB,CAACpW,OAAnB,CAA2B,UAAAqV,KAAK;EAAA,eAC9BA,KAAK,CAAC7O,QAAN,CAAe3B,UAAf,CAA0BwQ,KAAK,CAACC,qBAAhC,EAAuDnF,aAAvD,CAD8B;EAAA,OAAhC;EAIA,UAAMkG,YAAY,GAAGD,kBAAkB,CAACzI,GAAnB,CAAuB,UAAA0H,KAAK;EAAA,eAAIA,KAAK,CAAC7O,QAAV;EAAA,OAA5B,CAArB;EACA,UAAM8P,eAAe,GAAGlf,MAAM,CAACmf,WAAP,CACtBF,YAAY,CAAC1I,GAAb,CAAiB,UAAAnH,QAAQ;EAAA,eAAI,CAACA,QAAQ,CAACpK,OAAT,CAAiBF,SAAlB,EAA6BsK,QAA7B,CAAJ;EAAA,OAAzB,CADsB,CAAxB;EAGA,UAAMgQ,SAAS,GAAGH,YAAY,CAAC1I,GAAb,CAAiB,UAAAnH,QAAQ;EAAA,eACzCA,QAAQ,CAACG,gBAAT,EADyC;EAAA,OAAzB,CAAlB;EAIA,UAAM8P,cAAc,GAAGJ,YAAY,CAACvY,IAAb,CACrB,UAAC0I,QAAD,EAAWvM,KAAX;EAAA,eAAqBuM,QAAQ,KAAK+O,aAAa,CAACtb,KAAD,CAA/C;EAAA,OADqB,CAAvB;;EAGA,UAAIsb,aAAa,CAACnd,MAAd,KAAyBie,YAAY,CAACje,MAAtC,IAAgD,CAACqe,cAArD,EAAqE;EACnE;EACD;;EAED,MAAA,MAAI,CAAC3R,SAAL,GAAiBuR,YAAjB;EACA,MAAA,MAAI,CAACrB,YAAL,GAAoBsB,eAApB;EACA,MAAA,MAAI,CAAC5Y,MAAL,GAAc8Y,SAAd;;EAEA,UAAI,CAAC,MAAI,CAACre,YAAL,EAAL,EAA0B;EACxB;EACD;;EAEDwB,MAAAA,UAAU,CAAC4b,aAAD,EAAgBc,YAAhB,CAAV,CAAwCrW,OAAxC,CAAgD,UAAAwG,QAAQ,EAAI;EAC1DA,QAAAA,QAAQ,CAACF,OAAT;EACD,OAFD;EAIA3M,MAAAA,UAAU,CAAC0c,YAAD,EAAed,aAAf,CAAV,CAAwCvV,OAAxC,CAAgD,UAAAwG,QAAQ,EAAI;EAC1DA,QAAAA,QAAQ,CAAC9O,SAAT,CAAmB,UAAAgG,MAAM,EAAI;EAC3B,UAAA,MAAI,CAACwX,QAAL,CAAc1O,QAAd,EAAwB9I,MAAxB;EACD,SAFD;EAGD,OAJD;;EAMA,MAAA,MAAI,CAACwJ,MAAL;EACD,KA5CD;EA6CD,GAlLH;;EAAA,SAoLUgO,QApLV,GAoLE,kBAAiB1O,QAAjB,EAA0C9I,MAA1C,EAA6E;EAC3E,QAAMzD,KAAK,GAAG,KAAK6K,SAAL,CAAehL,OAAf,CAAuB0M,QAAvB,CAAd;;EACA,QAAIvM,KAAK,KAAK,CAAC,CAAf,EAAkB;EAChB,WAAKyD,MAAL,GAAc3D,SAAS,CAAC,KAAK2D,MAAN,EAAczD,KAAd,EAAqByD,MAArB,CAAvB;EACA,WAAKwJ,MAAL;EACD;EACF,GA1LH;;EAAA,SA4LUA,MA5LV,GA4LE,kBAAuB;EAAA;;EACrB/C,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB,MAAA,MAAI,CAACnM,SAAL,CAAeuI,OAAf,CAAuB,UAAArI,QAAQ,EAAI;EACjCA,QAAAA,QAAQ,CAAC,MAAI,CAAC+F,MAAN,CAAR;EACD,OAFD;EAGD,KAJD;EAKD,GAlMH;;EAAA;EAAA,EAAqClG,YAArC;;MCeakf,qBAAb;EAAA;;EAWE;EAKA;EAGA;EAKA;EACA,iCACEpN,MADF,EAEElN,OAFF,EAQE;EAAA,WACA,0BAAMkN,MAAN,EAAclN,OAAd,CADA;EAED;;EAnCH;;EAAA,SAqCYsT,WArCZ,GAqCE,uBAA8B;EAC5B,6BAAMA,WAAN;;EACA,SAAKiH,aAAL,GAAqB,KAAKA,aAAL,CAAmBrO,IAAnB,CAAwB,IAAxB,CAArB;EACA,SAAKsO,iBAAL,GAAyB,KAAKA,iBAAL,CAAuBtO,IAAvB,CAA4B,IAA5B,CAAzB;EACD,GAzCH;;EAAA,SA2CEzD,UA3CF,GA2CE,oBACEzI,OADF,EAOE+T,aAPF,EAQQ;EACN,6BAAMtL,UAAN,yBAEOzI,OAFP;EAGI8L,MAAAA,QAAQ,EAAE6C,qBAAqB;EAHnC,QAKEoF,aALF;EAOD,GA3DH;;EAAA,SA6DEY,mBA7DF,GA6DE,6BACE3U,OADF,EAO8C;EAC5CA,IAAAA,OAAO,CAAC8L,QAAR,GAAmB6C,qBAAqB,EAAxC;EACA,oCAAagG,mBAAb,YAAiC3U,OAAjC;EAID,GA1EH;;EAAA,SA4EEua,aA5EF,GA4EE,uBACEva,OADF,EAEuD;EAAA;;EACrD,WAAO,KAAKkL,KAAL,CAAW;EAChB;EACAE,MAAAA,aAAa,2BAAEpL,OAAF,oBAAEA,OAAO,CAAEoL,aAAX,oCAA4B,IAFzB;EAGhBmH,MAAAA,YAAY,EAAEvS,OAAF,oBAAEA,OAAO,CAAEuS,YAHP;EAIhBzJ,MAAAA,IAAI,EAAE;EACJ+F,QAAAA,SAAS,EAAE;EAAEE,UAAAA,SAAS,EAAE,SAAb;EAAwBxD,UAAAA,SAAS,EAAEvL,OAAF,oBAAEA,OAAO,CAAEuL;EAA5C;EADP;EAJU,KAAX,CAAP;EAQD,GAvFH;;EAAA,SAyFEiP,iBAzFF,GAyFE,2BACExa,OADF,EAEuD;EAAA;;EACrD,WAAO,KAAKkL,KAAL,CAAW;EAChB;EACAE,MAAAA,aAAa,4BAAEpL,OAAF,oBAAEA,OAAO,CAAEoL,aAAX,qCAA4B,IAFzB;EAGhBmH,MAAAA,YAAY,EAAEvS,OAAF,oBAAEA,OAAO,CAAEuS,YAHP;EAIhBzJ,MAAAA,IAAI,EAAE;EACJ+F,QAAAA,SAAS,EAAE;EAAEE,UAAAA,SAAS,EAAE,UAAb;EAAyBxD,UAAAA,SAAS,EAAEvL,OAAF,oBAAEA,OAAO,CAAEuL;EAA7C;EADP;EAJU,KAAX,CAAP;EAQD,GApGH;;EAAA,SAsGYqJ,YAtGZ,GAsGE,sBACEnV,KADF,EAEEO,OAFF,EAQ8C;EAAA;;EAAA,QACpCQ,KADoC,GAC1Bf,KAD0B,CACpCe,KADoC;;EAE5C,QAAMc,MAAM,4BAASsT,YAAT,YAAsBnV,KAAtB,EAA6BO,OAA7B,CAAZ;;EACA,wBACKsB,MADL;EAEEiZ,MAAAA,aAAa,EAAE,KAAKA,aAFtB;EAGEC,MAAAA,iBAAiB,EAAE,KAAKA,iBAH1B;EAIElK,MAAAA,WAAW,EAAEA,WAAW,CAACtQ,OAAD,iBAAUQ,KAAK,CAACiJ,IAAhB,qBAAU,YAAYyF,KAAtB,CAJ1B;EAKEsB,MAAAA,eAAe,EAAEA,eAAe,CAACxQ,OAAD,kBAAUQ,KAAK,CAACiJ,IAAhB,qBAAU,aAAYyF,KAAtB,CALlC;EAMEJ,MAAAA,kBAAkB,EAChBtO,KAAK,CAACJ,UAAN,IAAoB,qBAAAI,KAAK,CAACyL,SAAN,+DAAiB4C,SAAjB,2CAA4BE,SAA5B,MAA0C,SAPlE;EAQEC,MAAAA,sBAAsB,EACpBxO,KAAK,CAACJ,UAAN,IACA,sBAAAI,KAAK,CAACyL,SAAN,gEAAiB4C,SAAjB,2CAA4BE,SAA5B,MAA0C;EAV9C;EAYD,GA7HH;;EAAA;EAAA,EAKUoE,aALV;;ECDA;MAEasH,gBAAb;EAAA;;EAoBE,4BACEvN,MADF,EAEElN,OAFF,EAGE;EAAA;;EACA;EAEA,UAAKkN,MAAL,GAAcA,MAAd;;EACA,UAAKzE,UAAL,CAAgBzI,OAAhB;;EACA,UAAKsT,WAAL;;EACA,UAAKe,YAAL;;EANA;EAOD;;EA9BH;;EAAA,SAgCYf,WAhCZ,GAgCE,uBAA8B;EAC5B,SAAKoH,MAAL,GAAc,KAAKA,MAAL,CAAYxO,IAAZ,CAAiB,IAAjB,CAAd;EACA,SAAK/B,KAAL,GAAa,KAAKA,KAAL,CAAW+B,IAAX,CAAgB,IAAhB,CAAb;EACD,GAnCH;;EAAA,SAqCEzD,UArCF,GAqCE,oBACEzI,OADF,EAEE;EACA,SAAKA,OAAL,GAAe,KAAKkN,MAAL,CAAYqB,sBAAZ,CAAmCvO,OAAnC,CAAf;EACD,GAzCH;;EAAA,SA2CYlE,aA3CZ,GA2CE,yBAAgC;EAC9B,QAAI,CAAC,KAAKT,SAAL,CAAeW,MAApB,EAA4B;EAAA;;EAC1B,oCAAK2e,eAAL,2CAAsB5P,cAAtB,CAAqC,IAArC;EACD;EACF,GA/CH;;EAAA,SAiDEqD,gBAjDF,GAiDE,0BAAiBjC,MAAjB,EAA4E;EAC1E,SAAKkI,YAAL,GAD0E;;EAI1E,QAAMN,aAA4B,GAAG;EACnC1Y,MAAAA,SAAS,EAAE;EADwB,KAArC;;EAIA,QAAI8Q,MAAM,CAACtC,IAAP,KAAgB,SAApB,EAA+B;EAC7BkK,MAAAA,aAAa,CAAC3N,SAAd,GAA0B,IAA1B;EACD,KAFD,MAEO,IAAI+F,MAAM,CAACtC,IAAP,KAAgB,OAApB,EAA6B;EAClCkK,MAAAA,aAAa,CAACzN,OAAd,GAAwB,IAAxB;EACD;;EAED,SAAKwE,MAAL,CAAYiJ,aAAZ;EACD,GAhEH;;EAAA,SAkEExJ,gBAlEF,GAkEE,4BAKE;EACA,WAAO,KAAKsK,aAAZ;EACD,GAzEH;;EAAA,SA2EE1K,KA3EF,GA2EE,iBAAc;EACZ,SAAKwQ,eAAL,GAAuBlf,SAAvB;EACA,SAAK4Y,YAAL;EACA,SAAKvJ,MAAL,CAAY;EAAEzP,MAAAA,SAAS,EAAE;EAAb,KAAZ;EACD,GA/EH;;EAAA,SAiFEqf,MAjFF,GAiFE,gBACE1M,SADF,EAEEhO,OAFF,EAGkB;EAChB,SAAK4a,aAAL,GAAqB5a,OAArB;;EAEA,QAAI,KAAK2a,eAAT,EAA0B;EACxB,WAAKA,eAAL,CAAqB5P,cAArB,CAAoC,IAApC;EACD;;EAED,SAAK4P,eAAL,GAAuB,KAAKzN,MAAL,CAAY2F,gBAAZ,GAA+B5F,KAA/B,CAAqC,KAAKC,MAA1C,eAClB,KAAKlN,OADa;EAErBgO,MAAAA,SAAS,EACP,OAAOA,SAAP,KAAqB,WAArB,GAAmCA,SAAnC,GAA+C,KAAKhO,OAAL,CAAagO;EAHzC,OAAvB;EAMA,SAAK2M,eAAL,CAAqB9P,WAArB,CAAiC,IAAjC;EAEA,WAAO,KAAK8P,eAAL,CAAqB7M,OAArB,EAAP;EACD,GApGH;;EAAA,SAsGUuG,YAtGV,GAsGE,wBAA6B;EAC3B,QAAM7T,KAAK,GAAG,KAAKma,eAAL,GACV,KAAKA,eAAL,CAAqBna,KADX,GAEVqI,eAAe,EAFnB;;EAIA,QAAMvH,MAKL,gBACId,KADJ;EAEC+W,MAAAA,SAAS,EAAE/W,KAAK,CAACC,MAAN,KAAiB,SAF7B;EAGCyW,MAAAA,SAAS,EAAE1W,KAAK,CAACC,MAAN,KAAiB,SAH7B;EAIC6B,MAAAA,OAAO,EAAE9B,KAAK,CAACC,MAAN,KAAiB,OAJ3B;EAKC+W,MAAAA,MAAM,EAAEhX,KAAK,CAACC,MAAN,KAAiB,MAL1B;EAMCia,MAAAA,MAAM,EAAE,KAAKA,MANd;EAOCvQ,MAAAA,KAAK,EAAE,KAAKA;EAPb,MALD;;EAeA,SAAK0K,aAAL,GAAqBvT,MAArB;EAMD,GAhIH;;EAAA,SAkIUwJ,MAlIV,GAkIE,gBAAe9K,OAAf,EAAuC;EAAA;;EACrC+H,IAAAA,aAAa,CAACP,KAAd,CAAoB,YAAM;EACxB;EACA,UAAI,MAAI,CAACoT,aAAT,EAAwB;EACtB,YAAI5a,OAAO,CAACoG,SAAZ,EAAuB;EACrB,UAAA,MAAI,CAACwU,aAAL,CAAmBxU,SAAnB,oBAAA,MAAI,CAACwU,aAAL,CAAmBxU,SAAnB,CACE,MAAI,CAACyO,aAAL,CAAmBpL,IADrB,EAEE,MAAI,CAACoL,aAAL,CAAmB7G,SAFrB,EAGE,MAAI,CAAC6G,aAAL,CAAmBhJ,OAHrB;EAKA,UAAA,MAAI,CAAC+O,aAAL,CAAmBzM,SAAnB,oBAAA,MAAI,CAACyM,aAAL,CAAmBzM,SAAnB,CACE,MAAI,CAAC0G,aAAL,CAAmBpL,IADrB,EAEE,IAFF,EAGE,MAAI,CAACoL,aAAL,CAAmB7G,SAHrB,EAIE,MAAI,CAAC6G,aAAL,CAAmBhJ,OAJrB;EAMD,SAZD,MAYO,IAAI7L,OAAO,CAACsG,OAAZ,EAAqB;EAC1B,UAAA,MAAI,CAACsU,aAAL,CAAmBtU,OAAnB,oBAAA,MAAI,CAACsU,aAAL,CAAmBtU,OAAnB,CACE,MAAI,CAACuO,aAAL,CAAmB7R,KADrB,EAEE,MAAI,CAAC6R,aAAL,CAAmB7G,SAFrB,EAGE,MAAI,CAAC6G,aAAL,CAAmBhJ,OAHrB;EAKA,UAAA,MAAI,CAAC+O,aAAL,CAAmBzM,SAAnB,oBAAA,MAAI,CAACyM,aAAL,CAAmBzM,SAAnB,CACE1S,SADF,EAEE,MAAI,CAACoZ,aAAL,CAAmB7R,KAFrB,EAGE,MAAI,CAAC6R,aAAL,CAAmB7G,SAHrB,EAIE,MAAI,CAAC6G,aAAL,CAAmBhJ,OAJrB;EAMD;EACF,OA5BuB;;;EA+BxB,UAAI7L,OAAO,CAAC3E,SAAZ,EAAuB;EACrB,QAAA,MAAI,CAACA,SAAL,CAAeuI,OAAf,CAAuB,UAAArI,QAAQ,EAAI;EACjCA,UAAAA,QAAQ,CAAC,MAAI,CAACsZ,aAAN,CAAR;EACD,SAFD;EAGD;EACF,KApCD;EAqCD,GAxKH;;EAAA;EAAA,EAKUzZ,YALV;;ECfA;EAoCA;EAEA,SAASyf,iBAAT,CAA2Bva,QAA3B,EAAmE;EACjE,SAAO;EACLvB,IAAAA,WAAW,EAAEuB,QAAQ,CAACN,OAAT,CAAiBjB,WADzB;EAELyB,IAAAA,KAAK,EAAEF,QAAQ,CAACE;EAFX,GAAP;EAID;EAGD;EACA;EACA;;;EACA,SAASsa,cAAT,CAAwBrb,KAAxB,EAAuD;EACrD,SAAO;EACLe,IAAAA,KAAK,EAAEf,KAAK,CAACe,KADR;EAEL5B,IAAAA,QAAQ,EAAEa,KAAK,CAACb,QAFX;EAGLkB,IAAAA,SAAS,EAAEL,KAAK,CAACK;EAHZ,GAAP;EAKD;;EAED,SAASib,8BAAT,CAAwCza,QAAxC,EAA4D;EAC1D,SAAOA,QAAQ,CAACE,KAAT,CAAesF,QAAtB;EACD;;EAED,SAASkV,2BAAT,CAAqCvb,KAArC,EAAmD;EACjD,SAAOA,KAAK,CAACe,KAAN,CAAYC,MAAZ,KAAuB,SAA9B;EACD;;EAEM,SAASwa,SAAT,CACL/N,MADK,EAELlN,OAFK,EAGY;EAAA;;EACjBA,EAAAA,OAAO,GAAGA,OAAO,IAAI,EAArB;EAEA,MAAMsO,SAA+B,GAAG,EAAxC;EACA,MAAMvB,OAA0B,GAAG,EAAnC;;EAEA,MAAI,aAAA/M,OAAO,SAAP,qBAASkb,kBAAT,MAAgC,KAApC,EAA2C;EACzC,QAAMC,uBAAuB,GAC3Bnb,OAAO,CAACmb,uBAAR,IAAmCJ,8BADrC;EAGA7N,IAAAA,MAAM,CACH2F,gBADH,GAEGrF,MAFH,GAGG5J,OAHH,CAGW,UAAAtD,QAAQ,EAAI;EACnB,UAAI6a,uBAAuB,CAAC7a,QAAD,CAA3B,EAAuC;EACrCgO,QAAAA,SAAS,CAAC5S,IAAV,CAAemf,iBAAiB,CAACva,QAAD,CAAhC;EACD;EACF,KAPH;EAQD;;EAED,MAAI,cAAAN,OAAO,SAAP,sBAASob,gBAAT,MAA8B,KAAlC,EAAyC;EACvC,QAAMC,oBAAoB,GACxBrb,OAAO,CAACqb,oBAAR,IAAgCL,2BADlC;EAGA9N,IAAAA,MAAM,CACHoE,aADH,GAEG9D,MAFH,GAGG5J,OAHH,CAGW,UAAAnE,KAAK,EAAI;EAChB,UAAI4b,oBAAoB,CAAC5b,KAAD,CAAxB,EAAiC;EAC/BsN,QAAAA,OAAO,CAACrR,IAAR,CAAaof,cAAc,CAACrb,KAAD,CAA3B;EACD;EACF,KAPH;EAQD;;EAED,SAAO;EAAE6O,IAAAA,SAAS,EAATA,SAAF;EAAavB,IAAAA,OAAO,EAAPA;EAAb,GAAP;EACD;EAEM,SAASuO,OAAT,CACLpO,MADK,EAELqO,eAFK,EAGLvb,OAHK,EAIC;EACN,MAAI,OAAOub,eAAP,KAA2B,QAA3B,IAAuCA,eAAe,KAAK,IAA/D,EAAqE;EACnE;EACD;;EAED,MAAM1N,aAAa,GAAGX,MAAM,CAAC2F,gBAAP,EAAtB;EACA,MAAMlC,UAAU,GAAGzD,MAAM,CAACoE,aAAP,EAAnB;EAEA,MAAMhD,SAAS,GAAIiN,eAAD,CAAqCjN,SAArC,IAAkD,EAApE;EACA,MAAMvB,OAAO,GAAIwO,eAAD,CAAqCxO,OAArC,IAAgD,EAAhE;EAEAuB,EAAAA,SAAS,CAAC1K,OAAV,CAAkB,UAAA4X,kBAAkB,EAAI;EAAA;;EACtC3N,IAAAA,aAAa,CAACZ,KAAd,CACEC,MADF,eAGOlN,OAHP,6CAGOA,OAAO,CAAEwI,cAHhB,qBAGO,sBAAyB8F,SAHhC;EAIIvP,MAAAA,WAAW,EAAEyc,kBAAkB,CAACzc;EAJpC,QAMEyc,kBAAkB,CAAChb,KANrB;EAQD,GATD;EAWAuM,EAAAA,OAAO,CAACnJ,OAAR,CAAgB,UAAA6X,eAAe,EAAI;EAAA;;EACjC,QAAMhc,KAAK,GAAGkR,UAAU,CAACjF,GAAX,CAAe+P,eAAe,CAAC3b,SAA/B,CAAd,CADiC;;EAIjC,QAAIL,KAAJ,EAAW;EACT,UAAIA,KAAK,CAACe,KAAN,CAAYsJ,aAAZ,GAA4B2R,eAAe,CAACjb,KAAhB,CAAsBsJ,aAAtD,EAAqE;EACnErK,QAAAA,KAAK,CAACsK,QAAN,CAAe0R,eAAe,CAACjb,KAA/B;EACD;;EACD;EACD,KATgC;;;EAYjCmQ,IAAAA,UAAU,CAAC1D,KAAX,CACEC,MADF,eAGOlN,OAHP,8CAGOA,OAAO,CAAEwI,cAHhB,qBAGO,uBAAyBuE,OAHhC;EAIInO,MAAAA,QAAQ,EAAE6c,eAAe,CAAC7c,QAJ9B;EAKIkB,MAAAA,SAAS,EAAE2b,eAAe,CAAC3b;EAL/B,QAOE2b,eAAe,CAACjb,KAPlB;EASD,GArBD;EAsBD;;ECzJD,IAAMkb,cAAc,gBAAGC,cAAK,CAACC,aAAN,CAA6CngB,SAA7C,CAAvB;EACA,IAAMogB,yBAAyB,gBAAGF,cAAK,CAACC,aAAN,CAA6B,KAA7B,CAAlC;EAGA;EACA;EACA;EACA;EACA;;EACA,SAASE,qBAAT,CAA+BC,cAA/B,EAAwD;EACtD,MAAIA,cAAc,IAAI,OAAOnf,MAAP,KAAkB,WAAxC,EAAqD;EACnD,QAAI,CAACA,MAAM,CAACof,uBAAZ,EAAqC;EACnCpf,MAAAA,MAAM,CAACof,uBAAP,GAAiCN,cAAjC;EACD;;EAED,WAAO9e,MAAM,CAACof,uBAAd;EACD;;EAED,SAAON,cAAP;EACD;;MAEYO,cAAc,GAAG,SAAjBA,cAAiB,GAAM;EAClC,MAAMC,WAAW,GAAGP,cAAK,CAACQ,UAAN,CAClBL,qBAAqB,CAACH,cAAK,CAACQ,UAAN,CAAiBN,yBAAjB,CAAD,CADH,CAApB;;EAIA,MAAI,CAACK,WAAL,EAAkB;EAChB,UAAM,IAAI3Z,KAAJ,CAAU,wDAAV,CAAN;EACD;;EAED,SAAO2Z,WAAP;EACD;MAQYE,mBAAmB,GAAG,SAAtBA,mBAAsB,OAIU;EAAA,MAH3ClP,MAG2C,QAH3CA,MAG2C;EAAA,iCAF3C6O,cAE2C;EAAA,MAF3CA,cAE2C,oCAF1B,KAE0B;EAAA,MAD3CM,QAC2C,QAD3CA,QAC2C;EAC3CV,EAAAA,cAAK,CAACW,SAAN,CAAgB,YAAM;EACpBpP,IAAAA,MAAM,CAAC4D,KAAP;EACA,WAAO,YAAM;EACX5D,MAAAA,MAAM,CAAC+D,OAAP;EACD,KAFD;EAGD,GALD,EAKG,CAAC/D,MAAD,CALH;EAOA,MAAMqP,OAAO,GAAGT,qBAAqB,CAACC,cAAD,CAArC;EAEA,sBACEJ,6BAAC,yBAAD,CAA2B,QAA3B;EAAoC,IAAA,KAAK,EAAEI;EAA3C,kBACEJ,6BAAC,OAAD,CAAS,QAAT;EAAkB,IAAA,KAAK,EAAEzO;EAAzB,KAAkCmP,QAAlC,CADF,CADF;EAKD;;EC1DD,SAASG,WAAT,GAAqD;EACnD,MAAIC,QAAO,GAAG,KAAd;EACA,SAAO;EACLC,IAAAA,UAAU,EAAE,sBAAM;EAChBD,MAAAA,QAAO,GAAG,KAAV;EACD,KAHI;EAILtS,IAAAA,KAAK,EAAE,iBAAM;EACXsS,MAAAA,QAAO,GAAG,IAAV;EACD,KANI;EAOLA,IAAAA,OAAO,EAAE,mBAAM;EACb,aAAOA,QAAP;EACD;EATI,GAAP;EAWD;;EAED,IAAME,8BAA8B,gBAAGhB,cAAK,CAACC,aAAN,CAAoBY,WAAW,EAA/B,CAAvC;;MAIaI,0BAA0B,GAAG,SAA7BA,0BAA6B;EAAA,SACxCjB,cAAK,CAACQ,UAAN,CAAiBQ,8BAAjB,CADwC;EAAA;;MAW7BE,uBAAuB,GAAG,SAA1BA,uBAA0B,OAEH;EAAA,MADlCR,QACkC,QADlCA,QACkC;EAClC,MAAMnf,KAAK,GAAGye,cAAK,CAACmB,OAAN,CAAc;EAAA,WAAMN,WAAW,EAAjB;EAAA,GAAd,EAAmC,EAAnC,CAAd;EACA,sBACEb,6BAAC,8BAAD,CAAgC,QAAhC;EAAyC,IAAA,KAAK,EAAEze;EAAhD,KACG,OAAOmf,QAAP,KAAoB,UAApB,GACIA,QAAD,CAAuBnf,KAAvB,CADH,GAEGmf,QAHN,CADF;EAOD;;EC3CD,IAAMU,eAAe,GAAG,SAAlBA,eAAkB,CACtBb,WADsB,EAEtB1c,OAFsB,EAGtBY,UAHsB,EAItB4c,aAJsB,EAKnB;EACH,MAAMC,aAAa,GAAGf,WAAW,CAAC9b,UAAZ,CAAuBZ,OAAvB,CAAtB;;EACA,MAAIY,UAAU,KAAK6c,aAAnB,EAAkC;EAChCD,IAAAA,aAAa,CAACC,aAAD,CAAb;EACD;EACF,CAVD;;EAiBO,SAASC,aAAT,CACL1e,IADK,EAELC,IAFK,EAGG;EACR,MAAM0e,UAAU,GAAGxB,cAAK,CAACyB,MAAN,CAAa,KAAb,CAAnB;EAEA,MAAMlB,WAAW,GAAGD,cAAc,EAAlC;;EAHQ,yBAKUhd,eAAe,CAACT,IAAD,EAAOC,IAAP,CALzB;EAAA,MAKDe,OALC;;EAAA,wBAM4Bmc,cAAK,CAAC0B,QAAN,CAClCnB,WAAW,CAAC9b,UAAZ,CAAuBZ,OAAvB,CADkC,CAN5B;EAAA,MAMDY,UANC;EAAA,MAMW4c,aANX;;EAUR,MAAMM,UAAU,GAAG3B,cAAK,CAACyB,MAAN,CAAa5d,OAAb,CAAnB;EACA8d,EAAAA,UAAU,CAACC,OAAX,GAAqB/d,OAArB;EACA,MAAMge,aAAa,GAAG7B,cAAK,CAACyB,MAAN,CAAahd,UAAb,CAAtB;EACAod,EAAAA,aAAa,CAACD,OAAd,GAAwBnd,UAAxB;EAEAub,EAAAA,cAAK,CAACW,SAAN,CAAgB,YAAM;EACpBa,IAAAA,UAAU,CAACI,OAAX,GAAqB,IAArB;EAEAR,IAAAA,eAAe,CACbb,WADa,EAEboB,UAAU,CAACC,OAFE,EAGbC,aAAa,CAACD,OAHD,EAIbP,aAJa,CAAf;EAOA,QAAM3H,WAAW,GAAG6G,WAAW,CAAC5K,aAAZ,GAA4BhW,SAA5B,CAClByM,aAAa,CAACJ,UAAd,CAAyB,YAAM;EAC7B,UAAIwV,UAAU,CAACI,OAAf,EAAwB;EACtBR,QAAAA,eAAe,CACbb,WADa,EAEboB,UAAU,CAACC,OAFE,EAGbC,aAAa,CAACD,OAHD,EAIbP,aAJa,CAAf;EAMD;EACF,KATD,CADkB,CAApB;EAaA,WAAO,YAAM;EACXG,MAAAA,UAAU,CAACI,OAAX,GAAqB,KAArB;EACAlI,MAAAA,WAAW;EACZ,KAHD;EAID,GA3BD,EA2BG,CAAC6G,WAAD,CA3BH;EA6BA,SAAO9b,UAAP;EACD;;EC7DM,SAASqd,aAAT,CACLjf,IADK,EAELC,IAFK,EAGG;EACR,MAAM0e,UAAU,GAAGxB,cAAK,CAACyB,MAAN,CAAa,KAAb,CAAnB;EACA,MAAM5d,OAAO,GAAGN,uBAAuB,CAACV,IAAD,EAAOC,IAAP,CAAvC;EAEA,MAAMyd,WAAW,GAAGD,cAAc,EAAlC;;EAJQ,wBAM4BN,cAAK,CAAC0B,QAAN,CAClCnB,WAAW,CAAChL,UAAZ,CAAuB1R,OAAvB,CADkC,CAN5B;EAAA,MAMD0R,UANC;EAAA,MAMWwM,aANX;;EAUR,MAAMJ,UAAU,GAAG3B,cAAK,CAACyB,MAAN,CAAa5d,OAAb,CAAnB;EACA8d,EAAAA,UAAU,CAACC,OAAX,GAAqB/d,OAArB;EACA,MAAMme,aAAa,GAAGhC,cAAK,CAACyB,MAAN,CAAalM,UAAb,CAAtB;EACAyM,EAAAA,aAAa,CAACJ,OAAd,GAAwBrM,UAAxB;EAEAyK,EAAAA,cAAK,CAACW,SAAN,CAAgB,YAAM;EACpBa,IAAAA,UAAU,CAACI,OAAX,GAAqB,IAArB;EAEA,QAAMlI,WAAW,GAAG6G,WAAW,CAACrJ,gBAAZ,GAA+BvX,SAA/B,CAClByM,aAAa,CAACJ,UAAd,CAAyB,YAAM;EAC7B,UAAIwV,UAAU,CAACI,OAAf,EAAwB;EACtB,YAAMK,aAAa,GAAG1B,WAAW,CAAChL,UAAZ,CAAuBoM,UAAU,CAACC,OAAlC,CAAtB;;EACA,YAAII,aAAa,CAACJ,OAAd,KAA0BK,aAA9B,EAA6C;EAC3CF,UAAAA,aAAa,CAACE,aAAD,CAAb;EACD;EACF;EACF,KAPD,CADkB,CAApB;EAWA,WAAO,YAAM;EACXT,MAAAA,UAAU,CAACI,OAAX,GAAqB,KAArB;EACAlI,MAAAA,WAAW;EACZ,KAHD;EAID,GAlBD,EAkBG,CAAC6G,WAAD,CAlBH;EAoBA,SAAOhL,UAAP;EACD;;ECnDM,SAAS2M,gBAAT,CACL1I,QADK,EAEL2I,iBAFK,EAGLC,MAHK,EAII;EACT;EACA,MAAI,OAAOD,iBAAP,KAA6B,UAAjC,EAA6C;EAC3C,WAAOA,iBAAiB,MAAjB,SAAqBC,MAArB,CAAP;EACD,GAJQ;;;EAOT,MAAI,OAAOD,iBAAP,KAA6B,SAAjC,EAA4C,OAAOA,iBAAP,CAPnC;;EAUT,SAAO,CAAC,CAAC3I,QAAT;EACD;;EC8CM,SAAS6I,WAAT,CAMLxf,IANK,EAULC,IAVK,EAaLC,IAbK,EAcmD;EACxD,MAAMye,UAAU,GAAGxB,cAAK,CAACyB,MAAN,CAAa,KAAb,CAAnB;;EADwD,wBAEhCzB,cAAK,CAAC0B,QAAN,CAAe,CAAf,CAFgC;EAAA,MAE/CY,WAF+C;;EAIxD,MAAMje,OAAO,GAAGlB,iBAAiB,CAACN,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAAjC;EACA,MAAMwd,WAAW,GAAGD,cAAc,EAAlC;EAEA,MAAMiC,MAAM,GAAGvC,cAAK,CAACyB,MAAN,EAAf;;EAIA,MAAI,CAACc,MAAM,CAACX,OAAZ,EAAqB;EACnBW,IAAAA,MAAM,CAACX,OAAP,GAAiB,IAAI9C,gBAAJ,CAAqByB,WAArB,EAAkClc,OAAlC,CAAjB;EACD,GAFD,MAEO;EACLke,IAAAA,MAAM,CAACX,OAAP,CAAe9U,UAAf,CAA0BzI,OAA1B;EACD;;EAED,MAAM6U,aAAa,GAAGqJ,MAAM,CAACX,OAAP,CAAehT,gBAAf,EAAtB;EAEAoR,EAAAA,cAAK,CAACW,SAAN,CAAgB,YAAM;EACpBa,IAAAA,UAAU,CAACI,OAAX,GAAqB,IAArB;EAEA,QAAMlI,WAAW,GAAG6I,MAAM,CAACX,OAAP,CAAgBjiB,SAAhB,CAClByM,aAAa,CAACJ,UAAd,CAAyB,YAAM;EAC7B,UAAIwV,UAAU,CAACI,OAAf,EAAwB;EACtBU,QAAAA,WAAW,CAAC,UAAApiB,CAAC;EAAA,iBAAIA,CAAC,GAAG,CAAR;EAAA,SAAF,CAAX;EACD;EACF,KAJD,CADkB,CAApB;EAOA,WAAO,YAAM;EACXshB,MAAAA,UAAU,CAACI,OAAX,GAAqB,KAArB;EACAlI,MAAAA,WAAW;EACZ,KAHD;EAID,GAdD,EAcG,EAdH;EAgBA,MAAMqF,MAAM,GAAGiB,cAAK,CAACwC,WAAN,CAEb,UAACnQ,SAAD,EAAY4M,aAAZ,EAA8B;EAC9BsD,IAAAA,MAAM,CAACX,OAAP,CAAgB7C,MAAhB,CAAuB1M,SAAvB,EAAkC4M,aAAlC,EAAiD7X,KAAjD,CAAuDlG,IAAvD;EACD,GAJc,EAIZ,EAJY,CAAf;;EAMA,MACEgY,aAAa,CAAC7R,KAAd,IACA6a,gBAAgB,CAACpiB,SAAD,EAAYyiB,MAAM,CAACX,OAAP,CAAevd,OAAf,CAAuBkV,gBAAnC,EAAqD,CACnEL,aAAa,CAAC7R,KADqD,CAArD,CAFlB,EAKE;EACA,UAAM6R,aAAa,CAAC7R,KAApB;EACD;;EAED,sBAAY6R,aAAZ;EAA2B6F,IAAAA,MAAM,EAANA,MAA3B;EAAmC0D,IAAAA,WAAW,EAAEvJ,aAAa,CAAC6F;EAA9D;EACD;;ECpHM,SAAS2D,YAAT,CAOLre,OAPK,EAcLse,QAdK,EAeL;EACA,MAAMnB,UAAU,GAAGxB,cAAK,CAACyB,MAAN,CAAa,KAAb,CAAnB;;EADA,wBAEwBzB,cAAK,CAAC0B,QAAN,CAAe,CAAf,CAFxB;EAAA,MAESY,WAFT;;EAIA,MAAM/B,WAAW,GAAGD,cAAc,EAAlC;EACA,MAAMsC,kBAAkB,GAAG3B,0BAA0B,EAArD;EACA,MAAMlL,gBAAgB,GAAGwK,WAAW,CAAChJ,2BAAZ,CAAwClT,OAAxC,CAAzB,CANA;;EASA0R,EAAAA,gBAAgB,CAACoF,iBAAjB,GAAqC,IAArC,CATA;;EAYA,MAAIpF,gBAAgB,CAACpL,OAArB,EAA8B;EAC5BoL,IAAAA,gBAAgB,CAACpL,OAAjB,GAA2ByB,aAAa,CAACJ,UAAd,CACzB+J,gBAAgB,CAACpL,OADQ,CAA3B;EAGD;;EAED,MAAIoL,gBAAgB,CAACtL,SAArB,EAAgC;EAC9BsL,IAAAA,gBAAgB,CAACtL,SAAjB,GAA6B2B,aAAa,CAACJ,UAAd,CAC3B+J,gBAAgB,CAACtL,SADU,CAA7B;EAGD;;EAED,MAAIsL,gBAAgB,CAACvD,SAArB,EAAgC;EAC9BuD,IAAAA,gBAAgB,CAACvD,SAAjB,GAA6BpG,aAAa,CAACJ,UAAd,CAC3B+J,gBAAgB,CAACvD,SADU,CAA7B;EAGD;;EAED,MAAIuD,gBAAgB,CAACyD,QAArB,EAA+B;EAC7B;EACA;EACA,QAAI,OAAOzD,gBAAgB,CAACxT,SAAxB,KAAsC,QAA1C,EAAoD;EAClDwT,MAAAA,gBAAgB,CAACxT,SAAjB,GAA6B,IAA7B;EACD,KAL4B;EAQ7B;;;EACA,QAAIwT,gBAAgB,CAAC1I,SAAjB,KAA+B,CAAnC,EAAsC;EACpC0I,MAAAA,gBAAgB,CAAC1I,SAAjB,GAA6B,CAA7B;EACD;EACF;;EAED,MAAI0I,gBAAgB,CAACyD,QAAjB,IAA6BzD,gBAAgB,CAACwD,gBAAlD,EAAoE;EAClE;EACA,QAAI,CAACqJ,kBAAkB,CAAC9B,OAAnB,EAAL,EAAmC;EACjC/K,MAAAA,gBAAgB,CAAC8G,YAAjB,GAAgC,KAAhC;EACD;EACF;;EAjDD,yBAmDmBmD,cAAK,CAAC0B,QAAN,CACjB;EAAA,WACE,IAAIiB,QAAJ,CACEpC,WADF,EAEExK,gBAFF,CADF;EAAA,GADiB,CAnDnB;EAAA,MAmDOtH,QAnDP;;EA2DA,MAAI9I,MAAM,GAAG8I,QAAQ,CAACuK,mBAAT,CAA6BjD,gBAA7B,CAAb;EAEAiK,EAAAA,cAAK,CAACW,SAAN,CAAgB,YAAM;EACpBa,IAAAA,UAAU,CAACI,OAAX,GAAqB,IAArB;EAEAgB,IAAAA,kBAAkB,CAAC7B,UAAnB;EAEA,QAAMrH,WAAW,GAAGjL,QAAQ,CAAC9O,SAAT,CAClByM,aAAa,CAACJ,UAAd,CAAyB,YAAM;EAC7B,UAAIwV,UAAU,CAACI,OAAf,EAAwB;EACtBU,QAAAA,WAAW,CAAC,UAAApiB,CAAC;EAAA,iBAAIA,CAAC,GAAG,CAAR;EAAA,SAAF,CAAX;EACD;EACF,KAJD,CADkB,CAApB,CALoB;EAcpB;;EACAuO,IAAAA,QAAQ,CAACiK,YAAT;EAEA,WAAO,YAAM;EACX8I,MAAAA,UAAU,CAACI,OAAX,GAAqB,KAArB;EACAlI,MAAAA,WAAW;EACZ,KAHD;EAID,GArBD,EAqBG,CAACkJ,kBAAD,EAAqBnU,QAArB,CArBH;EAuBAuR,EAAAA,cAAK,CAACW,SAAN,CAAgB,YAAM;EACpB;EACA;EACAlS,IAAAA,QAAQ,CAAC3B,UAAT,CAAoBiJ,gBAApB,EAAsC;EAAErW,MAAAA,SAAS,EAAE;EAAb,KAAtC;EACD,GAJD,EAIG,CAACqW,gBAAD,EAAmBtH,QAAnB,CAJH,EApFA;;EA2FA,MAAIsH,gBAAgB,CAACyD,QAAjB,IAA6B7T,MAAM,CAACiW,SAAxC,EAAmD;EACjD,UAAMnN,QAAQ,CACXmL,eADG,CACa7D,gBADb,EAEH5O,IAFG,CAEE,gBAAc;EAAA,UAAX2G,IAAW,QAAXA,IAAW;EAClBiI,MAAAA,gBAAgB,CAACtL,SAAjB,oBAAAsL,gBAAgB,CAACtL,SAAjB,CAA6BqD,IAA7B;EACAiI,MAAAA,gBAAgB,CAACvD,SAAjB,oBAAAuD,gBAAgB,CAACvD,SAAjB,CAA6B1E,IAA7B,EAAmC,IAAnC;EACD,KALG,EAMH1G,KANG,CAMG,UAAAC,KAAK,EAAI;EACdub,MAAAA,kBAAkB,CAAC7B,UAAnB;EACAhL,MAAAA,gBAAgB,CAACpL,OAAjB,oBAAAoL,gBAAgB,CAACpL,OAAjB,CAA2BtD,KAA3B;EACA0O,MAAAA,gBAAgB,CAACvD,SAAjB,oBAAAuD,gBAAgB,CAACvD,SAAjB,CAA6B1S,SAA7B,EAAwCuH,KAAxC;EACD,KAVG,CAAN;EAWD,GAvGD;;;EA0GA,MACE1B,MAAM,CAACgB,OAAP,IACA,CAACic,kBAAkB,CAAC9B,OAAnB,EADD,IAEA,CAACnb,MAAM,CAAClB,UAFR,IAGAyd,gBAAgB,CACdnM,gBAAgB,CAACyD,QADH,EAEdzD,gBAAgB,CAACwD,gBAFH,EAGd,CAAC5T,MAAM,CAAC0B,KAAR,EAAeoH,QAAQ,CAACkL,eAAT,EAAf,CAHc,CAJlB,EASE;EACA,UAAMhU,MAAM,CAAC0B,KAAb;EACD,GArHD;;;EAwHA,MAAI0O,gBAAgB,CAACqG,mBAAjB,KAAyC,SAA7C,EAAwD;EACtDzW,IAAAA,MAAM,GAAG8I,QAAQ,CAAC0K,WAAT,CAAqBxT,MAArB,EAA6BoQ,gBAA7B,CAAT;EACD;;EAED,SAAOpQ,MAAP;EACD;;EC7GM,SAASkd,QAAT,CAMLhgB,IANK,EAOLC,IAPK,EAULC,IAVK,EAW0B;EAC/B,MAAM+S,aAAa,GAAGlT,cAAc,CAACC,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAApC;EACA,SAAO2f,YAAY,CAAC5M,aAAD,EAAgB0B,aAAhB,CAAnB;EACD;;ECmEM,SAASsL,UAAT,CACL1R,OADK,EAEc;EACnB,MAAMoQ,UAAU,GAAGxB,cAAK,CAACyB,MAAN,CAAa,KAAb,CAAnB;;EADmB,wBAEKzB,cAAK,CAAC0B,QAAN,CAAe,CAAf,CAFL;EAAA,MAEVY,WAFU;;EAInB,MAAM/B,WAAW,GAAGD,cAAc,EAAlC;EAEA,MAAMyC,gBAAgB,GAAG5B,aAAO,CAC9B;EAAA,WACE/P,OAAO,CAACwE,GAAR,CAAY,UAAAvR,OAAO,EAAI;EACrB,UAAM0R,gBAAgB,GAAGwK,WAAW,CAAChJ,2BAAZ,CACvBlT,OADuB,CAAzB,CADqB;;EAMrB0R,MAAAA,gBAAgB,CAACoF,iBAAjB,GAAqC,IAArC;EAEA,aAAOpF,gBAAP;EACD,KATD,CADF;EAAA,GAD8B,EAY9B,CAAC3E,OAAD,EAAUmP,WAAV,CAZ8B,CAAhC;;EANmB,yBAqBAP,cAAK,CAAC0B,QAAN,CACjB;EAAA,WAAM,IAAI1E,eAAJ,CAAoBuD,WAApB,EAAiCwC,gBAAjC,CAAN;EAAA,GADiB,CArBA;EAAA,MAqBZtU,QArBY;;EAyBnB,MAAM9I,MAAM,GAAG8I,QAAQ,CAACuK,mBAAT,CAA6B+J,gBAA7B,CAAf;EAEA/C,EAAAA,cAAK,CAACW,SAAN,CAAgB,YAAM;EACpBa,IAAAA,UAAU,CAACI,OAAX,GAAqB,IAArB;EAEA,QAAMlI,WAAW,GAAGjL,QAAQ,CAAC9O,SAAT,CAClByM,aAAa,CAACJ,UAAd,CAAyB,YAAM;EAC7B,UAAIwV,UAAU,CAACI,OAAf,EAAwB;EACtBU,QAAAA,WAAW,CAAC,UAAApiB,CAAC;EAAA,iBAAIA,CAAC,GAAG,CAAR;EAAA,SAAF,CAAX;EACD;EACF,KAJD,CADkB,CAApB;EAQA,WAAO,YAAM;EACXshB,MAAAA,UAAU,CAACI,OAAX,GAAqB,KAArB;EACAlI,MAAAA,WAAW;EACZ,KAHD;EAID,GAfD,EAeG,CAACjL,QAAD,CAfH;EAiBAuR,EAAAA,cAAK,CAACW,SAAN,CAAgB,YAAM;EACpB;EACA;EACAlS,IAAAA,QAAQ,CAACyO,UAAT,CAAoB6F,gBAApB,EAAsC;EAAErjB,MAAAA,SAAS,EAAE;EAAb,KAAtC;EACD,GAJD,EAIG,CAACqjB,gBAAD,EAAmBtU,QAAnB,CAJH;EAMA,SAAO9I,MAAP;EACD;;ECnHM,SAASqd,gBAAT,CAMLngB,IANK,EAeLC,IAfK,EAwBLC,IAxBK,EA+BkC;EACvC,MAAMsB,OAAO,GAAGzB,cAAc,CAACC,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAA9B;EACA,SAAO2f,YAAY,CACjBre,OADiB,EAEjBsa,qBAFiB,CAAnB;EAID;;EC5FM,SAASsE,UAAT,CAAoBpe,KAApB,EAAoCR,OAApC,EAA8D;EACnE,MAAMkc,WAAW,GAAGD,cAAc,EAAlC;EAEA,MAAM4C,UAAU,GAAGlD,cAAK,CAACyB,MAAN,CAAapd,OAAb,CAAnB;EACA6e,EAAAA,UAAU,CAACtB,OAAX,GAAqBvd,OAArB,CAJmE;EAOnE;EACA;EACA;;EACA2b,EAAAA,cAAK,CAACmB,OAAN,CAAc,YAAM;EAClB,QAAItc,KAAJ,EAAW;EACT8a,MAAAA,OAAO,CAACY,WAAD,EAAc1b,KAAd,EAAqBqe,UAAU,CAACtB,OAAhC,CAAP;EACD;EACF,GAJD,EAIG,CAACrB,WAAD,EAAc1b,KAAd,CAJH;EAKD;MAQYse,OAAO,GAAG,SAAVA,OAAU,OAAgD;EAAA,MAA7CzC,QAA6C,QAA7CA,QAA6C;EAAA,MAAnCrc,OAAmC,QAAnCA,OAAmC;EAAA,MAA1BQ,KAA0B,QAA1BA,KAA0B;EACrEoe,EAAAA,UAAU,CAACpe,KAAD,EAAQR,OAAR,CAAV;EACA,SAAOqc,QAAP;EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}