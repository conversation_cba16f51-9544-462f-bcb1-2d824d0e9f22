!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-query")):"function"==typeof define&&define.amd?define(["exports","react","react-query"],t):t((e=e||self).ReactQueryDevtools={},e.<PERSON>,e.ReactQuery)}(this,(function(e,t,n){"use strict";var r="default"in t?t.default:t;function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function o(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var l={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","Ấ":"A","Ắ":"A","Ẳ":"A","Ẵ":"A","Ặ":"A","Æ":"AE","Ầ":"A","Ằ":"A","Ȃ":"A","Ç":"C","Ḉ":"C","È":"E","É":"E","Ê":"E","Ë":"E","Ế":"E","Ḗ":"E","Ề":"E","Ḕ":"E","Ḝ":"E","Ȇ":"E","Ì":"I","Í":"I","Î":"I","Ï":"I","Ḯ":"I","Ȋ":"I","Ð":"D","Ñ":"N","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","Ố":"O","Ṍ":"O","Ṓ":"O","Ȏ":"O","Ù":"U","Ú":"U","Û":"U","Ü":"U","Ý":"Y","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","ấ":"a","ắ":"a","ẳ":"a","ẵ":"a","ặ":"a","æ":"ae","ầ":"a","ằ":"a","ȃ":"a","ç":"c","ḉ":"c","è":"e","é":"e","ê":"e","ë":"e","ế":"e","ḗ":"e","ề":"e","ḕ":"e","ḝ":"e","ȇ":"e","ì":"i","í":"i","î":"i","ï":"i","ḯ":"i","ȋ":"i","ð":"d","ñ":"n","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","ố":"o","ṍ":"o","ṓ":"o","ȏ":"o","ù":"u","ú":"u","û":"u","ü":"u","ý":"y","ÿ":"y","Ā":"A","ā":"a","Ă":"A","ă":"a","Ą":"A","ą":"a","Ć":"C","ć":"c","Ĉ":"C","ĉ":"c","Ċ":"C","ċ":"c","Č":"C","č":"c","C̆":"C","c̆":"c","Ď":"D","ď":"d","Đ":"D","đ":"d","Ē":"E","ē":"e","Ĕ":"E","ĕ":"e","Ė":"E","ė":"e","Ę":"E","ę":"e","Ě":"E","ě":"e","Ĝ":"G","Ǵ":"G","ĝ":"g","ǵ":"g","Ğ":"G","ğ":"g","Ġ":"G","ġ":"g","Ģ":"G","ģ":"g","Ĥ":"H","ĥ":"h","Ħ":"H","ħ":"h","Ḫ":"H","ḫ":"h","Ĩ":"I","ĩ":"i","Ī":"I","ī":"i","Ĭ":"I","ĭ":"i","Į":"I","į":"i","İ":"I","ı":"i","Ĳ":"IJ","ĳ":"ij","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","Ḱ":"K","ḱ":"k","K̆":"K","k̆":"k","Ĺ":"L","ĺ":"l","Ļ":"L","ļ":"l","Ľ":"L","ľ":"l","Ŀ":"L","ŀ":"l","Ł":"l","ł":"l","Ḿ":"M","ḿ":"m","M̆":"M","m̆":"m","Ń":"N","ń":"n","Ņ":"N","ņ":"n","Ň":"N","ň":"n","ŉ":"n","N̆":"N","n̆":"n","Ō":"O","ō":"o","Ŏ":"O","ŏ":"o","Ő":"O","ő":"o","Œ":"OE","œ":"oe","P̆":"P","p̆":"p","Ŕ":"R","ŕ":"r","Ŗ":"R","ŗ":"r","Ř":"R","ř":"r","R̆":"R","r̆":"r","Ȓ":"R","ȓ":"r","Ś":"S","ś":"s","Ŝ":"S","ŝ":"s","Ş":"S","Ș":"S","ș":"s","ş":"s","Š":"S","š":"s","Ţ":"T","ţ":"t","ț":"t","Ț":"T","Ť":"T","ť":"t","Ŧ":"T","ŧ":"t","T̆":"T","t̆":"t","Ũ":"U","ũ":"u","Ū":"U","ū":"u","Ŭ":"U","ŭ":"u","Ů":"U","ů":"u","Ű":"U","ű":"u","Ų":"U","ų":"u","Ȗ":"U","ȗ":"u","V̆":"V","v̆":"v","Ŵ":"W","ŵ":"w","Ẃ":"W","ẃ":"w","X̆":"X","x̆":"x","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Y̆":"Y","y̆":"y","Ź":"Z","ź":"z","Ż":"Z","ż":"z","Ž":"Z","ž":"z","ſ":"s","ƒ":"f","Ơ":"O","ơ":"o","Ư":"U","ư":"u","Ǎ":"A","ǎ":"a","Ǐ":"I","ǐ":"i","Ǒ":"O","ǒ":"o","Ǔ":"U","ǔ":"u","Ǖ":"U","ǖ":"u","Ǘ":"U","ǘ":"u","Ǚ":"U","ǚ":"u","Ǜ":"U","ǜ":"u","Ứ":"U","ứ":"u","Ṹ":"U","ṹ":"u","Ǻ":"A","ǻ":"a","Ǽ":"AE","ǽ":"ae","Ǿ":"O","ǿ":"o","Þ":"TH","þ":"th","Ṕ":"P","ṕ":"p","Ṥ":"S","ṥ":"s","X́":"X","x́":"x","Ѓ":"Г","ѓ":"г","Ќ":"К","ќ":"к","A̋":"A","a̋":"a","E̋":"E","e̋":"e","I̋":"I","i̋":"i","Ǹ":"N","ǹ":"n","Ồ":"O","ồ":"o","Ṑ":"O","ṑ":"o","Ừ":"U","ừ":"u","Ẁ":"W","ẁ":"w","Ỳ":"Y","ỳ":"y","Ȁ":"A","ȁ":"a","Ȅ":"E","ȅ":"e","Ȉ":"I","ȉ":"i","Ȍ":"O","ȍ":"o","Ȑ":"R","ȑ":"r","Ȕ":"U","ȕ":"u","B̌":"B","b̌":"b","Č̣":"C","č̣":"c","Ê̌":"E","ê̌":"e","F̌":"F","f̌":"f","Ǧ":"G","ǧ":"g","Ȟ":"H","ȟ":"h","J̌":"J","ǰ":"j","Ǩ":"K","ǩ":"k","M̌":"M","m̌":"m","P̌":"P","p̌":"p","Q̌":"Q","q̌":"q","Ř̩":"R","ř̩":"r","Ṧ":"S","ṧ":"s","V̌":"V","v̌":"v","W̌":"W","w̌":"w","X̌":"X","x̌":"x","Y̌":"Y","y̌":"y","A̧":"A","a̧":"a","B̧":"B","b̧":"b","Ḑ":"D","ḑ":"d","Ȩ":"E","ȩ":"e","Ɛ̧":"E","ɛ̧":"e","Ḩ":"H","ḩ":"h","I̧":"I","i̧":"i","Ɨ̧":"I","ɨ̧":"i","M̧":"M","m̧":"m","O̧":"O","o̧":"o","Q̧":"Q","q̧":"q","U̧":"U","u̧":"u","X̧":"X","x̧":"x","Z̧":"Z","z̧":"z"},u=Object.keys(l).join("|"),c=new RegExp(u,"g"),s=new RegExp(u,""),d=function(e){return e.replace(c,(function(e){return l[e]}))},f=d,m=function(e){return!!e.match(s)},p=d;f.has=m,f.remove=p;var g={CASE_SENSITIVE_EQUAL:7,EQUAL:6,STARTS_WITH:5,WORD_STARTS_WITH:4,CONTAINS:3,ACRONYM:2,MATCHES:1,NO_MATCH:0};v.rankings=g;var y=function(e,t){return String(e.rankedValue).localeCompare(String(t.rankedValue))};function v(e,t,n){void 0===n&&(n={});var r=n,a=r.keys,o=r.threshold,l=void 0===o?g.MATCHES:o,u=r.baseSort,c=void 0===u?y:u;return e.reduce((function(e,r,o){var u=function(e,t,n,r){if(!t){return{rankedValue:e,rank:b(e,n,r),keyIndex:-1,keyThreshold:r.threshold}}return function(e,t){return t.reduce((function(t,n){var r=function(e,t){"object"==typeof t&&(t=t.key);var n;n="function"==typeof t?t(e):function(e,t){return e.split(".").reduce((function(e,t){return e?e[t]:null}),t)}(t,e);return null!=n?[].concat(n):null}(e,n);return r&&r.forEach((function(e){t.push({itemValue:e,attributes:E(n)})})),t}),[])}(e,t).reduce((function(e,t,a){var o=e.rank,i=e.rankedValue,l=e.keyIndex,u=e.keyThreshold,c=t.itemValue,s=t.attributes,d=b(c,n,r),f=i,m=s.minRanking,p=s.maxRanking,y=s.threshold;return d<m&&d>=g.MATCHES?d=m:d>p&&(d=p),d>o&&(o=d,l=a,u=y,f=c),{rankedValue:f,rank:o,keyIndex:l,keyThreshold:u}}),{rankedValue:e,rank:g.NO_MATCH,keyIndex:-1,keyThreshold:r.threshold})}(r,a,t,n),c=u.rank,s=u.keyThreshold;c>=(void 0===s?l:s)&&e.push(i({},u,{item:r,index:o}));return e}),[]).sort((function(e,t){return function(e,t,n){var r=e.rank,a=e.keyIndex,o=t.rank,i=t.keyIndex;return r===o?a===i?n(e,t):a<i?-1:1:r>o?-1:1}(e,t,c)})).map((function(e){return e.item}))}function b(e,t,n){return e=h(e,n),(t=h(t,n)).length>e.length?g.NO_MATCH:e===t?g.CASE_SENSITIVE_EQUAL:(e=e.toLowerCase())===(t=t.toLowerCase())?g.EQUAL:e.startsWith(t)?g.STARTS_WITH:e.includes(" "+t)?g.WORD_STARTS_WITH:e.includes(t)?g.CONTAINS:1===t.length?g.NO_MATCH:(r=e,a="",r.split(" ").forEach((function(e){e.split("-").forEach((function(e){a+=e.substr(0,1)}))})),a).includes(t)?g.ACRONYM:function(e,t){var n=0,r=0;function a(e,t,r){for(var a=r;a<t.length;a++){if(t[a]===e)return n+=1,a+1}return-1}var o=a(t[0],e,0);if(o<0)return g.NO_MATCH;r=o;for(var i=1;i<t.length;i++){var l=t[i];if(!((r=a(l,e,r))>-1))return g.NO_MATCH}return function(e){var r=n/t.length;return g.MATCHES+r*(1/e)}(r-o)}(e,t);var r,a}function h(e,t){return e=""+e,t.keepDiacritics||(e=f(e)),e}var C={maxRanking:1/0,minRanking:-1/0};function E(e){return"string"==typeof e?C:i({},C,e)}function k(e,t){var n=r.useState(),a=n[0],o=n[1];return r.useEffect((function(){var n=function(e){try{var t=localStorage.getItem(e);return"string"==typeof t?JSON.parse(t):void 0}catch(e){return}}(e);o(null==n?"function"==typeof t?t():t:n)}),[t,e]),[a,r.useCallback((function(t){o((function(n){var r=t;"function"==typeof t&&(r=t(n));try{localStorage.setItem(e,JSON.stringify(r))}catch(e){}return r}))}),[e])]}var x={background:"#0b1521",backgroundAlt:"#132337",foreground:"white",gray:"#3f4e60",grayAlt:"#222e3e",inputBackgroundColor:"#fff",inputTextColor:"#000",success:"#00ab52",danger:"#ff0085",active:"#006bff",warning:"#ffb200"},L=r.createContext(x);function w(e){var t=e.theme,n=o(e,["theme"]);return r.createElement(L.Provider,a({value:t},n))}var S="undefined"==typeof window;function O(e,t){return e.state.isFetching?t.active:e.getObserversCount()?e.isStale()?t.warning:t.success:t.gray}function A(e){return e.state.isFetching?"fetching":e.getObserversCount()?e.isStale()?"stale":"fresh":"inactive"}function R(e,t,n){return void 0===n&&(n={}),r.forwardRef((function(i,l){var u=i.style,c=o(i,["style"]),s=r.useContext(L),d=Object.entries(n).reduce((function(e,t){var n,o,i,l,u=t[0],d=t[1];return n=u,o=r.useState((function(){if("undefined"!=typeof window)return window.matchMedia&&window.matchMedia(n).matches})),i=o[0],l=o[1],r.useEffect((function(){if("undefined"!=typeof window){if(!window.matchMedia)return;var e=window.matchMedia(n),t=function(e){var t=e.matches;return l(t)};return e.addListener(t),function(){e.removeListener(t)}}}),[i,n,l]),i?a({},e,"function"==typeof d?d(c,s):d):e}),{});return r.createElement(e,a({},c,{style:a({},"function"==typeof t?t(c,s):t,u,d),ref:l}))}))}function I(){var e=r.useRef(!1),t=r.useCallback((function(){return e.current}),[]);return r[S?"useEffect":"useLayoutEffect"]((function(){return e.current=!0,function(){e.current=!1}}),[]),t}function D(e){var t=I(),n=r.useState(e),a=n[0],o=n[1];return[a,r.useCallback((function(e){var n;n=function(){t()&&o(e)},Promise.resolve().then(n).catch((function(e){return setTimeout((function(){throw e}))}))}),[t])]}var T=R("div",(function(e,t){return{fontSize:"clamp(12px, 1.5vw, 14px)",fontFamily:"sans-serif",display:"flex",backgroundColor:t.background,color:t.foreground}}),{"(max-width: 700px)":{flexDirection:"column"},"(max-width: 600px)":{fontSize:".9em"}}),H=R("div",(function(){return{flex:"1 1 500px",display:"flex",flexDirection:"column",overflow:"auto",height:"100%"}}),{"(max-width: 700px)":function(e,t){return{borderTop:"2px solid "+t.gray}}}),Q=R("button",(function(e,t){return{appearance:"none",fontSize:".9em",fontWeight:"bold",background:t.gray,border:"0",borderRadius:".3em",color:"white",padding:".5em",opacity:e.disabled?".5":void 0,cursor:"pointer"}})),M=R("span",{display:"inline-block",fontSize:"0.9em"}),U=R("span",{display:"inline-flex",alignItems:"center",padding:".2em .4em",fontWeight:"bold",textShadow:"0 0 10px black",borderRadius:".2em"}),z=R("code",{fontSize:".9em",color:"inherit",background:"inherit"}),N=R("input",(function(e,t){return{backgroundColor:t.inputBackgroundColor,border:0,borderRadius:".2em",color:t.inputTextColor,fontSize:".9em",lineHeight:"1.3",padding:".3em .4em"}})),j=R("select",(function(e,t){return{display:"inline-block",fontSize:".9em",fontFamily:"sans-serif",fontWeight:"normal",lineHeight:"1.3",padding:".3em 1.5em .3em .5em",height:"auto",border:0,borderRadius:".2em",appearance:"none",WebkitAppearance:"none",backgroundColor:t.inputBackgroundColor,backgroundImage:"url(\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23444444'><polygon points='0,25 100,25 50,75'/></svg>\")",backgroundRepeat:"no-repeat",backgroundPosition:"right .55em center",backgroundSize:".65em auto, 100%",color:t.inputTextColor}}),{"(max-width: 500px)":{display:"none"}}),P=R("div",{fontFamily:"Menlo, monospace",fontSize:"1em",lineHeight:"1.7",outline:"none",wordBreak:"break-word"}),q=R("span",{color:"white"}),B=R("button",{cursor:"pointer",color:"white"}),W=R("button",{cursor:"pointer",color:"inherit",font:"inherit",outline:"inherit",background:"transparent",border:"none",padding:0}),F=R("span",(function(e,t){return{color:t.danger}})),_=R("div",{marginLeft:".1em",paddingLeft:"1em",borderLeft:"2px solid rgba(0,0,0,.15)"}),Y=R("span",{color:"grey",fontSize:".7em"}),Z=function(e){var t=e.expanded,n=e.style,o=void 0===n?{}:n;return r.createElement("span",{style:a({display:"inline-block",transition:"all .1s ease",transform:"rotate("+(t?90:0)+"deg) "+(o.transform||"")},o)},"▶")};var V=function(e){var t=e.HandleEntry,n=e.label,a=e.value,o=e.subEntries,i=void 0===o?[]:o,l=e.subEntryPages,u=void 0===l?[]:l,c=e.type,s=e.expanded,d=void 0!==s&&s,f=e.toggleExpanded,m=e.pageSize,p=r.useState([]),g=p[0],y=p[1];return r.createElement(P,{key:n},(null==u?void 0:u.length)?r.createElement(r.Fragment,null,r.createElement(W,{onClick:function(){return f()}},r.createElement(Z,{expanded:d})," ",n," ",r.createElement(Y,null,"iterable"===String(c).toLowerCase()?"(Iterable) ":"",i.length," ",i.length>1?"items":"item")),d?1===u.length?r.createElement(_,null,i.map((function(e){return r.createElement(t,{key:e.label,entry:e})}))):r.createElement(_,null,u.map((function(e,n){return r.createElement("div",{key:n},r.createElement(P,null,r.createElement(B,{onClick:function(){return y((function(e){return e.includes(n)?e.filter((function(e){return e!==n})):[].concat(e,[n])}))}},r.createElement(Z,{expanded:d})," [",n*m," ..."," ",n*m+m-1,"]"),g.includes(n)?r.createElement(_,null,e.map((function(e){return r.createElement(t,{key:e.label,entry:e})}))):null))}))):null):r.createElement(r.Fragment,null,r.createElement(q,null,n,":")," ",r.createElement(F,null,function(e){var t=Object.getOwnPropertyNames(Object(e)),n="bigint"==typeof e?e.toString()+"n":e;return JSON.stringify(n,t)}(a))))};function J(e){var t,n=e.value,i=e.defaultExpanded,l=e.renderer,u=void 0===l?V:l,c=e.pageSize,s=void 0===c?100:c,d=o(e,["value","defaultExpanded","renderer","pageSize"]),f=r.useState(Boolean(i)),m=f[0],p=f[1],g=r.useCallback((function(){return p((function(e){return!e}))}),[]),y=typeof n,v=[],b=function(e){var t;return a({},e,{defaultExpanded:!0===i?((t={})[e.label]=!0,t):null==i?void 0:i[e.label]})};Array.isArray(n)?(y="array",v=n.map((function(e,t){return b({label:t.toString(),value:e})}))):null!==n&&"object"==typeof n&&(t=n,Symbol.iterator in t)&&"function"==typeof n[Symbol.iterator]?(y="Iterable",v=Array.from(n,(function(e,t){return b({label:t.toString(),value:e})}))):"object"==typeof n&&null!==n&&(y="object",v=Object.entries(n).map((function(e){var t=e[0],n=e[1];return b({label:t,value:n})})));var h=function(e,t){if(t<1)return[];for(var n=0,r=[];n<e.length;)r.push(e.slice(n,n+t)),n+=t;return r}(v,s);return u(a({HandleEntry:function(e){var t=e.entry;return r.createElement(J,a({value:n,renderer:u},d,t))},type:y,subEntries:v,subEntryPages:h,value:n,expanded:m,toggleExpanded:g,pageSize:s},d))}function K(e){return t.createElement("svg",a({width:"40px",height:"40px",viewBox:"0 0 190 190",version:"1.1"},e),t.createElement("g",{stroke:"none",strokeWidth:"1",fill:"none",fillRule:"evenodd"},t.createElement("g",{transform:"translate(-33.000000, 0.000000)"},t.createElement("path",{d:"M72.7239712,61.3436237 C69.631224,46.362877 68.9675112,34.8727722 70.9666331,26.5293551 C72.1555965,21.5671678 74.3293088,17.5190846 77.6346064,14.5984631 C81.1241394,11.5150478 85.5360327,10.0020122 90.493257,10.0020122 C98.6712013,10.0020122 107.26826,13.7273214 116.455725,20.8044264 C120.20312,23.6910458 124.092437,27.170411 128.131651,31.2444746 C128.45314,30.8310265 128.816542,30.4410453 129.22143,30.0806152 C140.64098,19.9149716 150.255245,13.5989272 158.478408,11.1636507 C163.367899,9.715636 167.958526,9.57768202 172.138936,10.983031 C176.551631,12.4664684 180.06766,15.5329489 182.548314,19.8281091 C186.642288,26.9166735 187.721918,36.2310983 186.195595,47.7320243 C185.573451,52.4199112 184.50985,57.5263831 183.007094,63.0593153 C183.574045,63.1277086 184.142416,63.2532808 184.705041,63.4395297 C199.193932,68.2358678 209.453582,73.3937462 215.665021,79.2882839 C219.360669,82.7953831 221.773972,86.6998434 222.646365,91.0218204 C223.567176,95.5836746 222.669313,100.159332 220.191548,104.451297 C216.105211,111.529614 208.591643,117.11221 197.887587,121.534031 C193.589552,123.309539 188.726579,124.917559 183.293259,126.363748 C183.541176,126.92292 183.733521,127.516759 183.862138,128.139758 C186.954886,143.120505 187.618598,154.61061 185.619477,162.954027 C184.430513,167.916214 182.256801,171.964297 178.951503,174.884919 C175.46197,177.968334 171.050077,179.48137 166.092853,179.48137 C157.914908,179.48137 149.31785,175.756061 140.130385,168.678956 C136.343104,165.761613 132.410866,162.238839 128.325434,158.108619 C127.905075,158.765474 127.388968,159.376011 126.77857,159.919385 C115.35902,170.085028 105.744755,176.401073 97.5215915,178.836349 C92.6321009,180.284364 88.0414736,180.422318 83.8610636,179.016969 C79.4483686,177.533532 75.9323404,174.467051 73.4516862,170.171891 C69.3577116,163.083327 68.2780823,153.768902 69.8044053,142.267976 C70.449038,137.410634 71.56762,132.103898 73.1575891,126.339009 C72.5361041,126.276104 71.9120754,126.144816 71.2949591,125.940529 C56.8060684,121.144191 46.5464184,115.986312 40.3349789,110.091775 C36.6393312,106.584675 34.2260275,102.680215 33.3536352,98.3582381 C32.4328237,93.7963839 33.3306866,89.2207269 35.8084524,84.9287618 C39.8947886,77.8504443 47.4083565,72.2678481 58.1124133,67.8460273 C62.5385143,66.0176154 67.5637208,64.366822 73.1939394,62.8874674 C72.9933393,62.3969171 72.8349374,61.8811235 72.7239712,61.3436237 Z",fill:"#002C4B",fillRule:"nonzero",transform:"translate(128.000000, 95.000000) scale(-1, 1) translate(-128.000000, -95.000000) "}),t.createElement("path",{d:"M113.396882,64 L142.608177,64 C144.399254,64 146.053521,64.958025 146.944933,66.5115174 L161.577138,92.0115174 C162.461464,93.5526583 162.461464,95.4473417 161.577138,96.9884826 L146.944933,122.488483 C146.053521,124.041975 144.399254,125 142.608177,125 L113.396882,125 C111.605806,125 109.951539,124.041975 109.060126,122.488483 L94.4279211,96.9884826 C93.543596,95.4473417 93.543596,93.5526583 94.4279211,92.0115174 L109.060126,66.5115174 C109.951539,64.958025 111.605806,64 113.396882,64 Z M138.987827,70.2765273 C140.779849,70.2765273 142.434839,71.2355558 143.325899,72.7903404 L154.343038,92.0138131 C155.225607,93.5537825 155.225607,95.4462175 154.343038,96.9861869 L143.325899,116.20966 C142.434839,117.764444 140.779849,118.723473 138.987827,118.723473 L117.017233,118.723473 C115.225211,118.723473 113.570221,117.764444 112.67916,116.20966 L101.662022,96.9861869 C100.779452,95.4462175 100.779452,93.5537825 101.662022,92.0138131 L112.67916,72.7903404 C113.570221,71.2355558 115.225211,70.2765273 117.017233,70.2765273 L138.987827,70.2765273 Z M135.080648,77.1414791 L120.924411,77.1414791 C119.134228,77.1414791 117.480644,78.0985567 116.5889,79.6508285 L116.5889,79.6508285 L109.489217,92.0093494 C108.603232,93.5515958 108.603232,95.4484042 109.489217,96.9906506 L109.489217,96.9906506 L116.5889,109.349172 C117.480644,110.901443 119.134228,111.858521 120.924411,111.858521 L120.924411,111.858521 L135.080648,111.858521 C136.870831,111.858521 138.524416,110.901443 139.41616,109.349172 L139.41616,109.349172 L146.515843,96.9906506 C147.401828,95.4484042 147.401828,93.5515958 146.515843,92.0093494 L146.515843,92.0093494 L139.41616,79.6508285 C138.524416,78.0985567 136.870831,77.1414791 135.080648,77.1414791 L135.080648,77.1414791 Z M131.319186,83.7122186 C133.108028,83.7122186 134.760587,84.6678753 135.652827,86.2183156 L138.983552,92.0060969 C139.87203,93.5500005 139.87203,95.4499995 138.983552,96.9939031 L135.652827,102.781684 C134.760587,104.332125 133.108028,105.287781 131.319186,105.287781 L124.685874,105.287781 C122.897032,105.287781 121.244473,104.332125 120.352233,102.781684 L117.021508,96.9939031 C116.13303,95.4499995 116.13303,93.5500005 117.021508,92.0060969 L120.352233,86.2183156 C121.244473,84.6678753 122.897032,83.7122186 124.685874,83.7122186 L131.319186,83.7122186 Z M128.003794,90.1848875 C126.459294,90.1848875 125.034382,91.0072828 124.263005,92.3424437 C123.491732,93.6774232 123.491732,95.3225768 124.263005,96.6575563 C125.034382,97.9927172 126.459294,98.8151125 128.001266,98.8151125 L128.001266,98.8151125 C129.545766,98.8151125 130.970678,97.9927172 131.742055,96.6575563 C132.513327,95.3225768 132.513327,93.6774232 131.742055,92.3424437 C130.970678,91.0072828 129.545766,90.1848875 128.003794,90.1848875 L128.003794,90.1848875 Z M93,94.5009646 L100.767764,94.5009646",fill:"#FFD94C"}),t.createElement("path",{d:"M87.8601729,108.357758 C89.1715224,107.608286 90.8360246,108.074601 91.5779424,109.399303 L91.5779424,109.399303 L92.0525843,110.24352 C95.8563392,116.982993 99.8190116,123.380176 103.940602,129.435068 C108.807881,136.585427 114.28184,143.82411 120.362479,151.151115 C121.316878,152.30114 121.184944,154.011176 120.065686,154.997937 L120.065686,154.997937 L119.454208,155.534625 C99.3465389,173.103314 86.2778188,176.612552 80.2480482,166.062341 C74.3500652,155.742717 76.4844915,136.982888 86.6513274,109.782853 C86.876818,109.179582 87.3045861,108.675291 87.8601729,108.357758 Z M173.534177,129.041504 C174.986131,128.785177 176.375496,129.742138 176.65963,131.194242 L176.65963,131.194242 L176.812815,131.986376 C181.782365,157.995459 178.283348,171 166.315764,171 C154.609745,171 139.708724,159.909007 121.612702,137.727022 C121.211349,137.235047 120.994572,136.617371 121,135.981509 C121.013158,134.480686 122.235785,133.274651 123.730918,133.287756 L123.730918,133.287756 L124.684654,133.294531 C132.305698,133.335994 139.714387,133.071591 146.910723,132.501323 C155.409039,131.82788 164.283523,130.674607 173.534177,129.041504 Z M180.408726,73.8119663 C180.932139,72.4026903 182.508386,71.6634537 183.954581,72.149012 L183.954581,72.149012 L184.742552,72.4154854 C210.583763,81.217922 220.402356,90.8916805 214.198332,101.436761 C208.129904,111.751366 190.484347,119.260339 161.26166,123.963678 C160.613529,124.067994 159.948643,123.945969 159.382735,123.618843 C158.047025,122.846729 157.602046,121.158214 158.388848,119.847438 L158.388848,119.847438 L158.889328,119.0105 C162.877183,112.31633 166.481358,105.654262 169.701854,99.0242957 C173.50501,91.1948179 177.073967,82.7907081 180.408726,73.8119663 Z M94.7383398,66.0363218 C95.3864708,65.9320063 96.0513565,66.0540315 96.6172646,66.3811573 C97.9529754,67.153271 98.3979538,68.8417862 97.6111517,70.1525615 L97.6111517,70.1525615 L97.1106718,70.9895001 C93.1228168,77.6836699 89.5186416,84.3457379 86.2981462,90.9757043 C82.49499,98.8051821 78.9260328,107.209292 75.5912744,116.188034 C75.0678608,117.59731 73.4916142,118.336546 72.045419,117.850988 L72.045419,117.850988 L71.2574475,117.584515 C45.4162372,108.782078 35.597644,99.1083195 41.8016679,88.5632391 C47.8700957,78.2486335 65.515653,70.7396611 94.7383398,66.0363218 Z M136.545792,34.4653746 C156.653461,16.8966864 169.722181,13.3874478 175.751952,23.9376587 C181.649935,34.2572826 179.515508,53.0171122 169.348673,80.2171474 C169.123182,80.8204179 168.695414,81.324709 168.139827,81.6422422 C166.828478,82.3917144 165.163975,81.9253986 164.422058,80.6006966 L164.422058,80.6006966 L163.947416,79.7564798 C160.143661,73.0170065 156.180988,66.6198239 152.059398,60.564932 C147.192119,53.4145727 141.71816,46.1758903 135.637521,38.8488847 C134.683122,37.6988602 134.815056,35.9888243 135.934314,35.0020629 L135.934314,35.0020629 Z M90.6842361,18 C102.390255,18 117.291276,29.0909926 135.387298,51.2729777 C135.788651,51.7649527 136.005428,52.3826288 136,53.0184911 C135.986842,54.5193144 134.764215,55.7253489 133.269082,55.7122445 L133.269082,55.7122445 L132.315346,55.7054689 C124.694302,55.6640063 117.285613,55.9284091 110.089277,56.4986773 C101.590961,57.17212 92.7164767,58.325393 83.4658235,59.9584962 C82.0138691,60.2148231 80.6245044,59.2578618 80.3403697,57.805758 L80.3403697,57.805758 L80.1871846,57.0136235 C75.2176347,31.0045412 78.7166519,18 90.6842361,18 Z",fill:"#FF4154"}))))}function X(){}var G="undefined"==typeof window;var $=function(e){return e.state.isFetching?0:e.getObserversCount()?e.isStale()?2:1:3},ee={"Status > Last Updated":function(e,t){var n;return $(e)===$(t)?null==(n=ee["Last Updated"])?void 0:n.call(ee,e,t):$(e)>$(t)?1:-1},"Query Hash":function(e,t){return e.queryHash>t.queryHash?1:-1},"Last Updated":function(e,t){return e.state.dataUpdatedAt<t.state.dataUpdatedAt?1:-1}},te=r.forwardRef((function(e,t){var i,l=e.isOpen,u=void 0===l||l,c=e.styleNonce,s=e.setIsOpen,d=e.handleDragStart,f=o(e,["isOpen","styleNonce","setIsOpen","handleDragStart"]),m=n.useQueryClient(),p=m.getQueryCache(),g=k("reactQueryDevtoolsSortFn",Object.keys(ee)[0]),y=g[0],b=g[1],h=k("reactQueryDevtoolsFilter",""),C=h[0],E=h[1],L=k("reactQueryDevtoolsSortDesc",!1),S=L[0],R=L[1],I=r.useMemo((function(){return ee[y]}),[y]);r[G?"useEffect":"useLayoutEffect"]((function(){I||b(Object.keys(ee)[0])}),[b,I]);var P=D(Object.values(p.findAll())),q=P[0],B=P[1],W=k("reactQueryDevtoolsActiveQueryHash",""),F=W[0],_=W[1],Y=r.useMemo((function(){var e=[].concat(q).sort(I);return S&&e.reverse(),C?v(e,C,{keys:["queryHash"]}).filter((function(e){return e.queryHash})):e}),[S,I,q,C]),Z=r.useMemo((function(){return Y.find((function(e){return e.queryHash===F}))}),[F,Y]),V=Y.filter((function(e){return"fresh"===A(e)})).length,$=Y.filter((function(e){return"fetching"===A(e)})).length,te=Y.filter((function(e){return"stale"===A(e)})).length,ne=Y.filter((function(e){return"inactive"===A(e)})).length;r.useEffect((function(){if(u){var e=p.subscribe((function(){B(Object.values(p.getAll()))}));return B(Object.values(p.getAll())),e}}),[u,y,I,S,B,p]);return r.createElement(w,{theme:x},r.createElement(T,a({ref:t,className:"ReactQueryDevtoolsPanel","aria-label":"React Query Devtools Panel",id:"ReactQueryDevtoolsPanel"},f),r.createElement("style",{nonce:c,dangerouslySetInnerHTML:{__html:"\n            .ReactQueryDevtoolsPanel * {\n              scrollbar-color: "+x.backgroundAlt+" "+x.gray+";\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar, .ReactQueryDevtoolsPanel scrollbar {\n              width: 1em;\n              height: 1em;\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-track, .ReactQueryDevtoolsPanel scrollbar-track {\n              background: "+x.backgroundAlt+";\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-thumb, .ReactQueryDevtoolsPanel scrollbar-thumb {\n              background: "+x.gray+";\n              border-radius: .5em;\n              border: 3px solid "+x.backgroundAlt+";\n            }\n          "}}),r.createElement("div",{style:{position:"absolute",left:0,top:0,width:"100%",height:"4px",marginBottom:"-4px",cursor:"row-resize",zIndex:1e5},onMouseDown:d}),r.createElement("div",{style:{flex:"1 1 500px",minHeight:"40%",maxHeight:"100%",overflow:"auto",borderRight:"1px solid "+x.grayAlt,display:u?"flex":"none",flexDirection:"column"}},r.createElement("div",{style:{padding:".5em",background:x.backgroundAlt,display:"flex",justifyContent:"space-between",alignItems:"center"}},r.createElement("button",{type:"button","aria-label":"Close React Query Devtools","aria-controls":"ReactQueryDevtoolsPanel","aria-haspopup":"true","aria-expanded":"true",onClick:function(){return s(!1)},style:{display:"inline-flex",background:"none",border:0,padding:0,marginRight:".5em",cursor:"pointer"}},r.createElement(K,{"aria-hidden":!0})),r.createElement("div",{style:{display:"flex",flexDirection:"column"}},r.createElement(M,{style:{marginBottom:".5em"}},r.createElement(U,{style:{background:x.success,opacity:V?1:.3}},"fresh ",r.createElement(z,null,"(",V,")"))," ",r.createElement(U,{style:{background:x.active,opacity:$?1:.3}},"fetching ",r.createElement(z,null,"(",$,")"))," ",r.createElement(U,{style:{background:x.warning,color:"black",textShadow:"0",opacity:te?1:.3}},"stale ",r.createElement(z,null,"(",te,")"))," ",r.createElement(U,{style:{background:x.gray,opacity:ne?1:.3}},"inactive ",r.createElement(z,null,"(",ne,")"))),r.createElement("div",{style:{display:"flex",alignItems:"center"}},r.createElement(N,{placeholder:"Filter","aria-label":"Filter by queryhash",value:null!=C?C:"",onChange:function(e){return E(e.target.value)},onKeyDown:function(e){"Escape"===e.key&&E("")},style:{flex:"1",marginRight:".5em",width:"100%"}}),C?null:r.createElement(r.Fragment,null,r.createElement(j,{"aria-label":"Sort queries",value:y,onChange:function(e){return b(e.target.value)},style:{flex:"1",minWidth:75,marginRight:".5em"}},Object.keys(ee).map((function(e){return r.createElement("option",{key:e,value:e},"Sort by ",e)}))),r.createElement(Q,{type:"button",onClick:function(){return R((function(e){return!e}))},style:{padding:".3em .4em"}},S?"⬇ Desc":"⬆ Asc"))))),r.createElement("div",{style:{overflowY:"auto",flex:"1"}},Y.map((function(e,t){var n=e.getObserversCount()>0&&!e.isActive();return r.createElement("div",{key:e.queryHash||t,role:"button","aria-label":"Open query details for "+e.queryHash,onClick:function(){return _(F===e.queryHash?"":e.queryHash)},style:{display:"flex",borderBottom:"solid 1px "+x.grayAlt,cursor:"pointer",background:e===Z?"rgba(255,255,255,.1)":void 0}},r.createElement("div",{style:{flex:"0 0 auto",width:"2em",height:"2em",background:O(e,x),display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",textShadow:"stale"===A(e)?"0":"0 0 10px black",color:"stale"===A(e)?"black":"white"}},e.getObserversCount()),n?r.createElement("div",{style:{flex:"0 0 auto",height:"2em",background:x.gray,display:"flex",alignItems:"center",fontWeight:"bold",padding:"0 0.5em"}},"disabled"):null,r.createElement(z,{style:{padding:".5em"}},""+e.queryHash))})))),Z?r.createElement(H,null,r.createElement("div",{style:{padding:".5em",background:x.backgroundAlt,position:"sticky",top:0,zIndex:1}},"Query Details"),r.createElement("div",{style:{padding:".5em"}},r.createElement("div",{style:{marginBottom:".5em",display:"flex",alignItems:"start",justifyContent:"space-between"}},r.createElement(z,{style:{lineHeight:"1.8em"}},r.createElement("pre",{style:{margin:0,padding:0,overflow:"auto"}},JSON.stringify(Z.queryKey,null,2))),r.createElement("span",{style:{padding:"0.3em .6em",borderRadius:"0.4em",fontWeight:"bold",textShadow:"0 2px 10px black",background:O(Z,x),flexShrink:0}},A(Z))),r.createElement("div",{style:{marginBottom:".5em",display:"flex",alignItems:"center",justifyContent:"space-between"}},"Observers: ",r.createElement(z,null,Z.getObserversCount())),r.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"}},"Last Updated:"," ",r.createElement(z,null,new Date(Z.state.dataUpdatedAt).toLocaleTimeString()))),r.createElement("div",{style:{background:x.backgroundAlt,padding:".5em",position:"sticky",top:0,zIndex:1}},"Actions"),r.createElement("div",{style:{padding:"0.5em"}},r.createElement(Q,{type:"button",onClick:function(){var e=null==Z?void 0:Z.fetch();null==e||e.catch(X)},disabled:Z.state.isFetching,style:{background:x.active}},"Refetch")," ",r.createElement(Q,{type:"button",onClick:function(){return m.invalidateQueries(Z)},style:{background:x.warning,color:x.inputTextColor}},"Invalidate")," ",r.createElement(Q,{type:"button",onClick:function(){return m.resetQueries(Z)},style:{background:x.gray}},"Reset")," ",r.createElement(Q,{type:"button",onClick:function(){return m.removeQueries(Z)},style:{background:x.danger}},"Remove")),r.createElement("div",{style:{background:x.backgroundAlt,padding:".5em",position:"sticky",top:0,zIndex:1}},"Data Explorer"),r.createElement("div",{style:{padding:".5em"}},r.createElement(J,{label:"Data",value:null==Z||null==(i=Z.state)?void 0:i.data,defaultExpanded:{}})),r.createElement("div",{style:{background:x.backgroundAlt,padding:".5em",position:"sticky",top:0,zIndex:1}},"Query Explorer"),r.createElement("div",{style:{padding:".5em"}},r.createElement(J,{label:"Query",value:Z,defaultExpanded:{queryKey:!0}}))):null))}));e.ReactQueryDevtools=function(e){var t=e.initialIsOpen,n=e.panelProps,i=void 0===n?{}:n,l=e.closeButtonProps,u=void 0===l?{}:l,c=e.toggleButtonProps,s=void 0===c?{}:c,d=e.position,f=void 0===d?"bottom-left":d,m=e.containerElement,p=void 0===m?"aside":m,g=e.styleNonce,y=r.useRef(null),v=r.useRef(null),b=k("reactQueryDevtoolsOpen",t),h=b[0],C=b[1],E=k("reactQueryDevtoolsHeight",null),L=E[0],S=E[1],O=D(!1),A=O[0],R=O[1],T=D(!1),H=T[0],M=T[1],U=I();r.useEffect((function(){R(null!=h&&h)}),[h,A,R]),r.useEffect((function(){var e=v.current;if(e){var t=function(){e&&A&&(e.style.visibility="visible")},n=function(){e&&!A&&(e.style.visibility="hidden")};return e.addEventListener("transitionstart",t),e.addEventListener("transitionend",n),function(){e.removeEventListener("transitionstart",t),e.removeEventListener("transitionend",n)}}}),[A]),r[G?"useEffect":"useLayoutEffect"]((function(){if(A){var e,t,n=null==(e=y.current)||null==(t=e.parentElement)?void 0:t.style.paddingBottom,r=function(){var e,t,n=null==(e=v.current)?void 0:e.getBoundingClientRect().height;(null==(t=y.current)?void 0:t.parentElement)&&(y.current.parentElement.style.paddingBottom=n+"px")};if(r(),"undefined"!=typeof window)return window.addEventListener("resize",r),function(){var e;window.removeEventListener("resize",r),(null==(e=y.current)?void 0:e.parentElement)&&"string"==typeof n&&(y.current.parentElement.style.paddingBottom=n)}}}),[A]);var z=i.style,N=void 0===z?{}:z,j=o(i,["style"]),P=u.style,q=void 0===P?{}:P,B=u.onClick,W=o(u,["style","onClick"]),F=s.style,_=void 0===F?{}:F,Y=s.onClick,Z=o(s,["style","onClick"]);return U()?r.createElement(p,{ref:y,className:"ReactQueryDevtools","aria-label":"React Query Devtools"},r.createElement(w,{theme:x},r.createElement(te,a({ref:v,styleNonce:g},j,{style:a({position:"fixed",bottom:"0",right:"0",zIndex:99999,width:"100%",height:null!=L?L:500,maxHeight:"90%",boxShadow:"0 0 20px rgba(0,0,0,.3)",borderTop:"1px solid "+x.gray,transformOrigin:"top",visibility:h?"visible":"hidden"},N,H?{transition:"none"}:{transition:"all .2s ease"},A?{opacity:1,pointerEvents:"all",transform:"translateY(0) scale(1)"}:{opacity:0,pointerEvents:"none",transform:"translateY(15px) scale(1.02)"}),isOpen:A,setIsOpen:C,handleDragStart:function(e){return function(e,t){var n;if(0===t.button){M(!0);var r={originalHeight:null!=(n=null==e?void 0:e.getBoundingClientRect().height)?n:0,pageY:t.pageY},a=function(e){var t=r.pageY-e.pageY,n=(null==r?void 0:r.originalHeight)+t;S(n),C(!(n<70))};document.addEventListener("mousemove",a),document.addEventListener("mouseup",(function e(){M(!1),document.removeEventListener("mousemove",a),document.removeEventListener("mouseUp",e)}))}}(v.current,e)}})),A?r.createElement(Q,a({type:"button","aria-controls":"ReactQueryDevtoolsPanel","aria-haspopup":"true","aria-expanded":"true"},W,{onClick:function(e){C(!1),B&&B(e)},style:a({position:"fixed",zIndex:99999,margin:".5em",bottom:0},"top-right"===f?{right:"0"}:"top-left"===f?{left:"0"}:"bottom-right"===f?{right:"0"}:{left:"0"},q)}),"Close"):null),A?null:r.createElement("button",a({type:"button"},Z,{"aria-label":"Open React Query Devtools","aria-controls":"ReactQueryDevtoolsPanel","aria-haspopup":"true","aria-expanded":"false",onClick:function(e){C(!0),Y&&Y(e)},style:a({background:"none",border:0,padding:0,position:"fixed",zIndex:99999,display:"inline-flex",fontSize:"1.5em",margin:".5em",cursor:"pointer",width:"fit-content"},"top-right"===f?{top:"0",right:"0"}:"top-left"===f?{top:"0",left:"0"}:"bottom-right"===f?{bottom:"0",right:"0"}:{bottom:"0",left:"0"},_)}),r.createElement(K,{"aria-hidden":!0}))):null},e.ReactQueryDevtoolsPanel=te,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react-query-devtools.production.min.js.map
