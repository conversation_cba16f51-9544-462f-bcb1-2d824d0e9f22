{"name": "path-type", "version": "4.0.0", "description": "Check if a path is a file, directory, or symlink", "license": "MIT", "repository": "sindresorhus/path-type", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd-check"}, "files": ["index.js", "index.d.ts"], "keywords": ["path", "fs", "type", "is", "check", "directory", "dir", "file", "filepath", "symlink", "symbolic", "link", "stat", "stats", "filesystem"], "devDependencies": {"ava": "^1.3.1", "nyc": "^13.3.0", "tsd-check": "^0.3.0", "xo": "^0.24.0"}}